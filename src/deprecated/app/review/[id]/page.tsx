/**
 * Review Page - Redirect to Unified Experience
 * This page now redirects to the unified workflow experience
 */

'use client';

import { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

export default function ReviewPage() {
  const params = useParams();
  const router = useRouter();
  const reviewId = params.id as string;

  useEffect(() => {
    // Redirect to unified experience with review step
    if (reviewId) {
      const unifiedUrl = `/workflow/unified?step=review&reviewId=${reviewId}`;
      router.replace(unifiedUrl);
    } else {
      // Fallback to unified experience
      router.replace('/workflow/unified');
    }
  }, [reviewId, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to unified workflow experience...</p>
      </div>
    </div>
  );
}
