# Deprecated Files

This folder contains files that were moved from the main application structure during codebase reorganization.

## Moved Files

### From src/app/
- `review/` - Review system that was not integrated with the unified workflow
- `pipeline/` - Standalone pipeline system
- `my-route/` - Test route
- `.well-known/` - Agent discovery endpoint

### From src/components/
- `AgentProgress/` - Agent progress display components
- `Agents/` - Agent monitoring and configuration components
- `BulkOperations/` - Bulk workflow management components
- `ContentGeneration/` - Content generation UI components
- `DynamicCollaboration/` - Dynamic collaboration dashboard components
- `DynamicCollaborationV3/` - Version 3 collaboration components
- `EnhancedCollaboration/` - Enhanced collaboration visualizers
- `GoalBasedCollaboration/` - Goal-based collaboration components
- `IterativeCollaboration/` - Iterative collaboration components
- `Navigation/` - Navigation components
- `Research/` - Research form components
- `Review/` - Review interface components
- `UI/` - General UI components
- `Workflow/` (partial) - Unused workflow components

### From src/examples/
- `UIIntegrationExample.tsx` - UI integration examples

## Reason for Deprecation

These files were moved to keep only the actively used systems:
- `src/app/(payload)` - Main Payload CMS and agent collaboration APIs
- `src/app/workflow` - Unified workflow system
- `src/components/AI` - AI helper components for Payload CMS
- `src/components/Admin` - Admin components for Payload CMS
- `src/components/Workflow` - Core workflow components used by unified system

The deprecated files may be restored if needed in the future, but are not currently part of the active workflow system.

## Date Moved
2025-06-16
