/**
 * Dynamic Agent Collaboration Example
 * 
 * This example demonstrates how to use the new dynamic agent collaboration system
 * in a real workflow scenario.
 */

import { AgentCollaborationEngine } from '../core/agents/AgentCollaborationEngine';
import { DynamicWorkflowExecution } from '../components/Workflow/DynamicWorkflowExecution';
import { AgentCommunicationProtocol } from '../core/agents/AgentCommunicationProtocol';
import { SeoKeywordAgent } from '../core/agents/seo-keyword-agent';
import { MarketResearchAgent } from '../core/agents/market-research-agent';
import { ContentStrategyAgent } from '../core/agents/content-strategy-agent';

/**
 * Example: Content Creation Workflow with Dynamic Agent Collaboration
 */
export async function runContentCreationWorkflowExample() {
  console.log('🚀 Starting Dynamic Agent Collaboration Example');
  
  // 1. Initialize the collaboration engine
  const collaborationEngine = new AgentCollaborationEngine();
  
  // 2. Register available agents
  const seoAgent = new SeoKeywordAgent();
  const marketAgent = new MarketResearchAgent();
  const contentAgent = new ContentStrategyAgent();
  
  collaborationEngine.registerAgent(seoAgent);
  collaborationEngine.registerAgent(marketAgent);
  collaborationEngine.registerAgent(contentAgent);
  
  console.log('✅ Registered 3 agents for collaboration');
  
  // 3. Initialize dynamic workflow execution
  const dynamicExecution = new DynamicWorkflowExecution(collaborationEngine);
  
  // 4. Initialize communication protocol
  const communicationProtocol = new AgentCommunicationProtocol();
  
  // 5. Define workflow steps
  const workflowSteps = [
    {
      id: 'topic-input',
      name: 'Topic Input',
      type: 'topic-input'
    },
    {
      id: 'keyword-research',
      name: 'Keyword Research',
      type: 'keyword-research'
    },
    {
      id: 'content-creation',
      name: 'Content Creation',
      type: 'content-creation'
    },
    {
      id: 'seo-optimization',
      name: 'SEO Optimization',
      type: 'seo-optimization'
    }
  ];
  
  // 6. Define workflow inputs
  const workflowInputs = {
    topic: 'AI Startups in 2025: Trends and Opportunities',
    targetAudience: 'entrepreneurs and investors',
    primaryKeyword: 'AI startups 2025',
    contentType: 'blog post',
    industry: 'technology'
  };
  
  console.log(`📝 Processing topic: "${workflowInputs.topic}"`);
  
  // 7. Execute workflow with dynamic collaboration
  const results = [];
  
  for (const step of workflowSteps) {
    console.log(`\n🔄 Executing step: ${step.name}`);
    
    try {
      const result = await dynamicExecution.executeStepWithCollaboration(step, workflowInputs);
      
      console.log(`✅ Step completed with ${result.quality} quality`);
      
      if (result.collaboration) {
        console.log(`🤝 Collaboration involved ${result.collaboration.session.agents.length} agents`);
        console.log(`📊 Consensus confidence: ${Math.round(result.collaboration.consensus.confidence * 100)}%`);
        console.log(`🔄 Collaboration rounds: ${result.collaboration.rounds.length}`);
        
        // Log agent contributions
        for (const round of result.collaboration.rounds) {
          console.log(`   Round ${round.number}:`);
          for (const [agentId, input] of round.agentInputs) {
            console.log(`     - ${agentId}: ${Math.round(input.confidence * 100)}% confidence`);
          }
        }
      } else {
        console.log('ℹ️ No collaboration needed for this step');
      }
      
      results.push(result);
      
    } catch (error) {
      console.error(`❌ Step failed: ${step.name}`, error);
    }
  }
  
  // 8. Demonstrate agent communication
  console.log('\n💬 Demonstrating Agent Communication');
  
  try {
    // SEO agent asks market research agent for audience insights
    await communicationProtocol.sendMessage(
      'seo-keyword',
      'market-research',
      {
        type: 'question',
        content: {
          question: 'What are the key demographics for AI startup content?',
          context: workflowInputs
        },
        timestamp: new Date().toISOString(),
        requiresResponse: true
      },
      'seo-market-conversation'
    );
    
    console.log('✅ SEO agent sent question to Market Research agent');
    
    // Market research agent responds
    await communicationProtocol.sendMessage(
      'market-research',
      'seo-keyword',
      {
        type: 'suggestion',
        content: {
          response: 'Target demographics: 25-45 year old entrepreneurs, tech investors, startup founders',
          insights: ['Focus on B2B keywords', 'Emphasize ROI and growth metrics']
        },
        timestamp: new Date().toISOString(),
        requiresResponse: false
      },
      'seo-market-conversation'
    );
    
    console.log('✅ Market Research agent responded with insights');
    
  } catch (error) {
    console.error('❌ Communication failed:', error);
  }
  
  // 9. Demonstrate consensus building
  console.log('\n🤝 Demonstrating Consensus Building');
  
  try {
    const proposal = {
      id: 'content-structure-proposal',
      title: 'AI Startups Content Structure',
      description: 'Proposed structure for the AI startups article',
      proposedChanges: [
        { type: 'add-section' as const, content: 'Executive Summary' },
        { type: 'add-section' as const, content: 'Market Analysis' },
        { type: 'add-section' as const, content: 'Key Trends' },
        { type: 'add-section' as const, content: 'Investment Opportunities' },
        { type: 'add-section' as const, content: 'Conclusion' }
      ],
      reasoning: 'This structure provides comprehensive coverage while maintaining SEO optimization',
      confidence: 0.85
    };
    
    // Mock consensus responses for demonstration
    (communicationProtocol as any).collectConsensusResponses = async () => [
      { agentId: 'seo-keyword', response: { agreement: 0.9, feedback: 'Excellent SEO structure' } },
      { agentId: 'market-research', response: { agreement: 0.85, feedback: 'Aligns with market needs' } },
      { agentId: 'content-strategy', response: { agreement: 0.88, feedback: 'Strong content flow' } }
    ];
    
    const consensusResult = await communicationProtocol.requestConsensus(
      'content-coordinator',
      ['seo-keyword', 'market-research', 'content-strategy'],
      proposal,
      'content-structure-consensus'
    );
    
    console.log(`✅ Consensus ${consensusResult.consensusReached ? 'REACHED' : 'NOT REACHED'}`);
    console.log(`📊 Average agreement: ${Math.round(consensusResult.averageAgreement * 100)}%`);
    console.log(`💬 Final decision: ${consensusResult.finalDecision}`);
    
  } catch (error) {
    console.error('❌ Consensus building failed:', error);
  }
  
  // 10. Summary
  console.log('\n📋 Workflow Summary');
  console.log(`✅ Completed ${results.length} workflow steps`);
  console.log(`🤝 Collaboration sessions: ${results.filter(r => r.collaboration).length}`);
  console.log(`🏆 High quality results: ${results.filter(r => r.quality === 'high').length}`);
  console.log(`⚡ Standard executions: ${results.filter(r => r.quality === 'standard').length}`);
  
  return results;
}

/**
 * Example: Simple Agent Collaboration
 */
export async function runSimpleCollaborationExample() {
  console.log('\n🔬 Running Simple Collaboration Example');
  
  const collaborationEngine = new AgentCollaborationEngine();
  
  // Register agents
  collaborationEngine.registerAgent(new SeoKeywordAgent());
  collaborationEngine.registerAgent(new MarketResearchAgent());
  
  // Define collaboration task
  const task = {
    type: 'artifact-refinement' as const,
    stepId: 'example-step',
    stepType: 'content-creation',
    objective: 'Improve content through multi-agent collaboration'
  };
  
  // Define context
  const context = {
    initialArtifact: {
      id: 'example-artifact',
      type: 'content',
      content: 'Initial content about AI startups and their impact on the economy.',
      metadata: {}
    },
    stepContext: {
      topic: 'AI startups economic impact',
      targetAudience: 'business leaders'
    },
    workflowContext: {},
    qualityThreshold: 0.8
  };
  
  try {
    const result = await collaborationEngine.startCollaboration(
      task,
      ['seo-keyword', 'market-research'],
      context
    );
    
    console.log('✅ Collaboration completed successfully');
    console.log(`🎯 Consensus confidence: ${Math.round(result.consensus.confidence * 100)}%`);
    console.log(`🔄 Collaboration rounds: ${result.rounds.length}`);
    console.log(`📝 Final recommendations: ${result.consensus.finalRecommendations.length}`);
    
    return result;
    
  } catch (error) {
    console.error('❌ Collaboration failed:', error);
    throw error;
  }
}

// Export for use in other modules
export {
  AgentCollaborationEngine,
  DynamicWorkflowExecution,
  AgentCommunicationProtocol
};
