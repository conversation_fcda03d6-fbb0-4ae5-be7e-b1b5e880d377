'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  StepConnector,
  stepConnectorClasses,
  Button,
  Divider,
  Chip,
  Avatar,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Tooltip,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import CreateIcon from '@mui/icons-material/Create';
import RateReviewIcon from '@mui/icons-material/RateReview';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { IterativeCollaborationState } from '../../app/(payload)/api/agents/collaborative-iteration/types';

// Custom connector for the stepper
const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        'linear-gradient(95deg, #2196f3 0%, #3f51b5 50%, #009688 100%)',
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage:
        'linear-gradient(95deg, #2196f3 0%, #3f51b5 50%, #009688 100%)',
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor:
      theme.palette.mode === 'dark' ? theme.palette.grey[800] : '#eaeaf0',
    borderRadius: 1,
  },
}));

// Custom step icon
const ColorlibStepIconRoot = styled('div')<{
  ownerState: { completed?: boolean; active?: boolean };
}>(({ theme, ownerState }) => ({
  backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[700] : '#ccc',
  zIndex: 1,
  color: '#fff',
  width: 50,
  height: 50,
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  ...(ownerState.active && {
    backgroundImage:
      'linear-gradient(136deg, #2196f3 0%, #3f51b5 50%, #009688 100%)',
    boxShadow: '0 4px 10px 0 rgba(0,0,0,.25)',
  }),
  ...(ownerState.completed && {
    backgroundImage:
      'linear-gradient(136deg, #2196f3 0%, #3f51b5 50%, #009688 100%)',
  }),
}));

// Step icon component
function ColorlibStepIcon(props: {
  icon: React.ReactNode;
  active?: boolean;
  completed?: boolean;
}) {
  const { active, completed } = props;

  return (
    <ColorlibStepIconRoot ownerState={{ completed, active }}>
      {props.icon}
    </ColorlibStepIconRoot>
  );
}

// Define the workflow steps
const workflowSteps = [
  {
    label: 'Research Phase',
    description: 'Market research, SEO keyword analysis, and content strategy development',
    icon: <SearchIcon />,
    color: '#2196f3',
    subphases: [
      { id: 'marketResearch', label: 'Market Research', progressKey: 'marketResearchComplete' },
      { id: 'keywordResearch', label: 'Keyword Research', progressKey: 'keywordResearchComplete' },
      { id: 'contentStrategy', label: 'Content Strategy', progressKey: 'contentStrategyComplete' }
    ]
  },
  {
    label: 'Content Generation Phase',
    description: 'Creation of high-quality content based on research and strategy',
    icon: <CreateIcon />,
    color: '#009688',
    subphases: [
      { id: 'contentGeneration', label: 'Content Generation', progressKey: 'contentGenerationComplete' }
    ]
  },
  {
    label: 'Review & Optimization Phase',
    description: 'SEO optimization and final content refinement',
    icon: <RateReviewIcon />,
    color: '#ff9800',
    subphases: [
      { id: 'seoOptimization', label: 'SEO Optimization', progressKey: 'seoOptimizationComplete' }
    ]
  },
  {
    label: 'Finalization Phase',
    description: 'Final content preparation and delivery',
    icon: <CheckCircleIcon />,
    color: '#4caf50',
    subphases: [
      { id: 'finalization', label: 'Content Finalization', progressKey: 'finalizationComplete' }
    ]
  }
];

interface DetailedWorkflowVisualizerProps {
  sessionId: string;
  state: IterativeCollaborationState;
  onRefresh?: () => void;
  loading?: boolean;
}

/**
 * Enhanced component to visualize the workflow progress with detailed phase information
 */
const DetailedWorkflowVisualizer: React.FC<DetailedWorkflowVisualizerProps> = ({
  sessionId,
  state,
  onRefresh,
  loading = false
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  // Map API phase names to our step indices
  const phaseToStepIndex = {
    'initialization': 0,
    'research': 0,
    'content-generation': 1,
    'creation': 1,
    'review': 2,
    'finalization': 3,
    'completed': 4
  };

  // Update active step based on current phase
  useEffect(() => {
    if (!state) return;
    
    const currentPhase = state.currentPhase || 'initialization';
    const stepIndex = phaseToStepIndex[currentPhase as keyof typeof phaseToStepIndex] || 0;
    
    setActiveStep(stepIndex);
    
    // Determine completed steps
    const completed: number[] = [];
    
    if (state.workflowProgress) {
      if (state.workflowProgress.marketResearchComplete &&
          state.workflowProgress.keywordResearchComplete &&
          state.workflowProgress.contentStrategyComplete) {
        completed.push(0);
      }
      
      if (state.workflowProgress.contentGenerationComplete) {
        completed.push(1);
      }
      
      if (state.workflowProgress.seoOptimizationComplete) {
        completed.push(2);
      }
      
      if (state.currentPhase === 'completed') {
        completed.push(3);
      }
    }
    
    setCompletedSteps(completed);
  }, [state]);

  // Check if a subphase is complete
  const isSubphaseComplete = (progressKey: string): boolean => {
    if (!state || !state.workflowProgress) return false;
    return !!state.workflowProgress[progressKey as keyof typeof state.workflowProgress];
  };

  // Calculate overall progress percentage
  const calculateProgress = (): number => {
    if (!state || !state.workflowProgress) return 0;
    
    const totalSubphases = workflowSteps.reduce((total, step) => total + step.subphases.length, 0);
    let completedSubphases = 0;
    
    workflowSteps.forEach(step => {
      step.subphases.forEach(subphase => {
        if (isSubphaseComplete(subphase.progressKey)) {
          completedSubphases++;
        }
      });
    });
    
    return Math.round((completedSubphases / totalSubphases) * 100);
  };

  if (loading) {
    return (
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading workflow status...
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Workflow Progress
        </Typography>
        
        {onRefresh && (
          <IconButton onClick={onRefresh} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        )}
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
          <CircularProgress
            variant="determinate"
            value={calculateProgress()}
            size={60}
            thickness={4}
            color="primary"
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography variant="caption" component="div" color="text.secondary">
              {`${calculateProgress()}%`}
            </Typography>
          </Box>
        </Box>
        
        <Box>
          <Typography variant="subtitle1">
            Current Phase: {state?.currentPhase || 'Initialization'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Session ID: {sessionId}
          </Typography>
        </Box>
      </Box>
      
      <Divider sx={{ mb: 3 }} />
      
      <Stepper activeStep={activeStep} orientation="vertical" connector={<ColorlibConnector />}>
        {workflowSteps.map((step, index) => (
          <Step key={step.label} completed={completedSteps.includes(index)}>
            <StepLabel
              StepIconComponent={ColorlibStepIcon}
              StepIconProps={{
                icon: step.icon
              }}
            >
              <Typography variant="subtitle1">{step.label}</Typography>
            </StepLabel>
            
            <StepContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                {step.description}
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                {step.subphases.map(subphase => (
                  <Grid item xs={12} sm={6} md={4} key={subphase.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Typography variant="subtitle2">
                            {subphase.label}
                          </Typography>
                          
                          {isSubphaseComplete(subphase.progressKey) ? (
                            <Chip
                              label="Complete"
                              color="success"
                              size="small"
                              icon={<CheckCircleIcon />}
                            />
                          ) : activeStep === index ? (
                            <Chip
                              label="In Progress"
                              color="primary"
                              size="small"
                            />
                          ) : (
                            <Chip
                              label="Pending"
                              color="default"
                              size="small"
                            />
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
              
              {index === activeStep && (
                <Box sx={{ mb: 2 }}>
                  <Alert severity="info">
                    <Typography variant="body2">
                      {index === 0 && 'Agents are researching your topic, analyzing keywords, and developing a content strategy.'}
                      {index === 1 && 'Content is being generated based on the research and strategy.'}
                      {index === 2 && 'Content is being optimized for SEO and readability.'}
                      {index === 3 && 'Final content is being prepared for delivery.'}
                    </Typography>
                  </Alert>
                </Box>
              )}
            </StepContent>
          </Step>
        ))}
      </Stepper>
      
      {state?.currentPhase === 'completed' && (
        <Box sx={{ mt: 3, p: 2, bgcolor: '#e8f5e9', borderRadius: 1 }}>
          <Typography variant="subtitle1" color="success.main" gutterBottom>
            Workflow Completed Successfully
          </Typography>
          <Typography variant="body2">
            All phases have been completed. You can now view and download the final content.
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default DetailedWorkflowVisualizer;
