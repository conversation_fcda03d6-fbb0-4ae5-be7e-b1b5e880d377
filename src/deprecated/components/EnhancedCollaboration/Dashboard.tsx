// src/components/EnhancedCollaboration/Dashboard.tsx

import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface CollaborationState {
  id: string;
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  progress: number;
  currentGoal: string;
  status: 'active' | 'paused' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  duration: string;
  messages: any[];
  artifacts: any[];
  decisions: any[];
  goals: any[];
  finalOutput?: {
    title: string;
    content: string;
    seoScore?: number;
    marketResearch?: {
      targetAudience?: string;
      audienceNeeds?: string[];
      marketTrends?: string[];
      [key: string]: any;
    };
    keywords?: {
      primaryKeyword?: string;
      secondaryKeywords?: string[];
      [key: string]: any;
    };
    outline?: any;
    generatedAt?: string;
    contributors?: string[];
    [key: string]: any;
  };
}

const EnhancedCollaborationDashboard: React.FC = () => {
  const [sessionId, setSessionId] = useState<string>('');
  const [collaborationState, setCollaborationState] = useState<CollaborationState | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [topic, setTopic] = useState<string>('');
  const [contentType, setContentType] = useState<'blog-article' | 'product-page' | 'buying-guide'>('blog-article');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [tone, setTone] = useState<string>('professional');
  const [keywords, setKeywords] = useState<string[]>([]);
  const [messageInput, setMessageInput] = useState<string>('');
  
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Add auto-refresh effect for active sessions
  useEffect(() => {
    let refreshTimer: NodeJS.Timeout | null = null;

    // Auto-refresh function to poll for updates every 5 seconds
    const refreshActiveSession = () => {
      if (sessionId && collaborationState?.status === 'active') {
        console.log('Auto-refreshing session:', sessionId);
        fetchCollaborationState(sessionId);
      }
    };

    // Set up timer if we have an active session
    if (sessionId && collaborationState?.status === 'active') {
      refreshTimer = setInterval(refreshActiveSession, 5000);
    }

    // Clean up on unmount
    return () => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  }, [sessionId, collaborationState?.status]);
  
  // Initial load effect
  useEffect(() => {
    // Check if there's a session ID in the URL
    const id = searchParams.get('id');
    if (id) {
      setSessionId(id);
      fetchCollaborationState(id);
    }
  }, [searchParams]);
  
  // Fetch the current state of a collaboration session
  const fetchCollaborationState = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      console.log(`Fetching collaboration state for session: ${id}`);
      
      // Use the new orchestrated-collaboration endpoint to prevent loops
      const response = await fetch(`/api/orchestrated-collaboration?sessionId=${id}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch collaboration state: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Received data from API:', {
        success: data.success,
        hasState: !!data.state,
        hasFinalOutput: !!data.finalOutput || !!data.state?.finalOutput,
        contentLength: data.finalOutput?.content?.length || data.state?.finalOutput?.content?.length || 0,
        contentPreview: data.finalOutput?.content ? 
          data.finalOutput.content.substring(0, 100) + '...' : 
          data.state?.finalOutput?.content ? 
            data.state.finalOutput.content.substring(0, 100) + '...' : 'No content'
      });
      
      if (data.success && data.state) {
        // If finalOutput is at the top level, add it to the state object
        if (data.finalOutput && !data.state.finalOutput) {
          console.log('Found finalOutput at top level, adding to state');
          data.state.finalOutput = data.finalOutput;
        }
        
        // Log the state before setting it
        if (data.state.finalOutput) {
          console.log('Final output details:', {
            title: data.state.finalOutput.title,
            contentLength: data.state.finalOutput.content?.length || 0,
            seoScore: data.state.finalOutput.seoScore,
            hasMetadata: !!data.state.finalOutput.metadata
          });
        } else {
          console.warn('No finalOutput found in state or at top level');
          
          // Create a placeholder finalOutput if none exists
          data.state.finalOutput = {
            title: data.state.topic || 'Generated Content',
            content: `# ${data.state.topic || 'Generated Content'}\n\n## Content Generation in Progress\n\nOur AI agents are collaborating to create high-quality content for your topic.`,
            seoScore: 50,
            metadata: {
              wordCount: 50,
              readingTime: 1,
              keywords: data.state.keywords || [],
              generatedAt: new Date().toISOString(),
              generatedBy: 'AI Content Generation System'
            }
          };
        }
        
        setCollaborationState(data.state);
        console.log('State updated in component');
      } else {
        throw new Error(data.error || 'Failed to fetch collaboration state');
      }
    } catch (err) {
      setError((err as Error).message);
      console.error('Error fetching collaboration state:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Start a new collaboration session using the orchestrated workflow
  const startCollaboration = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate inputs
      if (!topic) {
        throw new Error('Topic is required');
      }
      
      if (!targetAudience) {
        throw new Error('Target audience is required');
      }
      
      if (keywords.length === 0) {
        throw new Error('At least one keyword is required');
      }
      
      // Create request payload
      const payload = {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords
      };
      
      // Show a message that the orchestrated flow is starting
      setCollaborationState({
        id: 'pending',
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        progress: 0,
        currentGoal: 'Starting orchestrated workflow...',
        status: 'active',
        startTime: new Date().toISOString(),
        duration: '0s',
        messages: [],
        artifacts: [],
        decisions: [],
        goals: []
      });
      
      // Send request to the orchestrated API endpoint
      const response = await fetch('/api/orchestrated-collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to start orchestrated collaboration: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.sessionId) {
        // Set the session ID
        setSessionId(data.sessionId);
        
        // Update URL with session ID
        router.push(`/admin/enhanced-collaboration?id=${data.sessionId}`);
        
        // Prepare state with finalOutput incorporated if available
        let updatedState = data.state || {};
        
        // If there's a finalOutput, incorporate it into the state
        if (data.finalOutput) {
          updatedState = {
            ...updatedState,
            finalOutput: data.finalOutput
          };
          
          // Show success message based on state
          if (data.status === 'completed') {
            setSuccessMessage(`Collaboration completed successfully! Final output is ready.`);
          } else {
            setSuccessMessage(`Collaboration started! Initial draft is ready. Check back for updates.`);
          }
        }
        
        // Set the state with the finalOutput incorporated
        setCollaborationState(updatedState);
        
        // Log state for debugging
        console.log('Updated collaboration state:', {
          id: updatedState.id,
          status: updatedState.status,
          hasFinalOutput: !!updatedState.finalOutput,
          contentLength: updatedState.finalOutput?.content?.length || 0
        });
        
        // Set active tab to Final Output if we have content
        if (updatedState.finalOutput?.content) {
          setActiveTab(4); // Final output tab
        }
      } else {
        throw new Error(data.error || 'Failed to start orchestrated collaboration');
      }
    } catch (err) {
      setError((err as Error).message);
      console.error('Error starting collaboration:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Send a message to the collaboration
  const sendMessage = async (message: any) => {
    try {
      setLoading(true);
      setError(null);
      
      // Send request to orchestrated API to prevent loops
      const response = await fetch('/api/orchestrated-collaboration/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          message
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.state) {
        setCollaborationState(data.state);
        setMessageInput('');
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (err) {
      setError((err as Error).message);
      console.error('Error sending message:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Send a message to an agent
  const sendMessageToAgent = (text: string) => {
    if (!text.trim()) return;
    
    const message = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      from: 'user',
      to: 'system',
      parts: [{ type: 'text', text }]
    };
    
    sendMessage(message);
  };
  
  // Initialize a specific goal
  const initializeGoal = async (title: string, description: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Send request to orchestrated API to prevent loops
      const response = await fetch('/api/orchestrated-collaboration/goal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          action: 'initializeGoal',
          goalData: {
            title,
            description
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to initialize goal: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.state) {
        setCollaborationState(data.state);
      } else {
        throw new Error(data.error || 'Failed to initialize goal');
      }
    } catch (err) {
      setError((err as Error).message);
      console.error('Error initializing goal:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Manually advance a goal
  const advanceGoal = async (goalId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Send request to orchestrated API to prevent loops
      const response = await fetch('/api/orchestrated-collaboration/goal', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          action: 'advanceGoal',
          goalId
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to advance goal: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.state) {
        setCollaborationState(data.state);
      } else {
        throw new Error(data.error || 'Failed to advance goal');
      }
    } catch (err) {
      setError((err as Error).message);
      console.error('Error advancing goal:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Update an artifact
  const updateArtifact = async (artifactId: string, field: string, value: any) => {
    try {
      setLoading(true);
      setError(null);
      
      // Send request to orchestrated API to prevent loops
      const response = await fetch('/api/orchestrated-collaboration/artifact', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          action: 'updateArtifact',
          artifactId,
          field,
          value
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update artifact: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.state) {
        setCollaborationState(data.state);
      } else {
        throw new Error(data.error || 'Failed to update artifact');
      }
    } catch (err) {
      setError((err as Error).message);
      console.error('Error updating artifact:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };
  
  // Render the appropriate tab content
  const renderTabContent = () => {
    if (!collaborationState) {
      return (
        <div className="empty-state">
          <p className="subtitle">No collaboration session active. Start a new session to begin.</p>
        </div>
      );
    }
    
    switch (activeTab) {
      case 0: // Overview
        return (
          <div className="overview-panel">
            <h2 className="panel-title">Collaboration Overview: {collaborationState.topic}</h2>
            
            <div className="progress-section">
              <div className="progress-circle" style={{
                background: `conic-gradient(#4a90e2 ${collaborationState.progress * 3.6}deg, #f0f0f0 ${collaborationState.progress * 3.6}deg)`
              }}>
                <span className="progress-text">{collaborationState.progress}%</span>
              </div>
              <div className="progress-details">
                <h3 className="progress-title">
                  {collaborationState.progress}% Complete
                </h3>
                <p className="status-text">
                  Status: {collaborationState.status.charAt(0).toUpperCase() + collaborationState.status.slice(1)}
                </p>
                <p className="duration-text">
            </div>
            <div className="detail-item">
              <span className="label">Content Type:</span>
              <span className="value">{collaborationState.contentType}</span>
            </div>
            <div className="detail-item">
              <span className="label">Target Audience:</span>
              <span className="value">{collaborationState.targetAudience}</span>
            </div>
            <div className="detail-item">
              <span className="label">Status:</span>
              <span className={`value status-${collaborationState.status}`}>{collaborationState.status}</span>
            </div>
            <div className="detail-item">
              <span className="label">Progress:</span>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${collaborationState.progress}%` }}
                ></div>
              </div>
              <span className="progress-text">{collaborationState.progress}%</span>
            </div>
            <div className="detail-item">
              <span className="label">Current Goal:</span>
              <span className="value">{collaborationState.currentGoal}</span>
            </div>
            <div className="detail-item">
              <span className="label">Started:</span>
              <span className="value">{new Date(collaborationState.startTime).toLocaleString()}</span>
            </div>
            {collaborationState.endTime && (
              <div className="detail-item">
                <span className="label">Completed:</span>
                <span className="value">{new Date(collaborationState.endTime).toLocaleString()}</span>
              </div>
            )}
            <div className="detail-item">
              <span className="label">Duration:</span>
              <span className="value">{collaborationState.duration}</span>
            </div>
          </div>
        </div>
      );
      
    case 1: // Messages
      return (
        <div className="messages-panel">
          <h3>Agent Messages</h3>
          <div className="message-list">
            {collaborationState.messages && collaborationState.messages.length > 0 ? (
              collaborationState.messages.map((message, index) => (
                <div key={index} className={`message-item ${message.sender}-message`}>
                  <div className="message-header">
                    <span className="sender">{message.sender}</span>
                    <span className="timestamp">{new Date(message.timestamp).toLocaleString()}</span>
                  </div>
                  <div className="message-content">
                    {message.content}
                  </div>
                  {message.type && (
                    <div className="message-type">
                      Type: {message.type}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <p>No messages yet.</p>
            )}
          </div>
          <div className="message-input-container">
                        <h4>Content Debug Info</h4>
                        <p><strong>Content Length:</strong> {collaborationState.finalOutput.content.length} characters</p>
                        <p><strong>Word Count:</strong> {collaborationState.finalOutput.content.split(/\s+/).length} words</p>
                        <p><strong>SEO Score:</strong> {collaborationState.finalOutput.seoScore || 'N/A'}</p>
                        <p><strong>Content Type:</strong> {typeof collaborationState.finalOutput.content}</p>
                        <p><strong>Has Metadata:</strong> {collaborationState.finalOutput.metadata ? 'Yes' : 'No'}</p>
                        <details>
                          <summary>View Raw Content (First 500 chars)</summary>
                          <pre style={{whiteSpace: 'pre-wrap', wordBreak: 'break-word'}}>
                            {collaborationState.finalOutput.content.substring(0, 500)}
                            {collaborationState.finalOutput.content.length > 500 ? '...' : ''}
                          </pre>
                        </details>
                      </div>
                    </>
                  ) : (
                    <div className="content-placeholder">
                      <h3>Content Generation in Progress</h3>
                      <p>Our AI agents are collaborating to create high-quality content for your topic.</p>
                      <p>This may take a few moments. The content will appear here once it's ready.</p>
                      <div className="content-debug" style={{marginTop: '20px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px'}}>
                        <h4>Content Debug Info</h4>
                        <p><strong>Content:</strong> {collaborationState.finalOutput.content ? 'Present' : 'Missing'}</p>
                        <p><strong>Content Type:</strong> {typeof collaborationState.finalOutput.content}</p>
                        <p><strong>Content Length:</strong> {typeof collaborationState.finalOutput.content === 'string' ? collaborationState.finalOutput.content.length : 0} characters</p>
                        <p><strong>Has Metadata:</strong> {collaborationState.finalOutput.metadata ? 'Yes' : 'No'}</p>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="content-metadata">
                  <div className="metadata-section">
                    <h4>Market Research Insights</h4>
                    {collaborationState.finalOutput.marketResearch ? (
                      <ul>
                        {collaborationState.finalOutput.marketResearch.targetAudience && (
                          <li><strong>Target Audience:</strong> {collaborationState.finalOutput.marketResearch.targetAudience}</li>
                        )}
                        {collaborationState.finalOutput.marketResearch.audienceNeeds && (
                          <li>
                            <strong>Audience Needs:</strong>
                            <ul>
                              {collaborationState.finalOutput.marketResearch.audienceNeeds.map((need: string, i: number) => (
                                <li key={i}>{need}</li>
                              ))}
                            </ul>
                          </li>
                        )}
                        {collaborationState.finalOutput.marketResearch.marketTrends && (
                          <li>
                            <strong>Market Trends:</strong>
                            <ul>
                              {collaborationState.finalOutput.marketResearch.marketTrends.map((trend: string, i: number) => (
                                <li key={i}>{trend}</li>
                              ))}
                            </ul>
                          </li>
                        )}
                      </ul>
                    ) : (
                      <p>No market research data available.</p>
                    )}
                  </div>
                  
                  <div className="metadata-section">
                    <h4>Keywords</h4>
                    {collaborationState.finalOutput.keywords ? (
                      <div>
                        {collaborationState.finalOutput.keywords.primaryKeyword && (
                          <div className="keyword-section">
                            <h5>Primary Keyword</h5>
                            <div className="primary-keyword">{collaborationState.finalOutput.keywords.primaryKeyword}</div>
                          </div>
                        )}
                        
                        {collaborationState.finalOutput.keywords.secondaryKeywords && 
                         collaborationState.finalOutput.keywords.secondaryKeywords.length > 0 && (
                          <div className="keyword-section">
                            <h5>Secondary Keywords</h5>
                            <div className="secondary-keywords">
                              {collaborationState.finalOutput.keywords.secondaryKeywords.map((kw: string, i: number) => (
                                <span key={i} className="keyword-pill">{kw}</span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <p>No keyword data available.</p>
                    )}
                  </div>
                  
                  <div className="metadata-section">
                    <h4>Content Contributors</h4>
                    {collaborationState.finalOutput.contributors && collaborationState.finalOutput.contributors.length > 0 ? (
                      <ul className="contributors-list">
                        {collaborationState.finalOutput.contributors.map((contributor: string, i: number) => (
                          <li key={i} className="contributor-item">{contributor}</li>
                        ))}
                      </ul>
                    ) : (
                      <p>No contributor data available.</p>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <p>No final output available yet. The collaboration may still be in progress.</p>
            )}
          </div>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="enhanced-collaboration-dashboard">
      <h2>Enhanced AI Collaboration</h2>
      
      {!sessionId ? (
        <div className="new-collaboration-form">
          <h3>Start New Collaboration</h3>
          
          <div className="form-container">
            <input
              type="text"
              placeholder="Topic"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              className="form-input"
            />
            
            <select
              value={contentType}
              onChange={(e) => setContentType(e.target.value as any)}
              className="form-select"
            >
              <option value="blog-article">Blog Article</option>
              <option value="product-page">Product Page</option>
              <option value="buying-guide">Buying Guide</option>
            </select>
            
            <input
              type="text"
              placeholder="Target Audience"
              value={targetAudience}
              onChange={(e) => setTargetAudience(e.target.value)}
              className="form-input"
            />
            
            <select
              value={tone}
              onChange={(e) => setTone(e.target.value)}
              className="form-select"
            >
              <option value="professional">Professional</option>
              <option value="conversational">Conversational</option>
              <option value="technical">Technical</option>
              <option value="friendly">Friendly</option>
            </select>
            
            <input
              type="text"
              placeholder="Keywords (comma separated)"
              value={keywords.join(', ')}
              onChange={(e) => setKeywords(e.target.value.split(',').map(k => k.trim()))}
              className="form-input"
            />
            
            <button 
              className="btn-primary"
              onClick={startCollaboration}
              disabled={loading}
            >
              {loading ? 'Starting...' : 'Start Collaboration'}
            </button>
            
            {error && (
              <div className="error-message">
                {error}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="collaboration-workspace">
          {sessionId && collaborationState && (
            <>
              <div className="tab-navigation">
                <button
                  className={`tab-button ${activeTab === 0 ? 'active' : ''}`}
                  onClick={() => setActiveTab(0)}
                >
                  Overview
                </button>
                <button
                  className={`tab-button ${activeTab === 1 ? 'active' : ''}`}
                  onClick={() => setActiveTab(1)}
                >
                  Messages
                </button>
                <button
                  className={`tab-button ${activeTab === 2 ? 'active' : ''}`}
                  onClick={() => setActiveTab(2)}
                >
                  Artifacts
                </button>
                <button
                  className={`tab-button ${activeTab === 3 ? 'active' : ''}`}
                  onClick={() => setActiveTab(3)}
                >
                  Reasoning
                </button>
                <button
                  className={`tab-button ${activeTab === 4 ? 'active' : ''}`}
                  onClick={() => setActiveTab(4)}
                >
                  Final Output
                </button>
              </div>
            </>
          )}
          
          <div className="tab-content">
            {loading && !collaborationState ? (
              <div className="loading-indicator">
                <div className="spinner"></div>
                <p>Loading collaboration data...</p>
              </div>
            ) : collaborationState?.finalOutput?.content && activeTab === 4 ? (
              <div className="content-panel">
                <div className="content-actions">
                  <button 
                    className="action-button refresh-button" 
                    onClick={() => fetchCollaborationState(sessionId)}
                    disabled={loading}
                  >
                    {loading ? 'Refreshing...' : 'Refresh Content'}
                  </button>
                  <button 
                    className="action-button publish-button"
                    onClick={() => alert('Publishing functionality to be implemented')}
                  >
                    Publish to CMS
                  </button>
                </div>
                {renderTabContent()}
              </div>
            ) : (
              renderTabContent()
            )}
          </div>
          {error && (
            <div className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50">
              {error}
            </div>
          )}
          {successMessage && (
            <div className="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50">
              {successMessage}
            </div>
          )}
        </div>
      )}
      
      <style jsx>{`
        .enhanced-collaboration-dashboard {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .markdown-content {
          background-color: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
          line-height: 1.6;
          font-size: 16px;
          overflow-wrap: break-word;
        }
        
        .markdown-content h1 {
          font-size: 28px;
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
          border-bottom: 1px solid #eaecef;
          padding-bottom: 8px;
        }
        
        .markdown-content h2 {
          font-size: 24px;
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
          border-bottom: 1px solid #eaecef;
          padding-bottom: 6px;
        }
        
        .markdown-content h3 {
          font-size: 20px;
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
        }
        
        .markdown-content p {
          margin-top: 0;
          margin-bottom: 16px;
        }
        
        .markdown-content ul, .markdown-content ol {
          padding-left: 2em;
          margin-top: 0;
          margin-bottom: 16px;
        }
        
        .markdown-content blockquote {
          padding: 0 1em;
          color: #6a737d;
          border-left: 0.25em solid #dfe2e5;
          margin: 0 0 16px 0;
        }
        
        .markdown-content pre {
          padding: 16px;
          overflow: auto;
          font-size: 85%;
          line-height: 1.45;
          background-color: #f6f8fa;
          border-radius: 3px;
          margin-top: 0;
          margin-bottom: 16px;
          word-wrap: normal;
        }
        
        .markdown-content code {
          padding: 0.2em 0.4em;
          margin: 0;
          font-size: 85%;
          background-color: rgba(27,31,35,0.05);
          border-radius: 3px;
          font-family: monospace;
        }
        
        .markdown-content img {
          max-width: 100%;
          box-sizing: content-box;
          background-color: #fff;
        }
        
        h2 {
          margin-bottom: 20px;
          color: #333;
        }
        
        .new-collaboration-form {
          background-color: #fff;
          border-radius: 5px;
          padding: 20px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          margin-bottom: 20px;
        }
        
        .form-container {
          display: flex;
          flex-direction: column;
          gap: 15px;
          max-width: 500px;
        }
        
        .form-input, .form-select {
          padding: 10px;
          font-size: 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        
        .btn-primary {
          background-color: #0070f3;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 10px 15px;
          font-size: 16px;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        
        .btn-primary:hover {
          background-color: #0060df;
        }
        
        .btn-primary:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
        
        .tab-navigation {
          display: flex;
          border-bottom: 1px solid #ddd;
          margin-bottom: 20px;
        }
        
        .tab-button {
          padding: 10px 20px;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 16px;
          border-bottom: 2px solid transparent;
        }
        
        .tab-button.active {
          border-bottom: 2px solid #0070f3;
          color: #0070f3;
        }
        
        .tab-content {
          background-color: #fff;
          border-radius: 5px;
          padding: 20px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          min-height: 400px;
        }
        
        .loading-indicator {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        .spinner {
          border: 4px solid rgba(0, 0, 0, 0.1);
          width: 36px;
          height: 36px;
          border-radius: 50%;
          border-left-color: #09f;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .content-body {
          margin-top: 20px;
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 20px;
        }
        
        .content-panel {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }
        
        .content-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-bottom: 10px;
        }
        
        .action-button {
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: none;
        }
        
        .refresh-button {
          background-color: #f0f0f0;
          color: #333;
        }
        
        .refresh-button:hover {
          background-color: #e0e0e0;
        }
        
        .publish-button {
          background-color: #4caf50;
          color: white;
        }
        
        .publish-button:hover {
          background-color: #3d8b40;
        }
        
        .confidence-bar {
          height: 6px;
          background-color: #eee;
          border-radius: 3px;
          overflow: hidden;
          margin-top: 5px;
        }
        
        .confidence-fill {
          height: 100%;
          background-color: #4caf50;
          transition: width 0.3s ease;
        }
        
        pre {
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto;
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  );
};

export default EnhancedCollaborationDashboard;
