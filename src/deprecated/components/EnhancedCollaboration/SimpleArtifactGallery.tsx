'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  CircularProgress
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import VisibilityIcon from '@mui/icons-material/Visibility';
import RefreshIcon from '@mui/icons-material/Refresh';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface SimpleArtifactGalleryProps {
  artifacts: any[] | Record<string, any>;
  refreshContent?: () => void;
  loading?: boolean;
}

/**
 * Simple artifact gallery component for displaying artifacts
 */
const SimpleArtifactGallery: React.FC<SimpleArtifactGalleryProps> = ({
  artifacts = [],
  refreshContent,
  loading = false
}) => {
  const [selectedArtifact, setSelectedArtifact] = useState<any | null>(null);

  // Convert artifacts to array if it's an object
  const artifactsArray = Array.isArray(artifacts)
    ? artifacts
    : Object.values(artifacts);

  // Get artifact type display name
  const getArtifactTypeDisplay = (type: string): string => {
    const typeMap: Record<string, string> = {
      'market-research-report': 'Market Research Report',
      'seo-keyword-analysis': 'SEO Keyword Analysis',
      'content-strategy-plan': 'Content Strategy Plan',
      'content-draft': 'Content Draft',
      'seo-optimization-report': 'SEO Optimization Report',
      'final-content': 'Final Content',
      'blog-post': 'Blog Post',
      'article': 'Article',
      // New artifact types from the workflow orchestrator
      'market-research': 'Market Research Report',
      'keyword-research': 'SEO Keyword Analysis',
      'content-strategy': 'Content Strategy Plan',
      'content-generation': 'Content Draft',
      'seo-optimization': 'SEO Optimization Report',
      'blog-content': 'Blog Content',
      'generated-content': 'Generated Content',
      'blog-post': 'Blog Post',
      'content-draft': 'Content Draft'
    };

    return typeMap[type] || type.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string): string => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  // Extract content from artifact
  const extractContent = (artifact: any): string => {
    if (!artifact) return 'No content available';

    // Check for direct content fields
    if (typeof artifact.content === 'string') {
      return artifact.content;
    }

    if (typeof artifact.text === 'string') {
      return artifact.text;
    }

    if (typeof artifact.body === 'string') {
      return artifact.body;
    }

    // Check for nested content
    if (artifact.content && typeof artifact.content === 'object') {
      // Handle new workflow orchestrator artifact structure
      if (typeof artifact.content.text === 'string') {
        return artifact.content.text;
      }

      if (typeof artifact.content.body === 'string') {
        return artifact.content.body;
      }

      // Handle content field that might be an array of sections
      if (Array.isArray(artifact.content.sections)) {
        return artifact.content.sections.map((section: any) => {
          if (typeof section === 'string') {
            return section;
          }
          if (section.title && section.content) {
            return `## ${section.title}\n\n${section.content}`;
          }
          return JSON.stringify(section, null, 2);
        }).join('\n\n');
      }

      // Try to stringify the content object
      try {
        return JSON.stringify(artifact.content, null, 2);
      } catch (e) {
        // Fallback
      }
    }

    // Handle data field which might contain the content
    if (artifact.data && typeof artifact.data === 'object') {
      if (typeof artifact.data.content === 'string') {
        return artifact.data.content;
      }

      if (typeof artifact.data.text === 'string') {
        return artifact.data.text;
      }

      try {
        return JSON.stringify(artifact.data, null, 2);
      } catch (e) {
        // Fallback
      }
    }

    // Try to stringify the entire artifact as a last resort
    try {
      return JSON.stringify(artifact, null, 2);
    } catch (e) {
      return 'Content could not be displayed';
    }
  };

  // Handle view artifact
  const handleViewArtifact = (artifact: any) => {
    setSelectedArtifact(artifact);
  };

  // Handle close artifact
  const handleCloseArtifact = () => {
    setSelectedArtifact(null);
  };

  // Group artifacts by type
  const groupedArtifacts: Record<string, any[]> = {};
  artifactsArray.forEach(artifact => {
    if (!artifact || !artifact.type) return;

    if (!groupedArtifacts[artifact.type]) {
      groupedArtifacts[artifact.type] = [];
    }
    groupedArtifacts[artifact.type].push(artifact);
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Content Artifacts ({artifactsArray.length})
        </Typography>

        {refreshContent && (
          <IconButton onClick={refreshContent} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        )}
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : artifactsArray.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No artifacts available yet. They will appear here as agents create them.
          </Typography>
        </Paper>
      ) : (
        <>
          {Object.entries(groupedArtifacts).map(([type, typeArtifacts]) => (
            <Box key={type} sx={{ mb: 4 }}>
              <Typography variant="subtitle1" gutterBottom>
                {getArtifactTypeDisplay(type)}
              </Typography>

              <Grid container spacing={2}>
                {typeArtifacts.map((artifact, index) => (
                  <Grid item xs={12} sm={6} md={4} key={artifact.id || index}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="subtitle1" noWrap>
                            {artifact.name || artifact.title || `Artifact ${index + 1}`}
                          </Typography>
                          <Chip
                            size="small"
                            label={getArtifactTypeDisplay(artifact.type)}
                            color="primary"
                          />
                        </Box>

                        <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                          Created: {formatTimestamp(artifact.createdAt || artifact.timestamp || artifact.created || '')}
                          {artifact.createdBy && ` by ${artifact.createdBy}`}
                        </Typography>

                        {artifact.qualityScore !== undefined && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, mb: 1 }}>
                            <Typography variant="body2" sx={{ mr: 1 }}>
                              Quality:
                            </Typography>
                            <Chip
                              label={`${Math.round(artifact.qualityScore * 100)}%`}
                              color={artifact.qualityScore > 0.8 ? "success" : artifact.qualityScore > 0.6 ? "primary" : "warning"}
                              size="small"
                            />
                          </Box>
                        )}

                        <Typography variant="body2" sx={{
                          mt: 1,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          height: '4.5em'
                        }}>
                          {artifact.description || 'No description available'}
                        </Typography>
                      </CardContent>
                      <CardActions>
                        <Button
                          size="small"
                          startIcon={<VisibilityIcon />}
                          onClick={() => handleViewArtifact(artifact)}
                        >
                          View
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </>
      )}

      {/* Artifact Detail Dialog */}
      <Dialog
        open={!!selectedArtifact}
        onClose={handleCloseArtifact}
        maxWidth="md"
        fullWidth
      >
        {selectedArtifact && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">
                  {selectedArtifact.name || selectedArtifact.title || 'Artifact Details'}
                </Typography>
                <IconButton onClick={handleCloseArtifact}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Box sx={{ mb: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Type:
                    </Typography>
                    <Typography variant="body1">
                      {getArtifactTypeDisplay(selectedArtifact.type)}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Created:
                    </Typography>
                    <Typography variant="body1">
                      {formatTimestamp(selectedArtifact.createdAt || selectedArtifact.timestamp || selectedArtifact.created || '')}
                    </Typography>
                  </Grid>
                  {selectedArtifact.createdBy && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        Created By:
                      </Typography>
                      <Typography variant="body1">
                        {selectedArtifact.createdBy}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>

              <Chip
                label="Content"
                color="primary"
                size="small"
                sx={{ mb: 1 }}
              />

              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  maxHeight: '400px',
                  overflow: 'auto',
                  bgcolor: 'background.default'
                }}
              >
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {extractContent(selectedArtifact)}
                </ReactMarkdown>
              </Paper>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseArtifact}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default SimpleArtifactGallery;
