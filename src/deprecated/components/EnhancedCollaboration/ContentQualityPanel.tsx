'use client'

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import VisibilityIcon from '@mui/icons-material/Visibility';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SchoolIcon from '@mui/icons-material/School';
import SpeedIcon from '@mui/icons-material/Speed';
import FormatQuoteIcon from '@mui/icons-material/FormatQuote';
import LinkIcon from '@mui/icons-material/Link';
import TextFieldsIcon from '@mui/icons-material/TextFields';

// Define the props interface
interface ContentQualityPanelProps {
  contentMetrics: {
    overallScore: number;
    readability: {
      fleschKincaidScore: number;
      smogIndex?: number;
      colemanLiauIndex?: number;
      automatedReadabilityIndex?: number;
      readingTimeMinutes: number;
      readabilityScore: number;
      suggestions?: string[];
    };
    structure: {
      headingCount: number;
      paragraphCount: number;
      listCount: number;
      structureScore: number;
      suggestions?: string[];
    };
    engagement: {
      engagementScore: number;
      questionCount?: number;
      callToActionCount?: number;
      suggestions?: string[];
    };
    coherence: {
      coherenceScore: number;
      topicConsistency?: number;
      suggestions?: string[];
    };
    content: {
      contentScore: number;
      wordCount: number;
      sentenceCount: number;
      averageSentenceLength: number;
      suggestions?: string[];
    };
    seo: {
      seoScore: number;
      keywordDensity?: Record<string, number>;
      suggestions?: string[];
    };
  };
}

/**
 * Component to display comprehensive content quality metrics
 */
const ContentQualityPanel: React.FC<ContentQualityPanelProps> = ({
  contentMetrics
}) => {
  const [expandedSection, setExpandedSection] = useState<string | false>('panel1');

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? panel : false);
  };

  // Helper function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  // Helper function to format score as percentage
  const formatScore = (score: number) => `${Math.round(score * 100)}%`;

  // Helper function to get readability level description
  const getReadabilityLevel = (score: number) => {
    if (score >= 90) return 'Very Easy - 5th Grade';
    if (score >= 80) return 'Easy - 6th Grade';
    if (score >= 70) return 'Fairly Easy - 7th Grade';
    if (score >= 60) return 'Standard - 8-9th Grade';
    if (score >= 50) return 'Fairly Difficult - 10-12th Grade';
    if (score >= 30) return 'Difficult - College';
    return 'Very Difficult - College Graduate';
  };

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        Content Quality Metrics
      </Typography>
      <Divider sx={{ mb: 3 }} />

      {/* Overall Score */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: `conic-gradient(${getScoreColor(contentMetrics.overallScore)} ${contentMetrics.overallScore * 360}deg, #e0e0e0 0)`,
              mr: 2
            }}
          >
            <Box
              sx={{
                width: 70,
                height: 70,
                borderRadius: '50%',
                bgcolor: 'background.paper',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {formatScore(contentMetrics.overallScore)}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="h6">Content Quality Score</Typography>
            <Typography variant="body2" color="text.secondary">
              Based on readability, structure, engagement, and coherence metrics
            </Typography>
          </Box>
        </Box>
        <Chip
          label={contentMetrics.overallScore >= 0.7 ? 'High Quality' : contentMetrics.overallScore >= 0.5 ? 'Average Quality' : 'Needs Improvement'}
          color={contentMetrics.overallScore >= 0.7 ? 'success' : contentMetrics.overallScore >= 0.5 ? 'warning' : 'error'}
          sx={{ fontWeight: 'bold' }}
        />
      </Box>

      {/* Key Metrics Summary */}
      <Typography variant="h6" gutterBottom>
        Key Metrics
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AccessTimeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  Reading Time
                </Typography>
              </Box>
              <Typography variant="h5">
                {contentMetrics.readability.readingTimeMinutes.toFixed(1)} min
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {contentMetrics.content.wordCount} words
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <SchoolIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  Readability
                </Typography>
              </Box>
              <Typography variant="h5">
                {contentMetrics.readability.fleschKincaidScore.toFixed(1)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {getReadabilityLevel(contentMetrics.readability.fleschKincaidScore)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <FormatListBulletedIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  Structure
                </Typography>
              </Box>
              <Typography variant="h5">
                {formatScore(contentMetrics.structure.structureScore)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {contentMetrics.structure.headingCount} headings, {contentMetrics.structure.listCount} lists
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TrendingUpIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="subtitle1">
                  Engagement
                </Typography>
              </Box>
              <Typography variant="h5">
                {formatScore(contentMetrics.engagement.engagementScore)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {contentMetrics.engagement.questionCount || 0} questions, {contentMetrics.engagement.callToActionCount || 0} CTAs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Metrics */}
      <Accordion expanded={expandedSection === 'panel1'} onChange={handleChange('panel1')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <VisibilityIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="subtitle1">Readability Metrics</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Metric</TableCell>
                  <TableCell align="right">Value</TableCell>
                  <TableCell align="right">Interpretation</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell component="th" scope="row">
                    Flesch-Kincaid Score
                  </TableCell>
                  <TableCell align="right">{contentMetrics.readability.fleschKincaidScore.toFixed(1)}</TableCell>
                  <TableCell align="right">
                    <Chip
                      size="small"
                      label={getReadabilityLevel(contentMetrics.readability.fleschKincaidScore)}
                      color={
                        contentMetrics.readability.fleschKincaidScore >= 60 && contentMetrics.readability.fleschKincaidScore <= 80
                          ? 'success'
                          : contentMetrics.readability.fleschKincaidScore > 80
                          ? 'info'
                          : 'warning'
                      }
                    />
                  </TableCell>
                </TableRow>
                {contentMetrics.readability.smogIndex && (
                  <TableRow>
                    <TableCell component="th" scope="row">
                      SMOG Index
                    </TableCell>
                    <TableCell align="right">{contentMetrics.readability.smogIndex.toFixed(1)}</TableCell>
                    <TableCell align="right">Grade level</TableCell>
                  </TableRow>
                )}
                {contentMetrics.readability.colemanLiauIndex && (
                  <TableRow>
                    <TableCell component="th" scope="row">
                      Coleman-Liau Index
                    </TableCell>
                    <TableCell align="right">{contentMetrics.readability.colemanLiauIndex.toFixed(1)}</TableCell>
                    <TableCell align="right">Grade level</TableCell>
                  </TableRow>
                )}
                <TableRow>
                  <TableCell component="th" scope="row">
                    Average Sentence Length
                  </TableCell>
                  <TableCell align="right">{contentMetrics.content.averageSentenceLength.toFixed(1)}</TableCell>
                  <TableCell align="right">
                    <Chip
                      size="small"
                      label={
                        contentMetrics.content.averageSentenceLength >= 10 && contentMetrics.content.averageSentenceLength <= 20
                          ? 'Optimal'
                          : contentMetrics.content.averageSentenceLength < 10
                          ? 'Too Short'
                          : 'Too Long'
                      }
                      color={
                        contentMetrics.content.averageSentenceLength >= 10 && contentMetrics.content.averageSentenceLength <= 20
                          ? 'success'
                          : 'warning'
                      }
                    />
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell component="th" scope="row">
                    Reading Time
                  </TableCell>
                  <TableCell align="right">{contentMetrics.readability.readingTimeMinutes.toFixed(1)} minutes</TableCell>
                  <TableCell align="right">
                    <Chip
                      size="small"
                      label={
                        contentMetrics.readability.readingTimeMinutes >= 3 && contentMetrics.readability.readingTimeMinutes <= 7
                          ? 'Optimal'
                          : contentMetrics.readability.readingTimeMinutes < 3
                          ? 'Too Short'
                          : 'Long Form'
                      }
                      color={
                        contentMetrics.readability.readingTimeMinutes >= 2
                          ? 'success'
                          : 'warning'
                      }
                    />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
          
          {contentMetrics.readability.suggestions && contentMetrics.readability.suggestions.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Readability Suggestions:
              </Typography>
              <List dense>
                {contentMetrics.readability.suggestions.map((suggestion, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <VisibilityIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={suggestion} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </AccordionDetails>
      </Accordion>

      <Accordion expanded={expandedSection === 'panel2'} onChange={handleChange('panel2')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormatListBulletedIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="subtitle1">Structure Analysis</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" align="center">{contentMetrics.structure.headingCount}</Typography>
                  <Typography variant="body2" align="center" color="text.secondary">Headings</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" align="center">{contentMetrics.structure.paragraphCount}</Typography>
                  <Typography variant="body2" align="center" color="text.secondary">Paragraphs</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" align="center">{contentMetrics.structure.listCount}</Typography>
                  <Typography variant="body2" align="center" color="text.secondary">Lists</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          {contentMetrics.structure.suggestions && contentMetrics.structure.suggestions.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Structure Suggestions:
              </Typography>
              <List dense>
                {contentMetrics.structure.suggestions.map((suggestion, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <FormatListBulletedIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={suggestion} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </AccordionDetails>
      </Accordion>

      <Accordion expanded={expandedSection === 'panel3'} onChange={handleChange('panel3')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TrendingUpIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="subtitle1">Engagement & Coherence</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Engagement Score</Typography>
                  <LinearProgress
                    variant="determinate"
                    value={contentMetrics.engagement.engagementScore * 100}
                    sx={{
                      height: 10,
                      borderRadius: 5,
                      mb: 1,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: getScoreColor(contentMetrics.engagement.engagementScore)
                      }
                    }}
                  />
                  <Typography variant="h6" align="right">{formatScore(contentMetrics.engagement.engagementScore)}</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Coherence Score</Typography>
                  <LinearProgress
                    variant="determinate"
                    value={contentMetrics.coherence.coherenceScore * 100}
                    sx={{
                      height: 10,
                      borderRadius: 5,
                      mb: 1,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: getScoreColor(contentMetrics.coherence.coherenceScore)
                      }
                    }}
                  />
                  <Typography variant="h6" align="right">{formatScore(contentMetrics.coherence.coherenceScore)}</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          {contentMetrics.engagement.suggestions && contentMetrics.engagement.suggestions.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Engagement Suggestions:
              </Typography>
              <List dense>
                {contentMetrics.engagement.suggestions.map((suggestion, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <TrendingUpIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={suggestion} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default ContentQualityPanel;
