'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Autocomplete,
  CircularProgress,
  Alert,
  Di<PERSON>r,
  Card,
  CardContent,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface WorkflowInitiatorProps {
  onStartWorkflow: (workflowData: WorkflowInitData) => Promise<void>;
  loading?: boolean;
}

export interface WorkflowInitData {
  topic: string;
  contentType: 'blog-article' | 'product-page' | 'buying-guide';
  targetAudience: string;
  tone: string;
  keywords: string[];
  additionalInstructions?: string;
  clientName?: string;
}

/**
 * Component for initiating a new collaborative agent workflow
 * Provides a user-friendly interface for setting up content creation parameters
 */
const WorkflowInitiator: React.FC<WorkflowInitiatorProps> = ({ 
  onStartWorkflow,
  loading = false
}) => {
  // Form state
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<WorkflowInitData>({
    topic: '',
    contentType: 'blog-article',
    targetAudience: '',
    tone: 'professional',
    keywords: [],
    additionalInstructions: '',
    clientName: ''
  });
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof WorkflowInitData, string>>>({});
  const [keywordInput, setKeywordInput] = useState('');

  // Content type options
  const contentTypes = [
    { value: 'blog-article', label: 'Blog Article', description: 'Informative content to engage readers and build authority' },
    { value: 'product-page', label: 'Product Page', description: 'Persuasive content to showcase product features and benefits' },
    { value: 'buying-guide', label: 'Buying Guide', description: 'Educational content to help customers make informed decisions' }
  ];

  // Tone options
  const toneOptions = [
    'professional', 'conversational', 'authoritative', 
    'friendly', 'technical', 'casual', 'formal'
  ];

  // Handle form field changes
  const handleChange = (field: keyof WorkflowInitData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field if it exists
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Add a keyword
  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      handleChange('keywords', [...formData.keywords, keywordInput.trim()]);
      setKeywordInput('');
    }
  };

  // Remove a keyword
  const handleRemoveKeyword = (keyword: string) => {
    handleChange('keywords', formData.keywords.filter(k => k !== keyword));
  };

  // Validate form data
  const validateForm = (): boolean => {
    const errors: Partial<Record<keyof WorkflowInitData, string>> = {};
    
    if (!formData.topic.trim()) {
      errors.topic = 'Topic is required';
    }
    
    if (!formData.targetAudience.trim()) {
      errors.targetAudience = 'Target audience is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (validateForm()) {
      await onStartWorkflow(formData);
    }
  };

  // Steps for the workflow setup
  const steps = [
    'Basic Information',
    'Content Details',
    'Additional Settings'
  ];

  // Handle next step
  const handleNext = () => {
    if (activeStep === 0 && (!formData.topic.trim() || !formData.contentType)) {
      setFormErrors({
        ...formErrors,
        topic: !formData.topic.trim() ? 'Topic is required' : undefined
      });
      return;
    }
    
    if (activeStep === 1 && !formData.targetAudience.trim()) {
      setFormErrors({
        ...formErrors,
        targetAudience: 'Target audience is required'
      });
      return;
    }
    
    setActiveStep(prevStep => prevStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Start New Content Creation Workflow
      </Typography>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      
      {/* Step 1: Basic Information */}
      {activeStep === 0 && (
        <Box>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Content Topic"
                value={formData.topic}
                onChange={(e) => handleChange('topic', e.target.value)}
                error={!!formErrors.topic}
                helperText={formErrors.topic || 'Enter the main topic for your content'}
                placeholder="e.g., Benefits of Cloud Computing for Small Businesses"
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Content Type
              </Typography>
              <Grid container spacing={2}>
                {contentTypes.map((type) => (
                  <Grid item xs={12} sm={4} key={type.value}>
                    <Card 
                      variant="outlined"
                      sx={{
                        cursor: 'pointer',
                        height: '100%',
                        bgcolor: formData.contentType === type.value ? 'primary.50' : 'background.paper',
                        borderColor: formData.contentType === type.value ? 'primary.main' : 'divider',
                        transition: 'all 0.2s',
                        '&:hover': {
                          borderColor: 'primary.main',
                          boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
                        }
                      }}
                      onClick={() => handleChange('contentType', type.value)}
                    >
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {type.label}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {type.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
          
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="contained"
              onClick={handleNext}
              endIcon={<PlayArrowIcon />}
            >
              Next
            </Button>
          </Box>
        </Box>
      )}
      
      {/* Step 2: Content Details */}
      {activeStep === 1 && (
        <Box>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Target Audience"
                value={formData.targetAudience}
                onChange={(e) => handleChange('targetAudience', e.target.value)}
                error={!!formErrors.targetAudience}
                helperText={formErrors.targetAudience || 'Describe who this content is for'}
                placeholder="e.g., Small business owners looking to modernize their IT infrastructure"
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="tone-select-label">Content Tone</InputLabel>
                <Select
                  labelId="tone-select-label"
                  value={formData.tone}
                  label="Content Tone"
                  onChange={(e) => handleChange('tone', e.target.value)}
                >
                  {toneOptions.map((tone) => (
                    <MenuItem key={tone} value={tone}>
                      {tone.charAt(0).toUpperCase() + tone.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Keywords
                <Tooltip title="Add important keywords that should be included in the content">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <HelpOutlineIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              
              <Box sx={{ display: 'flex', mb: 2 }}>
                <TextField
                  fullWidth
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  placeholder="Enter a keyword"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddKeyword();
                    }
                  }}
                  sx={{ mr: 1 }}
                />
                <Button 
                  variant="outlined" 
                  onClick={handleAddKeyword}
                  disabled={!keywordInput.trim()}
                >
                  Add
                </Button>
              </Box>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.keywords.map((keyword) => (
                  <Chip
                    key={keyword}
                    label={keyword}
                    onDelete={() => handleRemoveKeyword(keyword)}
                  />
                ))}
                {formData.keywords.length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    No keywords added yet
                  </Typography>
                )}
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            <Button onClick={handleBack}>
              Back
            </Button>
            <Button
              variant="contained"
              onClick={handleNext}
            >
              Next
            </Button>
          </Box>
        </Box>
      )}
      
      {/* Step 3: Additional Settings */}
      {activeStep === 2 && (
        <Box>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Client Name (Optional)"
                value={formData.clientName || ''}
                onChange={(e) => handleChange('clientName', e.target.value)}
                placeholder="e.g., Acme Corporation"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Additional Instructions (Optional)"
                value={formData.additionalInstructions || ''}
                onChange={(e) => handleChange('additionalInstructions', e.target.value)}
                multiline
                rows={4}
                placeholder="Any specific requirements or preferences for the content"
              />
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 4, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              Workflow Summary
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Topic:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formData.topic || 'Not specified'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Content Type:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {contentTypes.find(t => t.value === formData.contentType)?.label || formData.contentType}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Target Audience:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formData.targetAudience || 'Not specified'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Tone:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formData.tone.charAt(0).toUpperCase() + formData.tone.slice(1)}
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Keywords:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                  {formData.keywords.length > 0 ? (
                    formData.keywords.map((keyword) => (
                      <Chip key={keyword} label={keyword} size="small" />
                    ))
                  ) : (
                    <Typography variant="body1">None</Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            <Button onClick={handleBack}>
              Back
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <PlayArrowIcon />}
            >
              {loading ? 'Starting...' : 'Start Workflow'}
            </Button>
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default WorkflowInitiator;
