// src/components/EnhancedCollaboration/AgentCollaborationVisualizer.tsx
'use client';

import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface Agent {
  id: string;
  name: string;
  description: string;
  avatar?: string;
}

interface Message {
  id: string;
  from: string;
  to: string;
  content: string;
  timestamp: string;
  metadata?: {
    [key: string]: any;
  };
}

interface Artifact {
  id: string;
  type: string;
  name: string;
  creator: string;
  timestamp: string;
  data: {
    [key: string]: any;
  };
}

interface AgentCollaborationVisualizerProps {
  sessionId: string;
  messages: Message[];
  artifacts: Artifact[];
  agents: Agent[];
  isActive: boolean;
  onRefresh: () => void;
}

const AgentCollaborationVisualizer: React.FC<AgentCollaborationVisualizerProps> = ({
  sessionId,
  messages,
  artifacts,
  agents,
  isActive,
  onRefresh
}) => {
  const [expandedArtifacts, setExpandedArtifacts] = useState<{[key: string]: boolean}>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);

  // Default agents if none provided
  const defaultAgents: Agent[] = [
    { id: 'market-research-agent', name: 'Market Research Agent', description: 'Analyzes the target audience and provides market insights' },
    { id: 'seo-keyword-agent', name: 'SEO Keyword Agent', description: 'Generates and prioritizes keywords for content optimization' },
    { id: 'content-strategy-agent', name: 'Content Strategy Agent', description: 'Develops content structure and strategic approach' },
    { id: 'content-generation-agent', name: 'Content Generation Agent', description: 'Creates high-quality content based on strategy and research' },
    { id: 'seo-optimization-agent', name: 'SEO Optimization Agent', description: 'Ensures content is optimized for search engines' }
  ];

  const agentList = agents.length > 0 ? agents : defaultAgents;
  
  // Map agent IDs to their names and colors
  const agentMap = agentList.reduce((map, agent) => {
    map[agent.id] = {
      name: agent.name,
      color: getAgentColor(agent.id)
    };
    return map;
  }, {} as {[key: string]: {name: string, color: string}});

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  // Toggle artifact expansion
  const toggleArtifact = (artifactId: string) => {
    setExpandedArtifacts(prev => ({
      ...prev,
      [artifactId]: !prev[artifactId]
    }));
  };

  // Get color for agent
  function getAgentColor(agentId: string): string {
    const colorMap: {[key: string]: string} = {
      'market-research-agent': '#4285F4', // Blue
      'seo-keyword-agent': '#34A853',     // Green
      'content-strategy-agent': '#FBBC05', // Yellow
      'content-generation-agent': '#EA4335', // Red
      'seo-optimization-agent': '#8E24AA',  // Purple
      'orchestrator': '#039BE5',            // Light Blue
      'user': '#616161'                     // Gray
    };
    
    return colorMap[agentId] || '#757575';
  }

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    } catch (e) {
      return timestamp;
    }
  };

  // Render artifact content based on type
  const renderArtifactContent = (artifact: Artifact) => {
    switch (artifact.type) {
      case 'market-research':
        return renderMarketResearch(artifact.data);
      case 'keyword-set':
        return renderKeywords(artifact.data);
      case 'content-outline':
        return renderOutline(artifact.data);
      case 'final-content':
        return renderFinalContent(artifact.data);
      default:
        return (
          <pre className="artifact-data">
            {JSON.stringify(artifact.data, null, 2)}
          </pre>
        );
    }
  };

  // Render market research artifact
  const renderMarketResearch = (data: any) => (
    <div className="market-research">
      {data.audienceInsights && (
        <div className="research-section">
          <h4>Audience Insights</h4>
          <ul>
            {data.audienceInsights.map((insight: string, i: number) => (
              <li key={i}>{insight}</li>
            ))}
          </ul>
        </div>
      )}
      
      {data.marketTrends && (
        <div className="research-section">
          <h4>Market Trends</h4>
          <ul>
            {data.marketTrends.map((trend: string, i: number) => (
              <li key={i}>{trend}</li>
            ))}
          </ul>
        </div>
      )}
      
      {data.competitorAnalysis && (
        <div className="research-section">
          <h4>Competitor Analysis</h4>
          <ul>
            {data.competitorAnalysis.map((item: string, i: number) => (
              <li key={i}>{item}</li>
            ))}
          </ul>
        </div>
      )}
      
      {data.challenges && (
        <div className="research-section">
          <h4>Challenges</h4>
          <ul>
            {data.challenges.map((challenge: string, i: number) => (
              <li key={i}>{challenge}</li>
            ))}
          </ul>
        </div>
      )}
      
      {data.opportunities && (
        <div className="research-section">
          <h4>Opportunities</h4>
          <ul>
            {data.opportunities.map((opportunity: string, i: number) => (
              <li key={i}>{opportunity}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );

  // Render keywords artifact
  const renderKeywords = (data: any) => (
    <div className="keywords">
      {data.primaryKeyword && (
        <div className="keyword-section">
          <h4>Primary Keyword</h4>
          <div className="primary-keyword">{data.primaryKeyword}</div>
        </div>
      )}
      
      {data.secondaryKeywords && (
        <div className="keyword-section">
          <h4>Secondary Keywords</h4>
          <div className="keyword-tags">
            {data.secondaryKeywords.map((keyword: string, i: number) => (
              <span key={i} className="keyword-tag">{keyword}</span>
            ))}
          </div>
        </div>
      )}
      
      {data.keywordGroups && (
        <div className="keyword-section">
          <h4>Keyword Groups</h4>
          {data.keywordGroups.map((group: any, i: number) => (
            <div key={i} className="keyword-group">
              <h5>{group.name}</h5>
              <div className="keyword-tags">
                {group.keywords.map((keyword: string, j: number) => (
                  <span key={j} className="keyword-tag">{keyword}</span>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  // Render outline artifact
  const renderOutline = (data: any) => (
    <div className="outline">
      <h4>{data.title}</h4>
      
      {data.sections && (
        <div className="outline-sections">
          <h5>Sections</h5>
          <ol>
            {data.sections.map((section: string, i: number) => (
              <li key={i}>
                <strong>{section}</strong>
                
                {data.subsections && data.subsections[section] && (
                  <ul>
                    {data.subsections[section].map((subsection: string, j: number) => (
                      <li key={j}>{subsection}</li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ol>
        </div>
      )}
      
      {data.contentGoals && (
        <div className="content-goals">
          <h5>Content Goals</h5>
          <ul>
            {data.contentGoals.map((goal: string, i: number) => (
              <li key={i}>{goal}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );

  // Render final content artifact
  const renderFinalContent = (data: any) => (
    <div className="final-content-preview">
      <h4>Content Preview</h4>
      <div className="content-preview">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {data.content.substring(0, 300) + (data.content.length > 300 ? '...' : '')}
        </ReactMarkdown>
      </div>
      <div className="content-stats">
        <span>Word count: ~{data.content.split(/\s+/).length}</span>
      </div>
    </div>
  );

  return (
    <div className="agent-collaboration-visualizer">
      <div className="visualizer-header">
        <h3>Agent Collaboration Process</h3>
        <div className="visualizer-controls">
          <button 
            className="refresh-button"
            onClick={onRefresh}
            disabled={!isActive}
          >
            Refresh
          </button>
          <label className="auto-scroll-toggle">
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={() => setAutoScroll(!autoScroll)}
            />
            Auto-scroll
          </label>
        </div>
      </div>
      
      <div className="agent-list">
        <h4>Participating Agents</h4>
        <div className="agent-cards">
          {agentList.map(agent => (
            <div 
              key={agent.id} 
              className="agent-card"
              style={{ borderColor: getAgentColor(agent.id) }}
            >
              <div 
                className="agent-avatar"
                style={{ backgroundColor: getAgentColor(agent.id) }}
              >
                {agent.name.charAt(0)}
              </div>
              <div className="agent-info">
                <div className="agent-name">{agent.name}</div>
                <div className="agent-description">{agent.description}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="collaboration-timeline">
        {messages.length === 0 && artifacts.length === 0 ? (
          <div className="empty-timeline">
            <p>No collaboration activity yet. The agents will begin working once the session starts.</p>
          </div>
        ) : (
          <>
            {/* Combine and sort messages and artifacts by timestamp */}
            {[...messages, ...artifacts.map(a => ({ 
              id: a.id, 
              from: a.creator, 
              to: 'all', 
              content: '', 
              timestamp: a.timestamp,
              isArtifact: true,
              artifact: a
            }))].sort((a, b) => 
              new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
            ).map(item => {
              if ('isArtifact' in item && item.isArtifact) {
                // Render artifact
                const artifact = (item as any).artifact;
                return (
                  <div 
                    key={`artifact-${artifact.id}`} 
                    className="timeline-artifact"
                    style={{ borderColor: getAgentColor(artifact.creator) }}
                  >
                    <div className="artifact-header" onClick={() => toggleArtifact(artifact.id)}>
                      <div 
                        className="agent-indicator"
                        style={{ backgroundColor: getAgentColor(artifact.creator) }}
                      >
                        {agentMap[artifact.creator]?.name.charAt(0) || artifact.creator.charAt(0)}
                      </div>
                      <div className="artifact-title">
                        <strong>{agentMap[artifact.creator]?.name || artifact.creator}</strong> created: {artifact.name}
                      </div>
                      <div className="artifact-timestamp">
                        {formatTimestamp(artifact.timestamp)}
                      </div>
                      <div className="artifact-expand">
                        {expandedArtifacts[artifact.id] ? '▼' : '▶'}
                      </div>
                    </div>
                    
                    {expandedArtifacts[artifact.id] && (
                      <div className="artifact-content">
                        {renderArtifactContent(artifact)}
                      </div>
                    )}
                  </div>
                );
              } else {
                // Render message
                const message = item as Message;
                return (
                  <div 
                    key={`message-${message.id}`} 
                    className={`timeline-message ${message.from === 'user' ? 'user-message' : ''}`}
                  >
                    <div 
                      className="agent-indicator"
                      style={{ backgroundColor: getAgentColor(message.from) }}
                    >
                      {agentMap[message.from]?.name.charAt(0) || message.from.charAt(0)}
                    </div>
                    <div className="message-content">
                      <div className="message-header">
                        <strong>{agentMap[message.from]?.name || message.from}</strong>
                        <span className="message-recipient">
                          to {message.to === 'all' ? 'Everyone' : (agentMap[message.to]?.name || message.to)}
                        </span>
                      </div>
                      <div className="message-body">
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {message.content}
                        </ReactMarkdown>
                      </div>
                    </div>
                    <div className="message-timestamp">
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>
                );
              }
            })}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <style jsx>{`
        .agent-collaboration-visualizer {
          display: flex;
          flex-direction: column;
          height: 100%;
          background-color: #f9f9f9;
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid #e0e0e0;
        }
        
        .visualizer-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background-color: #f0f0f0;
          border-bottom: 1px solid #e0e0e0;
        }
        
        .visualizer-header h3 {
          margin: 0;
          font-size: 18px;
          color: #333;
        }
        
        .visualizer-controls {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .refresh-button {
          background-color: #4285F4;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 6px 12px;
          cursor: pointer;
          font-size: 14px;
          transition: background-color 0.2s;
        }
        
        .refresh-button:hover {
          background-color: #3367d6;
        }
        
        .refresh-button:disabled {
          background-color: #a0a0a0;
          cursor: not-allowed;
        }
        
        .auto-scroll-toggle {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: #555;
          cursor: pointer;
        }
        
        .agent-list {
          padding: 12px 16px;
          border-bottom: 1px solid #e0e0e0;
        }
        
        .agent-list h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          color: #333;
        }
        
        .agent-cards {
          display: flex;
          gap: 12px;
          overflow-x: auto;
          padding-bottom: 8px;
        }
        
        .agent-card {
          display: flex;
          align-items: center;
          gap: 10px;
          background-color: white;
          border-radius: 6px;
          padding: 8px 12px;
          border-left: 4px solid;
          min-width: 200px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .agent-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          flex-shrink: 0;
        }
        
        .agent-info {
          display: flex;
          flex-direction: column;
        }
        
        .agent-name {
          font-weight: 600;
          font-size: 14px;
        }
        
        .agent-description {
          font-size: 12px;
          color: #666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 150px;
        }
        
        .collaboration-timeline {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
          display: flex;
          flex-direction: column;
          gap: 12px;
        }
        
        .empty-timeline {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #666;
          text-align: center;
          padding: 20px;
        }
        
        .timeline-message {
          display: flex;
          gap: 12px;
          background-color: white;
          border-radius: 6px;
          padding: 12px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          max-width: 90%;
          align-self: flex-start;
        }
        
        .user-message {
          align-self: flex-end;
          background-color: #e3f2fd;
        }
        
        .agent-indicator {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          flex-shrink: 0;
        }
        
        .message-content {
          flex: 1;
          min-width: 0;
        }
        
        .message-header {
          display: flex;
          gap: 8px;
          align-items: baseline;
          margin-bottom: 6px;
        }
        
        .message-recipient {
          font-size: 12px;
          color: #666;
        }
        
        .message-body {
          font-size: 14px;
          line-height: 1.5;
          overflow-wrap: break-word;
        }
        
        .message-timestamp {
          font-size: 12px;
          color: #888;
          align-self: flex-start;
          white-space: nowrap;
        }
        
        .timeline-artifact {
          background-color: white;
          border-radius: 6px;
          border-left: 4px solid;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .artifact-header {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          cursor: pointer;
          background-color: #f5f5f5;
        }
        
        .artifact-title {
          flex: 1;
          font-size: 14px;
        }
        
        .artifact-timestamp {
          font-size: 12px;
          color: #888;
        }
        
        .artifact-expand {
          font-size: 12px;
          color: #666;
        }
        
        .artifact-content {
          padding: 16px;
          border-top: 1px solid #eee;
        }
        
        .market-research,
        .keywords,
        .outline,
        .final-content-preview {
          font-size: 14px;
        }
        
        .research-section,
        .keyword-section,
        .outline-sections,
        .content-goals {
          margin-bottom: 16px;
        }
        
        .research-section h4,
        .keyword-section h4,
        .outline-sections h5,
        .content-goals h5 {
          margin: 0 0 8px 0;
          font-size: 15px;
          color: #333;
        }
        
        .research-section ul,
        .content-goals ul {
          margin: 0;
          padding-left: 20px;
        }
        
        .research-section li,
        .content-goals li {
          margin-bottom: 4px;
        }
        
        .primary-keyword {
          font-weight: 600;
          color: #4285F4;
          margin-bottom: 8px;
        }
        
        .keyword-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
        }
        
        .keyword-tag {
          background-color: #e0f2f1;
          color: #00796b;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
        
        .keyword-group {
          margin-bottom: 12px;
        }
        
        .keyword-group h5 {
          margin: 0 0 6px 0;
          font-size: 14px;
          color: #555;
        }
        
        .outline-sections ol {
          margin: 0;
          padding-left: 20px;
        }
        
        .outline-sections li {
          margin-bottom: 8px;
        }
        
        .outline-sections ul {
          margin: 4px 0 0 0;
          padding-left: 20px;
        }
        
        .outline-sections ul li {
          margin-bottom: 4px;
          font-weight: normal;
        }
        
        .content-preview {
          background-color: #f5f5f5;
          padding: 12px;
          border-radius: 4px;
          margin-bottom: 8px;
        }
        
        .content-stats {
          font-size: 12px;
          color: #666;
        }
        
        .artifact-data {
          background-color: #f5f5f5;
          padding: 12px;
          border-radius: 4px;
          font-size: 12px;
          overflow-x: auto;
          max-height: 300px;
        }
      `}</style>
    </div>
  );
};

export default AgentCollaborationVisualizer;
