'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Chip,
  Avatar,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Button,
  Collapse,
  IconButton,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import PsychologyIcon from '@mui/icons-material/Psychology';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import ThumbDownIcon from '@mui/icons-material/ThumbDown';
import { styled } from '@mui/material/styles';
import { IterativeArtifact } from '../../app/(payload)/api/agents/collaborative-iteration/types';

// Styled expand icon for the card
const ExpandMore = styled((props: {
  expand: boolean;
  onClick: () => void;
  'aria-expanded': boolean;
  'aria-label': string;
}) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme, expand }) => ({
  transform: !expand ? 'rotate(0deg)' : 'rotate(180deg)',
  marginLeft: 'auto',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
}));

interface ArtifactReasoningPanelProps {
  artifacts: Record<string, IterativeArtifact>;
  generatedArtifacts: string[];
  messages: any[];
  onViewArtifact?: (artifactId: string) => void;
  loading?: boolean;
}

/**
 * Component to display the reasoning behind artifact generation and selection
 */
const ArtifactReasoningPanel: React.FC<ArtifactReasoningPanelProps> = ({
  artifacts,
  generatedArtifacts,
  messages,
  onViewArtifact,
  loading = false
}) => {
  const [expandedArtifact, setExpandedArtifact] = useState<string | null>(null);
  const [artifactsWithReasoning, setArtifactsWithReasoning] = useState<any[]>([]);

  // Process artifacts and extract reasoning
  useEffect(() => {
    if (!artifacts || !generatedArtifacts || !messages) return;

    const processedArtifacts = generatedArtifacts
      .map(id => {
        const artifact = artifacts[id];
        if (!artifact) return null;

        // Find messages related to this artifact
        const relatedMessages = messages.filter(msg => {
          // Check if message references this artifact
          if (msg.artifactId === id) return true;
          if (msg.metadata?.artifactId === id) return true;
          if (msg.content?.artifactId === id) return true;
          if (msg.artifactReferences?.includes(id)) return true;
          
          // Check if message is about artifact creation or delivery
          if (msg.type === 'ARTIFACT_DELIVERY' && msg.content?.artifactId === id) return true;
          
          return false;
        });

        // Extract reasoning from messages
        const reasoning = relatedMessages
          .filter(msg => msg.reasoning)
          .map(msg => msg.reasoning)
          .filter(Boolean);

        // Extract consultations from iterations
        const consultations = artifact.iterations
          ?.flatMap(iteration => iteration.incorporatedConsultations || [])
          .filter(Boolean) || [];

        return {
          ...artifact,
          relatedMessages,
          reasoning,
          consultations
        };
      })
      .filter(Boolean);

    setArtifactsWithReasoning(processedArtifacts);
  }, [artifacts, generatedArtifacts, messages]);

  // Handle card expansion
  const handleExpandClick = (artifactId: string) => {
    setExpandedArtifact(expandedArtifact === artifactId ? null : artifactId);
  };

  // Get agent color based on agent ID
  const getAgentColor = (agentId: string): string => {
    const agentColors: Record<string, string> = {
      'market-research': '#2196f3',
      'seo-keyword': '#ff9800',
      'content-strategy': '#3f51b5',
      'content-generation': '#009688',
      'seo-optimization': '#f44336',
      'system': '#9e9e9e',
      'user': '#4caf50'
    };
    
    return agentColors[agentId] || '#9e9e9e';
  };

  // Get artifact type display name
  const getArtifactTypeDisplay = (type: string): string => {
    const typeMap: Record<string, string> = {
      'market-research-report': 'Market Research Report',
      'keyword-analysis': 'SEO Keyword Analysis',
      'content-strategy': 'Content Strategy',
      'content-draft': 'Content Draft',
      'seo-optimized-content': 'SEO Optimized Content',
      'final-content': 'Final Content'
    };
    
    return typeMap[type] || type;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string): string => {
    if (!timestamp) return '';
    
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  // Extract reasoning thoughts
  const extractThoughts = (reasoning: any): string[] => {
    if (!reasoning) return [];
    
    if (Array.isArray(reasoning.thoughts)) {
      return reasoning.thoughts;
    }
    
    if (reasoning.process) {
      return [reasoning.process];
    }
    
    return [];
  };

  // Extract reasoning considerations
  const extractConsiderations = (reasoning: any): string[] => {
    if (!reasoning) return [];
    
    if (Array.isArray(reasoning.considerations)) {
      return reasoning.considerations;
    }
    
    if (reasoning.steps) {
      return reasoning.steps;
    }
    
    return [];
  };

  if (loading) {
    return (
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading artifact reasoning...
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (artifactsWithReasoning.length === 0) {
    return (
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h5" gutterBottom>
          Artifact Reasoning
        </Typography>
        <Alert severity="info">
          No artifacts with reasoning data available yet. As the workflow progresses, you'll see the reasoning behind each artifact's creation here.
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom>
        Artifact Reasoning
      </Typography>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        This panel explains the reasoning behind each artifact's creation, including the thought process, considerations, and decisions made by the agents.
      </Typography>
      
      <Divider sx={{ mb: 3 }} />
      
      <Grid container spacing={3}>
        {artifactsWithReasoning.map(artifact => (
          <Grid item xs={12} key={artifact.id}>
            <Card variant="outlined">
              <CardHeader
                avatar={
                  <Avatar sx={{ bgcolor: getAgentColor(artifact.createdBy) }}>
                    <PsychologyIcon />
                  </Avatar>
                }
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="subtitle1">
                      {artifact.name || `Artifact ${artifact.id}`}
                    </Typography>
                    <Chip 
                      label={getArtifactTypeDisplay(artifact.type)} 
                      size="small" 
                      color="primary" 
                      sx={{ ml: 1 }} 
                    />
                  </Box>
                }
                subheader={
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Created by {artifact.createdBy} on {formatTimestamp(artifact.createdAt)}
                    </Typography>
                    {artifact.qualityScore !== undefined && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                          Quality Score:
                        </Typography>
                        <Chip
                          size="small"
                          label={`${Math.round(artifact.qualityScore * 100)}%`}
                          color={
                            artifact.qualityScore > 0.8 ? 'success' :
                            artifact.qualityScore > 0.5 ? 'primary' : 'warning'
                          }
                          sx={{ height: 20 }}
                        />
                      </Box>
                    )}
                  </Box>
                }
              />
              
              <CardContent>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {artifact.reasoning && artifact.reasoning.length > 0 
                    ? `This artifact was created with ${artifact.reasoning.length} reasoning processes and incorporates ${artifact.consultations.length} consultations.`
                    : 'This artifact was created based on the workflow requirements.'}
                </Typography>
                
                {artifact.reasoning && artifact.reasoning.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Key Decision Factors:
                    </Typography>
                    <List dense>
                      {artifact.reasoning.slice(0, 1).map((reasoning: any, index: number) => (
                        <React.Fragment key={index}>
                          {extractConsiderations(reasoning).slice(0, 3).map((consideration: string, i: number) => (
                            <ListItem key={i}>
                              <ListItemIcon sx={{ minWidth: 36 }}>
                                <LightbulbIcon color="primary" fontSize="small" />
                              </ListItemIcon>
                              <ListItemText primary={consideration} />
                            </ListItem>
                          ))}
                        </React.Fragment>
                      ))}
                    </List>
                  </Box>
                )}
              </CardContent>
              
              <CardActions disableSpacing>
                {onViewArtifact && (
                  <Button
                    size="small"
                    startIcon={<VisibilityIcon />}
                    onClick={() => onViewArtifact(artifact.id)}
                  >
                    View Artifact
                  </Button>
                )}
                
                <ExpandMore
                  expand={expandedArtifact === artifact.id}
                  onClick={() => handleExpandClick(artifact.id)}
                  aria-expanded={expandedArtifact === artifact.id}
                  aria-label="show more"
                >
                  <ExpandMoreIcon />
                </ExpandMore>
              </CardActions>
              
              <Collapse in={expandedArtifact === artifact.id} timeout="auto" unmountOnExit>
                <CardContent>
                  <Typography variant="subtitle2" gutterBottom>
                    Complete Reasoning Process:
                  </Typography>
                  
                  {artifact.reasoning && artifact.reasoning.length > 0 ? (
                    artifact.reasoning.map((reasoning: any, index: number) => (
                      <Box key={index} sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" color="primary" gutterBottom>
                          Reasoning Process {index + 1}:
                        </Typography>
                        
                        {/* Thoughts */}
                        {extractThoughts(reasoning).length > 0 && (
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" fontWeight="medium">
                              Thoughts:
                            </Typography>
                            <List dense>
                              {extractThoughts(reasoning).map((thought: string, i: number) => (
                                <ListItem key={i}>
                                  <ListItemIcon sx={{ minWidth: 36 }}>
                                    <PsychologyIcon fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText primary={thought} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}
                        
                        {/* Considerations */}
                        {extractConsiderations(reasoning).length > 0 && (
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" fontWeight="medium">
                              Considerations:
                            </Typography>
                            <List dense>
                              {extractConsiderations(reasoning).map((consideration: string, i: number) => (
                                <ListItem key={i}>
                                  <ListItemIcon sx={{ minWidth: 36 }}>
                                    <LightbulbIcon fontSize="small" />
                                  </ListItemIcon>
                                  <ListItemText primary={consideration} />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}
                        
                        {/* Decision */}
                        {reasoning.decision && (
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" fontWeight="medium">
                              Decision:
                            </Typography>
                            <Typography variant="body2" sx={{ pl: 4 }}>
                              {reasoning.decision}
                            </Typography>
                          </Box>
                        )}
                        
                        {/* Confidence */}
                        {reasoning.confidence !== undefined && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Typography variant="body2" fontWeight="medium" sx={{ mr: 1 }}>
                              Confidence:
                            </Typography>
                            <Chip
                              size="small"
                              label={`${Math.round(reasoning.confidence * 100)}%`}
                              color={
                                reasoning.confidence > 0.8 ? 'success' :
                                reasoning.confidence > 0.5 ? 'primary' : 'warning'
                              }
                            />
                          </Box>
                        )}
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No detailed reasoning available for this artifact.
                    </Typography>
                  )}
                </CardContent>
              </Collapse>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default ArtifactReasoningPanel;
