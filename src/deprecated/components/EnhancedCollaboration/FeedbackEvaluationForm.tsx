import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Slider,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  FormControlLabel,
  Divider,
  Chip,
  IconButton,
  Paper,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

// Define the evaluation criteria
const DEFAULT_CRITERIA = [
  { id: 'relevance', name: 'Relevance', description: 'How relevant is the content to the topic?' },
  { id: 'accuracy', name: 'Accuracy', description: 'How accurate is the information provided?' },
  { id: 'completeness', name: 'Completeness', description: 'How complete is the coverage of the topic?' },
  { id: 'clarity', name: 'Clarity', description: 'How clear and understandable is the content?' },
  { id: 'structure', name: 'Structure', description: 'How well-organized is the content?' },
];

// Define the evaluation feedback interface
export interface EvaluationFeedback {
  overallScore: number;
  feedback: string;
  criteriaResults: Array<{
    criterion: string;
    score: number;
    feedback: string;
    suggestions?: string[];
  }>;
  suggestions: string[];
  metrics?: Record<string, number | string>;
}

interface FeedbackEvaluationFormProps {
  artifactId: string;
  artifactType: string;
  onSubmit: (artifactId: string, evaluation: EvaluationFeedback) => void;
  onCancel: () => void;
}

const FeedbackEvaluationForm: React.FC<FeedbackEvaluationFormProps> = ({
  artifactId,
  artifactType,
  onSubmit,
  onCancel,
}) => {
  // Initialize state with default criteria
  const [criteria, setCriteria] = useState(DEFAULT_CRITERIA.map(c => ({
    ...c,
    score: 0.7,
    feedback: '',
    suggestions: [''],
  })));
  
  const [overallFeedback, setOverallFeedback] = useState('');
  const [overallScore, setOverallScore] = useState(0.7);
  const [generalSuggestions, setGeneralSuggestions] = useState(['']);

  // Handle criteria score change
  const handleCriteriaScoreChange = (index: number, value: number) => {
    const newCriteria = [...criteria];
    newCriteria[index].score = value;
    setCriteria(newCriteria);
  };

  // Handle criteria feedback change
  const handleCriteriaFeedbackChange = (index: number, value: string) => {
    const newCriteria = [...criteria];
    newCriteria[index].feedback = value;
    setCriteria(newCriteria);
  };

  // Handle adding a suggestion to a criterion
  const handleAddCriteriaSuggestion = (criterionIndex: number) => {
    const newCriteria = [...criteria];
    newCriteria[criterionIndex].suggestions.push('');
    setCriteria(newCriteria);
  };

  // Handle updating a suggestion for a criterion
  const handleUpdateCriteriaSuggestion = (criterionIndex: number, suggestionIndex: number, value: string) => {
    const newCriteria = [...criteria];
    newCriteria[criterionIndex].suggestions[suggestionIndex] = value;
    setCriteria(newCriteria);
  };

  // Handle removing a suggestion from a criterion
  const handleRemoveCriteriaSuggestion = (criterionIndex: number, suggestionIndex: number) => {
    const newCriteria = [...criteria];
    newCriteria[criterionIndex].suggestions.splice(suggestionIndex, 1);
    setCriteria(newCriteria);
  };

  // Handle adding a general suggestion
  const handleAddGeneralSuggestion = () => {
    setGeneralSuggestions([...generalSuggestions, '']);
  };

  // Handle updating a general suggestion
  const handleUpdateGeneralSuggestion = (index: number, value: string) => {
    const newSuggestions = [...generalSuggestions];
    newSuggestions[index] = value;
    setGeneralSuggestions(newSuggestions);
  };

  // Handle removing a general suggestion
  const handleRemoveGeneralSuggestion = (index: number) => {
    const newSuggestions = [...generalSuggestions];
    newSuggestions.splice(index, 1);
    setGeneralSuggestions(newSuggestions);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Filter out empty suggestions
    const filteredCriteria = criteria.map(c => ({
      ...c,
      suggestions: c.suggestions.filter(s => s.trim() !== '')
    }));
    
    const filteredGeneralSuggestions = generalSuggestions.filter(s => s.trim() !== '');

    // Create the evaluation feedback object
    const evaluation: EvaluationFeedback = {
      overallScore,
      feedback: overallFeedback,
      criteriaResults: filteredCriteria.map(c => ({
        criterion: c.name,
        score: c.score,
        feedback: c.feedback,
        suggestions: c.suggestions
      })),
      suggestions: filteredGeneralSuggestions,
      metrics: {
        averageCriteriaScore: filteredCriteria.reduce((acc, c) => acc + c.score, 0) / filteredCriteria.length,
        criteriaCount: filteredCriteria.length,
        suggestionCount: filteredGeneralSuggestions.length + 
          filteredCriteria.reduce((acc, c) => acc + c.suggestions.length, 0)
      }
    };

    onSubmit(artifactId, evaluation);
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Evaluation Feedback for {artifactType}
      </Typography>
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" gutterBottom>
          Overall Evaluation
        </Typography>
        
        <FormControl fullWidth sx={{ mb: 2 }}>
          <FormLabel>Overall Quality Score</FormLabel>
          <Box sx={{ px: 2 }}>
            <Slider
              value={overallScore}
              onChange={(_, value) => setOverallScore(value as number)}
              step={0.1}
              marks
              min={0}
              max={1}
              valueLabelDisplay="auto"
              valueLabelFormat={value => `${Math.round(value * 100)}%`}
            />
          </Box>
        </FormControl>
        
        <TextField
          fullWidth
          multiline
          rows={3}
          label="Overall Feedback"
          value={overallFeedback}
          onChange={(e) => setOverallFeedback(e.target.value)}
          margin="normal"
        />
      </Box>
      
      <Divider sx={{ my: 3 }} />
      
      <Typography variant="subtitle1" gutterBottom>
        Evaluation Criteria
      </Typography>
      
      {criteria.map((criterion, index) => (
        <Accordion key={criterion.id} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Typography sx={{ flexGrow: 1 }}>{criterion.name}</Typography>
              <Chip 
                label={`${Math.round(criterion.score * 100)}%`} 
                color={criterion.score >= 0.7 ? 'success' : criterion.score >= 0.4 ? 'warning' : 'error'}
                size="small"
                sx={{ mr: 1 }}
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {criterion.description}
            </Typography>
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel>Score</FormLabel>
              <Box sx={{ px: 2 }}>
                <Slider
                  value={criterion.score}
                  onChange={(_, value) => handleCriteriaScoreChange(index, value as number)}
                  step={0.1}
                  marks
                  min={0}
                  max={1}
                  valueLabelDisplay="auto"
                  valueLabelFormat={value => `${Math.round(value * 100)}%`}
                />
              </Box>
            </FormControl>
            
            <TextField
              fullWidth
              multiline
              rows={2}
              label="Feedback"
              value={criterion.feedback}
              onChange={(e) => handleCriteriaFeedbackChange(index, e.target.value)}
              margin="normal"
            />
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                Improvement Suggestions
              </Typography>
              
              {criterion.suggestions.map((suggestion, suggestionIndex) => (
                <Box key={suggestionIndex} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={suggestion}
                    onChange={(e) => handleUpdateCriteriaSuggestion(index, suggestionIndex, e.target.value)}
                    placeholder="Suggestion for improvement"
                  />
                  <IconButton 
                    color="error" 
                    onClick={() => handleRemoveCriteriaSuggestion(index, suggestionIndex)}
                    disabled={criterion.suggestions.length <= 1}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
              
              <Button
                startIcon={<AddIcon />}
                onClick={() => handleAddCriteriaSuggestion(index)}
                size="small"
                sx={{ mt: 1 }}
              >
                Add Suggestion
              </Button>
            </Box>
          </AccordionDetails>
        </Accordion>
      ))}
      
      <Divider sx={{ my: 3 }} />
      
      <Typography variant="subtitle1" gutterBottom>
        General Improvement Suggestions
      </Typography>
      
      {generalSuggestions.map((suggestion, index) => (
        <Box key={index} sx={{ display: 'flex', mb: 1 }}>
          <TextField
            fullWidth
            size="small"
            value={suggestion}
            onChange={(e) => handleUpdateGeneralSuggestion(index, e.target.value)}
            placeholder="General suggestion for improvement"
          />
          <IconButton 
            color="error" 
            onClick={() => handleRemoveGeneralSuggestion(index)}
            disabled={generalSuggestions.length <= 1}
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      ))}
      
      <Button
        startIcon={<AddIcon />}
        onClick={handleAddGeneralSuggestion}
        size="small"
        sx={{ mt: 1, mb: 3 }}
      >
        Add General Suggestion
      </Button>
      
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        <Button onClick={onCancel} sx={{ mr: 1 }}>
          Cancel
        </Button>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleSubmit}
          disabled={!overallFeedback.trim()}
        >
          Submit Evaluation
        </Button>
      </Box>
    </Box>
  );
};

export default FeedbackEvaluationForm;
