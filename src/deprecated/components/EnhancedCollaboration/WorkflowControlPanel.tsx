'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Chip,
  Grid
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import StopIcon from '@mui/icons-material/Stop';
import RefreshIcon from '@mui/icons-material/Refresh';
import FeedbackIcon from '@mui/icons-material/Feedback';
import SettingsIcon from '@mui/icons-material/Settings';
import WorkflowMonitor from './WorkflowMonitor';

interface WorkflowControlPanelProps {
  sessionId: string;
  status: string;
  currentPhase?: string;
  progress?: number;
  estimatedTimeRemaining?: string;
  onRefresh?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  onCancel?: () => void;
  onSendFeedback?: (feedback: string) => void;
  onUpdateSettings?: (settings: Record<string, any>) => void;
  loading?: boolean;
  error?: string | null;
}

/**
 * Component to control workflow execution with feedback and settings options
 */
const WorkflowControlPanel: React.FC<WorkflowControlPanelProps> = ({
  sessionId,
  status,
  currentPhase,
  progress = 0,
  estimatedTimeRemaining,
  onRefresh,
  onPause,
  onResume,
  onCancel,
  onSendFeedback,
  onUpdateSettings,
  loading = false,
  error = null
}) => {
  // Dialog states
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [settingsDialogOpen, setSettingsDialogOpen] = useState(false);
  const [cancelConfirmDialogOpen, setCancelConfirmDialogOpen] = useState(false);
  
  // Form states
  const [feedback, setFeedback] = useState('');
  const [settings, setSettings] = useState({
    maxIterations: 5,
    qualityThreshold: 0.8
  });
  
  // Handle feedback submission
  const handleSubmitFeedback = () => {
    if (feedback.trim() && onSendFeedback) {
      onSendFeedback(feedback);
      setFeedback('');
      setFeedbackDialogOpen(false);
    }
  };
  
  // Handle settings update
  const handleUpdateSettings = () => {
    if (onUpdateSettings) {
      onUpdateSettings(settings);
      setSettingsDialogOpen(false);
    }
  };
  
  // Handle cancel confirmation
  const handleConfirmCancel = () => {
    if (onCancel) {
      onCancel();
      setCancelConfirmDialogOpen(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      {/* Workflow Monitor */}
      <WorkflowMonitor
        sessionId={sessionId}
        status={status}
        currentPhase={currentPhase}
        progress={progress}
        estimatedTimeRemaining={estimatedTimeRemaining}
        error={error}
        loading={loading}
      />
      
      <Divider sx={{ my: 3 }} />
      
      {/* Control Buttons */}
      <Typography variant="h6" gutterBottom>
        Workflow Controls
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Paper variant="outlined" sx={{ p: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Execution Controls
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {status === 'active' ? (
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<PauseIcon />}
                  onClick={onPause}
                  disabled={!onPause || loading}
                  fullWidth
                >
                  Pause Workflow
                </Button>
              ) : status === 'paused' ? (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<PlayArrowIcon />}
                  onClick={onResume}
                  disabled={!onResume || loading}
                  fullWidth
                >
                  Resume Workflow
                </Button>
              ) : null}
              
              <Button
                variant="outlined"
                color="error"
                startIcon={<StopIcon />}
                onClick={() => setCancelConfirmDialogOpen(true)}
                disabled={!onCancel || loading || status === 'completed' || status === 'failed'}
                fullWidth
              >
                Cancel Workflow
              </Button>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Paper variant="outlined" sx={{ p: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Feedback & Settings
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                startIcon={<FeedbackIcon />}
                onClick={() => setFeedbackDialogOpen(true)}
                disabled={!onSendFeedback || loading}
                fullWidth
              >
                Send Feedback
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                onClick={() => setSettingsDialogOpen(true)}
                disabled={!onUpdateSettings || loading}
                fullWidth
              >
                Workflow Settings
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
      
      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
        <Button
          variant="text"
          startIcon={<RefreshIcon />}
          onClick={onRefresh}
          disabled={!onRefresh || loading}
        >
          {loading ? 'Refreshing...' : 'Refresh Status'}
        </Button>
      </Box>
      
      {/* Feedback Dialog */}
      <Dialog
        open={feedbackDialogOpen}
        onClose={() => setFeedbackDialogOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Send Feedback to Agents</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Your feedback"
            fullWidth
            multiline
            rows={4}
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            placeholder="Provide feedback or guidance for the AI agents..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFeedbackDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleSubmitFeedback}
            variant="contained"
            disabled={!feedback.trim()}
          >
            Send
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Settings Dialog */}
      <Dialog
        open={settingsDialogOpen}
        onClose={() => setSettingsDialogOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Workflow Settings</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Changing settings will affect the current workflow execution.
          </Alert>
          
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                margin="dense"
                label="Maximum Iterations"
                type="number"
                fullWidth
                value={settings.maxIterations}
                onChange={(e) => setSettings({
                  ...settings,
                  maxIterations: parseInt(e.target.value) || 1
                })}
                inputProps={{ min: 1, max: 10 }}
                helperText="Maximum number of content iterations (1-10)"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                margin="dense"
                label="Quality Threshold"
                type="number"
                fullWidth
                value={settings.qualityThreshold}
                onChange={(e) => setSettings({
                  ...settings,
                  qualityThreshold: parseFloat(e.target.value) || 0.5
                })}
                inputProps={{ min: 0.5, max: 1, step: 0.1 }}
                helperText="Minimum quality score to accept (0.5-1.0)"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSettingsDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleUpdateSettings}
            variant="contained"
          >
            Update Settings
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Cancel Confirmation Dialog */}
      <Dialog
        open={cancelConfirmDialogOpen}
        onClose={() => setCancelConfirmDialogOpen(false)}
      >
        <DialogTitle>Cancel Workflow?</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to cancel this workflow? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCancelConfirmDialogOpen(false)}>No, Continue</Button>
          <Button 
            onClick={handleConfirmCancel}
            variant="contained"
            color="error"
          >
            Yes, Cancel Workflow
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default WorkflowControlPanel;
