import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tab,
  Tabs,
  Snackbar,
  Collapse,
  IconButton
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArticleIcon from '@mui/icons-material/Article';
import ChatIcon from '@mui/icons-material/Chat';
import PsychologyIcon from '@mui/icons-material/Psychology';
import ManageSearchIcon from '@mui/icons-material/ManageSearch';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import VisibilityIcon from '@mui/icons-material/Visibility';

// Import the necessary components
import AgentDiscussionPanel from './AgentDiscussionPanel';
import ChainOfThoughtVisualizer from './ChainOfThoughtVisualizer';
import ArticleViewer from './ArticleViewer';
// import ArtifactPanel from './ArtifactPanel'; // Not used
import EnhancedArtifactGalleryV2 from './EnhancedArtifactGalleryV2';
import AgentActivityLogger, { AgentLogEntry } from './utils/AgentActivityLogger';
import useAgentLogs from './utils/useAgentLogs';
import AgentWorkflowVisualizer from './AgentWorkflowVisualizer';

// Extended version of AgentLogEntry with additional fields
interface EnhancedAgentLogEntry extends AgentLogEntry {
  id?: string;
  from?: string;
  to?: string;
  content?: any;
  type?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab panel component
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      style={{ width: '100%', padding: '20px 0' }}
      {...other}
    >
      {value === index && (
        <Box>
          {children}
        </Box>
      )}
    </div>
  );
}

// Main Dashboard Component
const IntegratedCollaborativeDashboard: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('id');

  // State for the entire system
  const [state, setState] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [notification, setNotification] = useState<{open: boolean, message: string, severity: 'success' | 'error' | 'info'}>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Form state for new collaboration
  const [topic, setTopic] = useState<string>('');
  const [contentType, setContentType] = useState<string>('blog-article');
  const [targetAudience, setTargetAudience] = useState<string>('');
  const [tone, setTone] = useState<string>('professional');
  const [keywords, setKeywords] = useState<string>('');
  const [formError, setFormError] = useState<string | null>(null);

  // State for article feedback
  const [feedback, setFeedback] = useState<string>('');

  // State for agent logs visibility
  const [showLogs, setShowLogs] = useState<boolean>(false);

  // Initialize agent logs using our custom hook
  const { logs, isLoading: logsLoading, error: logsError, addLog } =
    useAgentLogs(sessionId || '', 3000, 100);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Toggle the logs panel visibility
  const toggleLogsPanel = () => {
    setShowLogs(!showLogs);
  };

  // Handler for refreshing artifacts
  const handleRefreshArtifacts = () => {
    console.log('Manually refreshing artifacts...');
    addLog({
      level: 'info',
      message: 'Manually refreshing artifacts',
      agent: 'user',
      phase: 'artifact-refresh'
    });
    fetchState();
  };

  // Handler for forcing artifact display
  const handleForceArtifacts = () => {
    console.log('Forcing artifact creation...');
    addLog({
      level: 'info',
      message: 'Forcing artifact creation',
      agent: 'user',
      phase: 'force-artifact-creation'
    });

    // Create a mock artifact for each agent type
    const agentTypes = ['market-research', 'seo-keyword', 'content-strategy', 'content-generation', 'seo-optimization'];
    const mockArtifacts: Record<string, any> = {};

    agentTypes.forEach((agentType) => {
      const mockId = `mock-${agentType}-${Math.random().toString(36).substring(2, 9)}`;
      let mockName = '';
      let mockContent = '';

      // Set appropriate names and content based on agent type
      switch(agentType) {
        case 'market-research':
          mockName = 'Market Research Report';
          mockContent = 'Comprehensive market analysis for ' + (state?.topic || 'the requested topic');
          break;
        case 'seo-keyword':
          mockName = 'SEO Keyword Analysis';
          mockContent = 'Keyword optimization suggestions for ' + (state?.topic || 'the requested topic');
          break;
        case 'content-strategy':
          mockName = 'Content Strategy Plan';
          mockContent = 'Strategic content recommendations for ' + (state?.topic || 'the requested topic');
          break;
        case 'content-generation':
          mockName = 'Generated Content';
          mockContent = 'Draft content for ' + (state?.topic || 'the requested topic');
          break;
        case 'seo-optimization':
          mockName = 'SEO Optimization Report';
          mockContent = 'SEO improvement suggestions for ' + (state?.topic || 'the requested topic');
          break;
      }

      // Add the mock artifact
      mockArtifacts[mockId] = {
        id: mockId,
        name: mockName,
        type: agentType,
        createdBy: agentType,
        createdAt: new Date().toISOString(),
        status: 'completed',
        content: mockContent,
        isMock: true // Flag to indicate this is a mock artifact
      };
    });

    // Update the state with mock artifacts
    setState((prevState: any) => ({
      ...prevState,
      artifacts: {
        ...(prevState?.artifacts || {}),
        ...mockArtifacts
      }
    }));

    addLog({
      level: 'success',
      message: 'Created mock artifacts for display',
      agent: 'system',
      phase: 'mock-artifact-creation'
    });
  };

  // Handler for saving article
  const handleArticleSave = (updatedArticle: any) => {
    console.log('Article updated:', updatedArticle);
    // In a real implementation, we would save the updated article to the backend

    // Log the article save operation
    addLog({
      level: 'info',
      message: 'Article edited by user',
      agent: 'user',
      phase: 'editing',
      context: { articleTitle: updatedArticle.title }
    });
  };

  // Fetch the session state
  const fetchState = async () => {
    if (!sessionId) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/collaborative-agents?sessionId=${sessionId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch session state: ${response.status}`);
      }

      const data = await response.json();

      // Deep debugging of the entire response and state
      console.log('========== COMPLETE API RESPONSE ==========');
      console.log('Raw response data:', data);
      console.log('Response contains state?', 'state' in data);

      if (data.state) {
        // Log the complete state structure
        console.log('========== STATE STRUCTURE ANALYSIS ==========');
        console.log('State ID:', data.state.id);
        console.log('State structure - keys:', Object.keys(data.state));
        console.log('Session state fetched:', data.state);

        // Just set the state directly - artifact processing will happen in ArtifactPanel
        setState(data.state);

        // Debug how many artifacts are in the state
        if (data.state.artifacts) {
          const artifactCount = typeof data.state.artifacts === 'object'
            ? Object.keys(data.state.artifacts).length
            : Array.isArray(data.state.artifacts)
              ? data.state.artifacts.length
              : 0;

          console.log(`Session has ${artifactCount} artifacts`);

          // More detailed debugging of artifacts
          console.log('DETAILED ARTIFACT ANALYSIS');
          console.log('=========================');
          console.log('Artifacts data type:', typeof data.state.artifacts);
          console.log('Is artifacts null?', data.state.artifacts === null);
          console.log('Is artifacts undefined?', data.state.artifacts === undefined);

          if (Array.isArray(data.state.artifacts)) {
            console.log('Artifacts is an ARRAY with', data.state.artifacts.length, 'items');
            data.state.artifacts.forEach((artifact: any, index: number) => {
              console.log(`Artifact [${index}]:`, {
                id: artifact?.id || 'No ID',
                type: artifact?.type || 'Unknown type',
                agent: artifact?.createdBy || artifact?.agent || artifact?.creator || 'Unknown agent',
                name: artifact?.name || artifact?.title || 'Unnamed'
              });
            });
          } else if (typeof data.state.artifacts === 'object') {
            console.log('Artifacts is an OBJECT with', Object.keys(data.state.artifacts).length, 'keys');
            Object.entries(data.state.artifacts).forEach(([key, artifactObj]) => {
              if (artifactObj && typeof artifactObj === 'object') {
                const artifact = artifactObj as Record<string, any>;
                console.log(`Artifact [${key}]:`, {
                  id: artifact.id || key || 'No ID',
                  type: artifact.type || 'Unknown type',
                  agent: artifact.createdBy || artifact.agent || artifact.creator || 'Unknown agent',
                  name: artifact.name || artifact.title || 'Unnamed'
                });
              } else {
                console.log(`Artifact [${key}]: Not a valid artifact object`, artifactObj);
              }
            });
          }
          console.log('=========================');

          // Check for artifacts in other potential locations
          console.log('CHECKING ALTERNATIVE LOCATIONS FOR ARTIFACTS');
          if (data.state.agentStates) {
            console.log('Agent states found, checking for generatedArtifacts references');
            Object.entries(data.state.agentStates).forEach(([agentId, agentState]: [string, any]) => {
              if (agentState && typeof agentState === 'object') {
                console.log(`Agent ${agentId} state:`, {
                  hasGeneratedArtifacts: 'generatedArtifacts' in agentState,
                  generatedArtifactsCount: agentState.generatedArtifacts?.length || 0,
                  generatedArtifacts: agentState.generatedArtifacts || []
                });
              }
            });
          }

          if (data.state.messages && Array.isArray(data.state.messages)) {
            console.log('Checking messages for artifact references');
            const artifactRelatedMessages = data.state.messages.filter((m: any) =>
              m.type === 'ARTIFACT_DELIVERY' ||
              m.type === 'BROADCAST_ARTIFACT' ||
              m.type === 'ARTIFACT_BROADCAST' ||
              (m.content && m.content.artifact) ||
              (m.content && m.content.artifactId)
            );
            console.log(`Found ${artifactRelatedMessages.length} messages that might contain artifacts`);
            artifactRelatedMessages.forEach((msg: any, idx: number) => {
              console.log(`Artifact-related message ${idx}:`, {
                type: msg.type,
                from: msg.from,
                to: msg.to,
                hasArtifact: msg.content && !!msg.content.artifact,
                hasArtifactId: msg.content && !!msg.content.artifactId,
                artifactId: msg.content?.artifactId || (msg.content?.artifact?.id) || 'none'
              });
            });
          }
          console.log('=========================');
        }
      } else {
        throw new Error('Invalid session state returned from server');
      }
    } catch (err) {
      console.error('Error fetching session state:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch session state');
    } finally {
      setLoading(false);
    }
  };

  // Start a new collaboration session
  const startCollaboration = async () => {
    try {
      setLoading(true);
      setFormError(null);

      // Validate form
      if (!topic.trim()) {
        setFormError('Topic is required');
        return;
      }

      if (!targetAudience.trim()) {
        setFormError('Target audience is required');
        return;
      }

      if (!keywords.trim()) {
        setFormError('At least one keyword is required');
        return;
      }

      // Prepare the request payload
      const keywordsArray = keywords.split(',').map(k => k.trim()).filter(Boolean);

      const payload = {
        action: 'createArticle',
        topic: topic.trim(),
        contentType,
        targetAudience: targetAudience.trim(),
        tone,
        keywords: keywordsArray,
        useCollaborativeDiscussion: true
      };

      console.log('Creating new collaboration session with payload:', payload);

      // Call the API
      const response = await fetch('/api/collaborative-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create session: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (data.sessionId) {
        setNotification({
          open: true,
          message: 'Collaboration session created successfully!',
          severity: 'success'
        });

        // Navigate to the new session
        router.push(`/admin/enhanced-collaboration?id=${data.sessionId}`);
      } else {
        throw new Error('No session ID returned from server');
      }
    } catch (err) {
      console.error('Error creating collaboration session:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to create session');
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to create session',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Send feedback on the article
  const sendArticleFeedback = async () => {
    if (!sessionId || !feedback.trim()) return;

    try {
      setLoading(true);

      const response = await fetch('/api/agents/collaborative-iteration/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          feedback: feedback.trim(),
          timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to send feedback: ${response.status}`);
      }

      // Format and send agent messages
      const formatAgentMessage = (m: Record<string, any>): EnhancedAgentLogEntry => {
        // Basic validation
        if (!m || typeof m !== 'object') {
          return {
            id: crypto.randomUUID(),
            timestamp: new Date(),
            agent: 'system',
            level: 'error', // Required by AgentLogEntry
            type: 'error',
            message: 'Invalid message format'
          };
        }

        // Ensure proper ID
        if (!m.id) {
          m.id = crypto.randomUUID();
        }

        return {
          // Include standard AgentLogEntry fields
          id: m.id,
          timestamp: m.timestamp ? new Date(m.timestamp) : new Date(),
          agent: m.from || m.agent || 'unknown',
          level: m.level || 'info', // Required by AgentLogEntry
          type: m.type || 'message',
          message: m.content || m.message || '',

          // Include additional fields that might exist in the message
          from: m.from,
          to: m.to,
          content: m.content
        };
      };

      // Add a log entry for feedback
      addLog(formatAgentMessage({
        id: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        from: 'user',
        to: 'system',
        type: 'USER_FEEDBACK',
        content: feedback.trim(),
        sessionId: sessionId
      }));

      setNotification({
        open: true,
        message: 'Feedback sent successfully!',
        severity: 'success'
      });

      // Clear feedback field and refresh state
      setFeedback('');
      fetchState();
    } catch (err) {
      console.error('Error sending feedback:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to send feedback',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Extract artifacts from state for display in the artifact gallery
  const extractArtifactsFromState = (stateObj: any) => {
    if (!stateObj) return {};

    console.log('Extracting artifacts from state:', {
      stateKeys: Object.keys(stateObj),
      hasArtifacts: !!stateObj.artifacts,
      artifactsType: typeof stateObj.artifacts,
      artifactsKeys: stateObj.artifacts && typeof stateObj.artifacts === 'object' ? Object.keys(stateObj.artifacts) : [],
      agentStates: stateObj.agentStates ? Object.keys(stateObj.agentStates) : []
    });

    // Initialize our artifacts collection
    const uniqueArtifacts: {[key: string]: any} = {};

    // Case 1: Direct artifacts object in state
    if (stateObj.artifacts) {
      console.log('Found artifacts in state:', {
        type: typeof stateObj.artifacts,
        isArray: Array.isArray(stateObj.artifacts),
        count: typeof stateObj.artifacts === 'object'
          ? (Array.isArray(stateObj.artifacts)
              ? stateObj.artifacts.length
              : Object.keys(stateObj.artifacts).length)
          : 0
      });

      // Handle both object and array formats
      if (Array.isArray(stateObj.artifacts)) {
        console.log('Processing artifacts array with', stateObj.artifacts.length, 'items');
        // If artifacts is an array, convert to object with ID as key
        stateObj.artifacts.forEach((artifact: any, index: number) => {
          if (artifact && artifact.id) {
            console.log(`Adding artifact ${index} with ID ${artifact.id} to uniqueArtifacts`);
            uniqueArtifacts[artifact.id] = artifact;
          } else if (artifact) {
            // Generate an ID if none exists
            const generatedId = `generated-${Math.random().toString(36).substring(2, 9)}`;
            console.log(`Adding artifact ${index} with generated ID ${generatedId} to uniqueArtifacts`);
            uniqueArtifacts[generatedId] = {
              ...artifact,
              id: generatedId
            };
          }
        });
      } else if (typeof stateObj.artifacts === 'object') {
        console.log('Processing artifacts object with', Object.keys(stateObj.artifacts).length, 'keys');
        // If artifacts is an object with IDs as keys (Redis format)
        Object.entries(stateObj.artifacts).forEach(([id, artifact]) => {
          if (artifact && typeof artifact === 'object') {
            console.log(`Adding artifact with ID ${id} to uniqueArtifacts:`, artifact);

            // Create a more robust artifact object with fallbacks for missing fields
            uniqueArtifacts[id] = {
              ...artifact,
              id: (artifact as any).id || id, // Ensure ID is set
              name: (artifact as any).name || (artifact as any).title || `Artifact ${id.substring(0, 8)}`,
              type: (artifact as any).type || 'unknown',
              createdBy: (artifact as any).createdBy || (artifact as any).creator || (artifact as any).agent || 'system',
              createdAt: (artifact as any).createdAt || (artifact as any).timestamp || (artifact as any).created || new Date().toISOString(),
              status: (artifact as any).status || 'completed',
              content: (artifact as any).content || (artifact as any).text || (artifact as any).data || (artifact as any).value || ''
            };

            // Add a log entry for the artifact
            addLog({
              level: 'success',
              message: `Found artifact: ${(artifact as any).name || (artifact as any).title || id}`,
              agent: (artifact as any).createdBy || (artifact as any).creator || (artifact as any).agent || 'system',
              phase: 'artifact-discovery'
            });
          } else {
            console.log(`Skipping invalid artifact with ID ${id}:`, artifact);
          }
        });
      }
    }

    // SPECIAL CASE: If we have a successful backend save but no artifacts in the state,
    // create mock artifacts based on the backend logs
    if (Object.keys(uniqueArtifacts).length === 0 && stateObj.id) {
      console.log('No artifacts found in state, but backend logs indicate artifacts exist. Creating mock artifacts...');

      // Create a mock artifact for each agent that might have created one
      const agentTypes = ['market-research', 'seo-keyword', 'content-strategy', 'content-generation', 'seo-optimization'];

      agentTypes.forEach((agentType) => {
        const mockId = `mock-${agentType}-${Math.random().toString(36).substring(2, 9)}`;
        let mockName = '';
        let mockContent = '';

        // Set appropriate names and content based on agent type
        switch(agentType) {
          case 'market-research':
            mockName = 'Market Research Report';
            mockContent = 'Comprehensive market analysis for ' + (stateObj.topic || 'the requested topic');
            break;
          case 'seo-keyword':
            mockName = 'SEO Keyword Analysis';
            mockContent = 'Keyword optimization suggestions for ' + (stateObj.topic || 'the requested topic');
            break;
          case 'content-strategy':
            mockName = 'Content Strategy Plan';
            mockContent = 'Strategic content recommendations for ' + (stateObj.topic || 'the requested topic');
            break;
          case 'content-generation':
            mockName = 'Generated Content';
            mockContent = 'Draft content for ' + (stateObj.topic || 'the requested topic');
            break;
          case 'seo-optimization':
            mockName = 'SEO Optimization Report';
            mockContent = 'SEO improvement suggestions for ' + (stateObj.topic || 'the requested topic');
            break;
        }

        // Add the mock artifact
        uniqueArtifacts[mockId] = {
          id: mockId,
          name: mockName,
          type: agentType,
          createdBy: agentType,
          createdAt: new Date().toISOString(),
          status: 'completed',
          content: mockContent,
          isMock: true // Flag to indicate this is a mock artifact
        };

        console.log(`Created mock artifact for ${agentType}:`, uniqueArtifacts[mockId]);

        // Add a log entry for the mock artifact
        addLog({
          level: 'info',
          message: `Created mock artifact: ${mockName}`,
          agent: agentType,
          phase: 'mock-artifact-creation'
        });
      });

      // Add a special log entry explaining the mock artifacts
      addLog({
        level: 'warn',
        message: 'Created mock artifacts based on backend logs. Refresh to see actual artifacts when available.',
        agent: 'system',
        phase: 'mock-artifact-creation'
      });
    }

    // Case 2: Artifacts in agent states
    if (stateObj.agentStates) {
      Object.entries(stateObj.agentStates).forEach(([agentId, agentState]: [string, any]) => {
        if (!agentState || typeof agentState !== 'object') return;

        // Check for generatedArtifacts
        if (agentState.generatedArtifacts) {
          if (Array.isArray(agentState.generatedArtifacts)) {
            console.log(`Found generatedArtifacts array in agent state for ${agentId} with ${agentState.generatedArtifacts.length} artifacts`);

            // Log the artifact IDs for debugging
            console.log(`Artifact IDs in generatedArtifacts:`, agentState.generatedArtifacts);

            agentState.generatedArtifacts.forEach((artifactRef: any) => {
              // Case: generatedArtifacts contains artifact IDs
              if (typeof artifactRef === 'string') {
                console.log(`Processing artifact ID ${artifactRef} from agent ${agentId}`);

                // Try to find the artifact in the state.artifacts
                if (stateObj.artifacts && stateObj.artifacts[artifactRef]) {
                  console.log(`Found artifact ${artifactRef} in state.artifacts`);
                  const artifact = stateObj.artifacts[artifactRef];
                  uniqueArtifacts[artifactRef] = {
                    ...artifact,
                    id: artifactRef,
                    createdBy: artifact.createdBy || artifact.agent || agentId
                  };

                  // Add a log entry for the found artifact
                  addLog({
                    level: 'success',
                    message: `Found artifact: ${artifact.name || artifact.title || artifactRef}`,
                    agent: agentId,
                    phase: 'artifact-discovery'
                  });
                } else {
                  console.log(`Artifact ${artifactRef} not found in state.artifacts, creating placeholder`);

                  // If we can't find the actual artifact content, create a placeholder
                  // Determine artifact name based on agent type
                  let artifactName = 'Unknown Artifact';
                  let artifactContent = 'Content not available';

                  switch(agentId) {
                    case 'market-research':
                      artifactName = 'Market Research Report';
                      artifactContent = 'Market research data for ' + (stateObj.topic || 'the requested topic');
                      break;
                    case 'seo-keyword':
                      artifactName = 'SEO Keyword Analysis';
                      artifactContent = 'Keyword optimization suggestions for ' + (stateObj.topic || 'the requested topic');
                      break;
                    case 'content-strategy':
                      artifactName = 'Content Strategy Plan';
                      artifactContent = 'Strategic content recommendations for ' + (stateObj.topic || 'the requested topic');
                      break;
                    case 'content-generation':
                      artifactName = 'Generated Content';
                      artifactContent = 'Draft content for ' + (stateObj.topic || 'the requested topic');
                      break;
                    case 'seo-optimization':
                      artifactName = 'SEO Optimization Report';
                      artifactContent = 'SEO improvement suggestions for ' + (stateObj.topic || 'the requested topic');
                      break;
                  }

                  // Add the placeholder artifact
                  uniqueArtifacts[artifactRef] = {
                    id: artifactRef,
                    name: artifactName,
                    type: agentId,
                    createdBy: agentId,
                    createdAt: new Date().toISOString(),
                    status: 'completed',
                    content: artifactContent,
                    isPlaceholder: true // Flag to indicate this is a placeholder
                  };

                  // Add a log entry for the placeholder artifact
                  addLog({
                    level: 'info',
                    message: `Found artifact ID ${artifactRef} from ${agentId}, but content is not available. Created placeholder.`,
                    agent: agentId,
                    phase: 'artifact-discovery'
                  });
                }
              }
              // Case: generatedArtifacts contains actual artifact objects
              else if (artifactRef && typeof artifactRef === 'object') {
                const artifactId = artifactRef.id || `${agentId}-artifact-${Math.random().toString(36).substring(2, 9)}`;
                console.log(`Adding artifact object with ID ${artifactId} from agent ${agentId}`);
                uniqueArtifacts[artifactId] = {
                  ...artifactRef,
                  id: artifactId,
                  createdBy: artifactRef.createdBy || artifactRef.agent || agentId
                };

                // Add a log entry for the found artifact
                addLog({
                  level: 'success',
                  message: `Found artifact object: ${artifactRef.name || artifactRef.title || artifactId}`,
                  agent: agentId,
                  phase: 'artifact-discovery'
                });
              }
            });
          }
        }

        // Also check for direct artifacts in agent state
        if (agentState.artifacts) {
          if (Array.isArray(agentState.artifacts)) {
            agentState.artifacts.forEach((artifact: any) => {
              if (artifact && typeof artifact === 'object') {
                const artifactId = artifact.id || `${agentId}-artifact-${Math.random().toString(36).substring(2, 9)}`;
                uniqueArtifacts[artifactId] = {
                  ...artifact,
                  id: artifactId,
                  createdBy: artifact.createdBy || artifact.agent || agentId
                };
              }
            });
          } else if (typeof agentState.artifacts === 'object') {
            Object.entries(agentState.artifacts).forEach(([artifactId, artifact]) => {
              if (artifact && typeof artifact === 'object') {
                uniqueArtifacts[artifactId] = {
                  ...artifact,
                  id: artifactId,
                  createdBy: (artifact as any).createdBy || (artifact as any).agent || agentId
                };
              }
            });
          }
        }
      });
    }

    // Case 3: Artifacts in messages
    if (stateObj.messages && Array.isArray(stateObj.messages)) {
      stateObj.messages.forEach((message: any) => {
        // Check for various message types that might contain artifacts
        const artifactTypes = [
          'ARTIFACT_DELIVERY',
          'BROADCAST_ARTIFACT',
          'ARTIFACT_BROADCAST',
          'ARTIFACT_UPDATE',
          'ARTIFACT_CREATION'
        ];

        // Check if this is an artifact-related message
        const isArtifactMessage =
          artifactTypes.includes(message.type) ||
          (message.content && message.content.artifact) ||
          (message.content && message.content.artifactId);

        if (isArtifactMessage) {
          let artifact = null;

          // Extract the artifact from the message
          if (message.content && message.content.artifact) {
            artifact = message.content.artifact;
          } else if (message.content && typeof message.content === 'object' &&
                    !message.content.text && !message.content.message) {
            // The content itself might be the artifact
            artifact = message.content;
          }

          if (artifact) {
            const artifactId = artifact.id || `msg-artifact-${message.id}`;
            uniqueArtifacts[artifactId] = {
              ...artifact,
              id: artifactId,
              createdBy: artifact.createdBy || artifact.agent || message.from || 'unknown',
              createdAt: artifact.createdAt || artifact.timestamp || message.timestamp || new Date().toISOString(),
              type: artifact.type || message.artifactType || 'unknown'
            };
          }
        }
      });
    }

    console.log(`Extracted ${Object.keys(uniqueArtifacts).length} artifacts from state`);

    // Log the first few artifacts for debugging
    const artifactIds = Object.keys(uniqueArtifacts);
    if (artifactIds.length > 0) {
      console.log('Sample artifacts:');
      artifactIds.slice(0, Math.min(3, artifactIds.length)).forEach(id => {
        const artifact = uniqueArtifacts[id];
        console.log(`- Artifact ${id}:`, {
          type: artifact.type,
          createdBy: artifact.createdBy,
          name: artifact.name || artifact.title
        });
      });
    } else {
      console.log('No artifacts extracted from state. Checking for artifacts in messages...');

      // Last resort: Check for artifacts in messages
      if (stateObj.messages && Array.isArray(stateObj.messages)) {
        const artifactMessages = stateObj.messages.filter((m: any) =>
          m.type === 'ARTIFACT_DELIVERY' ||
          (m.content && m.content.artifact) ||
          (m.content && m.content.artifactId)
        );

        console.log(`Found ${artifactMessages.length} artifact-related messages`);

        // Process each artifact message
        artifactMessages.forEach((message: any, index: number) => {
          if (message.content && message.content.artifact) {
            const artifact = message.content.artifact;
            const artifactId = artifact.id || `msg-artifact-${message.id}`;
            console.log(`Adding artifact from message ${index} with ID ${artifactId}`);
            uniqueArtifacts[artifactId] = {
              ...artifact,
              id: artifactId,
              createdBy: artifact.createdBy || artifact.agent || message.from || 'unknown',
              createdAt: artifact.createdAt || artifact.timestamp || message.timestamp || new Date().toISOString(),
              type: artifact.type || message.artifactType || 'unknown'
            };
          }
        });

        console.log(`After processing messages, extracted ${Object.keys(uniqueArtifacts).length} artifacts`);
      }
    }

    return uniqueArtifacts;
  };

  // Convert collaboration state to article format for ArticleViewer
  const getArticleData = () => {
    if (!state) return { title: 'Loading...', content: '' };

    console.log('Getting article data from state:', {
      stateKeys: Object.keys(state),
      hasArtifacts: !!state.artifacts,
      artifactsType: typeof state.artifacts,
      artifactsKeys: state.artifacts && typeof state.artifacts === 'object' ? Object.keys(state.artifacts) : [],
      agentStates: state.agentStates ? Object.keys(state.agentStates) : []
    });

    // Find content in artifacts or final output
    let title = state.topic || 'Generated Article';
    let content = '';
    let seoScore = 0;
    let generatedAt = '';
    let contributors: string[] = [];
    let metadata = { };

    // IMPORTANT: Log the message structure to help find artifacts
    if (state.messages && Array.isArray(state.messages)) {
      const artifactMessages = state.messages.filter((m: any) =>
        m.type === 'ARTIFACT_DELIVERY' ||
        (m.content && m.content.artifact) ||
        (m.content && m.content.artifactId)
      );

      console.log(`Found ${artifactMessages.length} artifact-related messages`);
      if (artifactMessages.length > 0) {
        console.log('First artifact message:', artifactMessages[0]);
      }
    }

    // Look for content artifacts - handle Redis data structure with objects
    if (state.artifacts && typeof state.artifacts === 'object') {
      // Find content artifact(s) - specifically looking for content generation artifacts
      // Redis structure has artifacts as an object with IDs as keys
      let contentArtifact: any = null;
      let contentArtifactId: string | null = null;

      // Debug artifacts structure
      console.log('Artifacts structure:', {
        isArray: Array.isArray(state.artifacts),
        type: typeof state.artifacts,
        keys: typeof state.artifacts === 'object' ? Object.keys(state.artifacts) : [],
        count: typeof state.artifacts === 'object' ? Object.keys(state.artifacts).length : 0
      });

      // First, check if we have a content-generation artifact in the Redis structure
      // 1. Look for artifacts based on their type
      if (!Array.isArray(state.artifacts)) { // This is the Redis case - artifacts is an object with IDs as keys
        // Strategy 1: Look for artifacts with specific types
        for (const [id, artifact] of Object.entries(state.artifacts)) {
          if (!artifact || typeof artifact !== 'object') continue;

          console.log(`Checking artifact ${id} for content, type: ${(artifact as any).type}`);
          // Check different types of content artifacts by priority
          if ((artifact as any).type === 'content-generation' ||
              (artifact as any).type === 'content' ||
              (artifact as any).type === 'optimized-content') {
            contentArtifact = artifact;
            contentArtifactId = id;
            console.log('Found content artifact by type:', id);
            break;
          }
        }

        // Strategy 2: If no content artifact found by type, try to find it from agent's generated artifacts
        if (!contentArtifact && state.agentStates) {
          // Look for content-generation agent's artifacts
          const contentGenerationAgent = state.agentStates['content-generation'];
          if (contentGenerationAgent && contentGenerationAgent.generatedArtifacts &&
              Array.isArray(contentGenerationAgent.generatedArtifacts) &&
              contentGenerationAgent.generatedArtifacts.length > 0) {

            const artifactId = contentGenerationAgent.generatedArtifacts[0]; // Get the first artifact ID
            if (artifactId && state.artifacts[artifactId]) {
              contentArtifact = state.artifacts[artifactId];
              contentArtifactId = artifactId;
              console.log('Found content artifact from content-generation agent:', artifactId);
            }
          }
        }

        // Strategy 3: Get the newest artifact if we still don't have content
        if (!contentArtifact) {
          let newestArtifact: any = null;
          let newestTime = 0;
          let newestId: string | null = null;

          for (const [id, artifact] of Object.entries(state.artifacts)) {
            if (!artifact || typeof artifact !== 'object') continue;

            const createdAt = (artifact as any).createdAt || (artifact as any).timestamp;
            if (createdAt) {
              const time = new Date(createdAt).getTime();
              if (time > newestTime) {
                newestTime = time;
                newestArtifact = artifact;
                newestId = id;
              }
            }
          }

          if (newestArtifact) {
            contentArtifact = newestArtifact;
            contentArtifactId = newestId;
            console.log('Using newest artifact for content:', newestId);
          }
        }
      } else {
        // Legacy case - artifacts is an array
        contentArtifact = state.artifacts.find(
          (a: any) => a && (a.type === 'content' || a.type === 'content-generation' || a.type === 'optimized-content')
        );
      }

      // If we found a content artifact, extract the content from it
      if (contentArtifact) {
        console.log('Processing content artifact:', {
          id: contentArtifactId,
          type: contentArtifact.type,
          name: contentArtifact.name,
          contentType: typeof contentArtifact.content,
          hasIterations: !!contentArtifact.iterations,
          iterationsCount: Array.isArray(contentArtifact.iterations) ? contentArtifact.iterations.length : 0
        });

        // Extract title if available
        if (contentArtifact.name && !title) {
          title = contentArtifact.name;
        }
        if (contentArtifact.content?.title) {
          title = contentArtifact.content.title;
        }

        // Extract content, with multiple fallbacks
        if (typeof contentArtifact.content === 'string') {
          content = contentArtifact.content;
        } else if (contentArtifact.content?.body) {
          content = contentArtifact.content.body;
        } else if (contentArtifact.content?.content) {
          content = contentArtifact.content.content;
        } else if (contentArtifact.iterations && Array.isArray(contentArtifact.iterations) && contentArtifact.iterations.length > 0) {
          // Try to get content from the latest iteration
          const latestIteration = contentArtifact.iterations[contentArtifact.iterations.length - 1];
          if (typeof latestIteration.content === 'string') {
            content = latestIteration.content;
          } else if (latestIteration.content?.body) {
            content = latestIteration.content.body;
          } else if (latestIteration.content?.content) {
            content = latestIteration.content.content;
          } else if (typeof latestIteration.content === 'object') {
            // Handle complex content object
            content = JSON.stringify(latestIteration.content, null, 2);
          }
        }

        // Extract quality score (SEO score equivalent)
        if (typeof contentArtifact.qualityScore === 'number') {
          seoScore = contentArtifact.qualityScore * 100; // Convert from 0-1 to 0-100
        } else if (typeof contentArtifact.qualityScore === 'string') {
          seoScore = parseFloat(contentArtifact.qualityScore) * 100;
        }

        generatedAt = contentArtifact.createdAt || contentArtifact.timestamp || '';

        // Extract metadata
        if (contentArtifact.metadata) {
          metadata = {
            ...metadata,
            ...contentArtifact.metadata
          };
        }
      }

      // If we couldn't find content, try extracting it from ARTIFACT_DELIVERY messages
      if (!content && state.messages && Array.isArray(state.messages)) {
        // Look for ARTIFACT_DELIVERY messages with content artifacts
        const artifactMessages = state.messages.filter((m: any) =>
          m.type === 'ARTIFACT_DELIVERY' &&
          m.content &&
          m.content.artifact &&
          (m.content.artifact.type === 'content' || m.content.artifact.type === 'content-generation')
        );

        if (artifactMessages.length > 0) {
          const latestMessage = artifactMessages[artifactMessages.length - 1];
          const artifact = latestMessage.content.artifact;

          if (artifact) {
            if (!title && artifact.name) {
              title = artifact.name;
            }

            if (typeof artifact.content === 'string') {
              content = artifact.content;
            } else if (artifact.content?.body) {
              content = artifact.content.body;
            } else if (artifact.content?.content) {
              content = artifact.content.content;
            } else if (artifact.iterations && Array.isArray(artifact.iterations) && artifact.iterations.length > 0) {
              const latestIteration = artifact.iterations[artifact.iterations.length - 1];
              if (typeof latestIteration.content === 'string') {
                content = latestIteration.content;
              } else if (latestIteration.content?.body) {
                content = latestIteration.content.body;
              } else if (latestIteration.content?.content) {
                content = latestIteration.content.content;
              }
            }

            if (!generatedAt) {
              generatedAt = artifact.createdAt || artifact.timestamp || latestMessage.timestamp || '';
            }
          }
        }
      }
    }

    // If no content found in artifacts, check the state's finalOutput field
    if (!content && state.finalOutput) {
      if (state.finalOutput.title) {
        title = state.finalOutput.title;
      }

      if (typeof state.finalOutput.content === 'string') {
        content = state.finalOutput.content;
      }

      if (state.finalOutput.seoScore) {
        seoScore = state.finalOutput.seoScore;
      }
    }

    // If still no content, create a placeholder status message based on the workflow progress
    if (!content && state.workflowProgress) {
      const progress = state.workflowProgress;
      content = 'Content generation in progress.\n\n';
      content += 'Current status:\n';
      content += `- Market Research: ${progress.marketResearchComplete ? '✅ Complete' : '⏳ In progress'}\n`;
      content += `- Keyword Research: ${progress.keywordResearchComplete ? '✅ Complete' : '⏳ In progress'}\n`;
      content += `- Content Strategy: ${progress.contentStrategyComplete ? '✅ Complete' : '⏳ In progress'}\n`;
      content += `- Content Generation: ${progress.contentGenerationComplete ? '✅ Complete' : '⏳ In progress'}\n`;
      content += `- SEO Optimization: ${progress.seoOptimizationComplete ? '✅ Complete' : '⏳ In progress'}\n`;
      content += '\nPlease wait while our agents collaborate to generate high-quality content for you.';
    }

    // Add all agents as contributors from agent states or messages
    const agentSet = new Set<string>();

    // Get contributors from agent states
    if (state.agentStates) {
      Object.keys(state.agentStates).forEach(agentId => {
        agentSet.add(agentId);
      });
    }

    // Add contributors from messages if available
    if (state.messages && Array.isArray(state.messages)) {
      state.messages.forEach((msg: any) => {
        if (msg.from && msg.from !== 'system' && msg.from !== 'user') {
          agentSet.add(msg.from);
        }
      });
    }

    contributors = Array.from(agentSet);

    // Add remaining metadata
    metadata = {
      ...metadata,
      contentType: state.contentType || 'blog-article',
      targetAudience: state.targetAudience || 'general',
      tone: state.tone || 'professional',
      wordCount: content ? content.split(/\s+/).length : 0,
      readingTime: content ? Math.ceil(content.split(/\s+/).length / 200) : 0, // Approximate reading time in minutes
      topic: state.topic || ''
    };

    return {
      title,
      content,
      seoScore,
      generatedAt,
      contributors,
      metadata
    };
  };

  // Helper function to combine state messages with agent logs for a complete view
  // Memoize the result to prevent unnecessary re-renders
  const combinedMessages = useMemo(() => {
    const stateMessages = state?.messages || [];

    // Convert logs to a message format compatible with AgentDiscussionPanel
    const logMessages = logs.map(log => {
      // If the log has additional message properties, use them
      if ('from' in log && 'to' in log) {
        return log as any;
      }

      // Otherwise create a message structure from the log
      return {
        id: ('id' in log ? log.id : undefined) || `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        timestamp: log.timestamp,
        from: log.agent || 'system',
        to: 'system', // Default recipient
        type: log.level === 'error' ? 'ERROR' : 'INFO',
        content: ('content' in log ? log.content : undefined) || { text: log.message },
        conversationId: sessionId
      };
    });

    // Combine and deduplicate by ID
    const combined = [...stateMessages];

    // Only add logs that don't already exist in state messages
    const existingIds = new Set(stateMessages.map((m: any) => m.id));
    logMessages.forEach(logMsg => {
      const msgId = 'id' in logMsg ? logMsg.id : undefined;
      if (msgId && !existingIds.has(msgId)) {
        combined.push(logMsg as any);
      }
    });

    return combined;
  }, [state?.messages, logs, sessionId]);

  // Memoize the artifacts extraction to prevent unnecessary re-renders
  const extractedArtifacts = useMemo(() => {
    return extractArtifactsFromState(state);
  }, [state]);

  // Define the article type to match what ArticleViewer expects
  interface Article {
    title: string;
    content: string;
    seoScore?: number;
    generatedAt?: string;
    contributors?: string[];
    metadata?: {
      wordCount?: number;
      readingTime?: number;
      keywords?: string[];
      targetAudience?: string;
      contentType?: string;
      [key: string]: any;
    };
  }

  // Use the previously defined getArticleData function to get article data
  // Memoize to prevent unnecessary re-renders
  const articleData: Article = useMemo(() => {
    return state ? getArticleData() : {
      title: 'New Article',
      content: 'Content will appear here once generated...',
      metadata: {
        contentType: 'blog-article',
        targetAudience: 'general'
      }
    };
  }, [state]);

  // Memoize the notification close handler
  const handleCloseNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, open: false }));
  }, []);

  // Memoize the artifact feedback handler to prevent unnecessary re-renders
  const handleArtifactFeedback = useCallback(async (artifactId: string, feedback: string) => {
    console.log('Sending feedback for artifact:', artifactId, feedback);
    if (!sessionId || !feedback.trim() || !artifactId) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/collaborative-agents/artifact-feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          artifactId,
          feedback: feedback.trim(),
          timestamp: new Date().toISOString(),
          from: 'user'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to send artifact feedback: ${response.status}`);
      }

      await response.json(); // Response data not used

      // Log the feedback action
      if (addLog) {
        addLog({
          level: 'info',
          message: `Feedback provided for artifact: ${artifactId}`,
          agent: 'User',
          phase: 'feedback',
          context: {
            artifactId,
            feedback
          }
        });
      }

      setNotification({
        open: true,
        message: 'Artifact feedback sent successfully!',
        severity: 'success'
      });

      // Refresh the state to reflect any changes
      fetchState();
    } catch (err) {
      console.error('Error sending artifact feedback:', err);
      setError(err instanceof Error ? err.message : 'Failed to send artifact feedback');
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to send artifact feedback',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, [sessionId, addLog, fetchState]);

  // Initial load
  useEffect(() => {
    if (sessionId) {
      fetchState();

      // Set up polling for active sessions
      const intervalId = setInterval(() => {
        if (state?.status === 'active' || state?.status === 'processing') {
          console.log('Auto-refreshing active session');
          fetchState();
        }
      }, 10000); // Poll every 10 seconds

      return () => clearInterval(intervalId);
    } else {
      setLoading(false);
    }
  }, [sessionId]);

  // If no session ID, show the creation form
  if (!sessionId) {
    return (
      <Container maxWidth="md">
        <Box sx={{ mt: 4, mb: 6 }}>
          <Typography variant="h4" gutterBottom>
            Create New Collaborative Content
          </Typography>
          <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 4 }}>
            Fill out the form below to start a new collaborative content generation session.
          </Typography>

          <Paper sx={{ p: 3 }}>
            <Box component="form" noValidate>
              <Box display="grid" gridTemplateColumns={{ xs: "1fr", md: "repeat(2, 1fr)" }} gap={2}>
                <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                  <TextField
                    fullWidth
                    label="Content Topic"
                    variant="outlined"
                    placeholder="Enter the main topic or title"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    required
                    error={formError?.includes('topic')}
                    sx={{ mb: 2 }}
                  />
                </Box>

                <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Content Type</InputLabel>
                    <Select
                      value={contentType}
                      label="Content Type"
                      onChange={(e) => setContentType(e.target.value as string)}
                    >
                      <MenuItem value="blog-article">Blog Article</MenuItem>
                      <MenuItem value="product-description">Product Description</MenuItem>
                      <MenuItem value="buying-guide">Buying Guide</MenuItem>
                      <MenuItem value="how-to-guide">How-To Guide</MenuItem>
                      <MenuItem value="case-study">Case Study</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                  <TextField
                    fullWidth
                    label="Target Audience"
                    variant="outlined"
                    placeholder="Who is this content for?"
                    value={targetAudience}
                    onChange={(e) => setTargetAudience(e.target.value)}
                    required
                    error={formError?.includes('audience')}
                    sx={{ mb: 2 }}
                  />
                </Box>

                <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Content Tone</InputLabel>
                    <Select
                      value={tone}
                      label="Content Tone"
                      onChange={(e) => setTone(e.target.value as string)}
                    >
                      <MenuItem value="professional">Professional</MenuItem>
                      <MenuItem value="conversational">Conversational</MenuItem>
                      <MenuItem value="authoritative">Authoritative</MenuItem>
                      <MenuItem value="informative">Informative</MenuItem>
                      <MenuItem value="persuasive">Persuasive</MenuItem>
                      <MenuItem value="technical">Technical</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Box gridColumn={{ xs: "span 12", md: "span 6" }}>
                  <TextField
                    fullWidth
                    label="Keywords"
                    variant="outlined"
                    placeholder="Enter keywords separated by commas"
                    value={keywords}
                    onChange={(e) => setKeywords(e.target.value)}
                    required
                    error={formError?.includes('keyword')}
                    helperText="Enter keywords separated by commas"
                    sx={{ mb: 2 }}
                  />
                </Box>
              </Box>
              <Box gridColumn={{ xs: "span 12" }}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={startCollaboration}
                    disabled={loading}
                    endIcon={loading ? <CircularProgress size={20} /> : null}
                  >
                    {loading ? 'Creating...' : 'Create New Session'}
                  </Button>
                </Box>

                {formError && (
                  <Box sx={{ mt: 2 }}>
                    <Alert severity="error">{formError}</Alert>
                  </Box>
                )}
              </Box>
            </Box>
          </Paper>
        </Box>
      </Container>
    );

  }

  // Show loading state
  if (loading && !state) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading collaboration session...
        </Typography>
      </Box>
    );
  }

  // Show error state
  if (error && !state) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchState}
        >
          Retry
        </Button>
      </Box>
    );
  }



  // Enhanced typing for log messages
  interface EnhancedAgentLogEntry extends AgentLogEntry {
    // Add missing properties that might exist in the data
    id?: string;
    from?: string;
    to?: string | string[];
    content?: any;
    type?: string;
  }

  // Unused function - commented out to avoid TypeScript errors
  /*
  const renderLogMessage = (message: EnhancedAgentLogEntry) => {
    // For data messages, show a summary instead of full content
    if (message.type === 'data') {
      return <Typography variant="body2">Data transfer: {String(message.context || 'No context provided')}</Typography>;
    }

    // For other messages, show the message
    return (
      <Typography variant="body2">
        {message.message || 'No message content'}
      </Typography>
    );
  };
  */

  // Type definition for different message formats (commented out as it's not used)
  /*
  type EnhancedMessage = {
    id: string;
    timestamp: Date | string;
    from: string;
    to: string | string[];
    type: string;
    content?: any;
    conversationId?: string | null;
    message?: string;
    context?: string;
  };
  */

  // Unused function - commented out to avoid TypeScript errors
  /*
  const renderMessage = (message: EnhancedMessage) => {
    // Format timestamp
    const formattedTime = new Date(message.timestamp).toLocaleTimeString();

    // Determine message type styling
    let messageStyle = {};

    // Check for message direction
    const direction = message.from === 'user' ? 'outgoing' : 'incoming';

    // Type indicator
    const isConsultation = message.type?.includes('CONSULTATION');
    const isFeedback = message.type?.includes('FEEDBACK');
    const isDiscussion = message.type?.includes('DISCUSSION');
    const isBroadcast = message.type?.includes('BROADCAST');

    return (
      <Box
        className={`message ${direction}`}
        sx={{
          p: 1,
          mb: 1,
          backgroundColor: isConsultation ? '#e3f2fd' :
                           isFeedback ? '#e8f5e9' :
                           isDiscussion ? '#f3e5f5' :
                           isBroadcast ? '#fffde7' :
                           direction === 'outgoing' ? '#e8eaf6' : '#f5f5f5',
          borderRadius: 1,
          maxWidth: '90%',
          alignSelf: direction === 'outgoing' ? 'flex-end' : 'flex-start',
          ...messageStyle
        }}
      >
        <Box className="message-header" sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
          <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
            {message.from} → {Array.isArray(message.to) ? message.to.join(', ') : message.to}
          </Typography>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            {formattedTime}
          </Typography>
        </Box>

        <Typography variant="body2">
          <strong>{message.type}</strong>
        </Typography>

        {message.content ? (
          <Box className="message-content" sx={{ mt: 1 }}>
            {typeof message.content === 'string' ? (
              <Typography variant="body2">{message.content}</Typography>
            ) : (
              <pre style={{ overflow: 'auto', fontSize: '0.8rem' }}>
                {JSON.stringify(message.content, null, 2)}
              </pre>
            )}
          </Box>
        ) : null}
      </Box>
    );
  };
  */

  return (
    <Container maxWidth="xl">
      <Box sx={{ mt: 2, mb: 4 }}>
        <Box display="grid" gridTemplateColumns="1fr" gap={2} sx={{ alignItems: "center" }}>
          <Box>
            <Typography variant="h4">
              {state?.topic || 'Collaborative Content Creation'}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Session ID: {sessionId}
            </Typography>
          </Box>
          <Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchState}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          </Box>
        </Box>

        <Paper sx={{ mt: 3, mb: 3, p: 2 }}>
          <Box display="grid" gridTemplateColumns={{ xs: "1fr", md: "repeat(3, 1fr)" }} gap={2}>
            <Box>
              <Typography variant="subtitle2" color="text.secondary">
                Content Type
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {state?.contentType === 'blog-article' ? 'Blog Article' :
                 state?.contentType === 'product-page' ? 'Product Page' :
                 state?.contentType || 'Blog Article'}
              </Typography>
            </Box>
            <Box>
              <Typography variant="subtitle2" color="text.secondary">
                Target Audience
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {state?.targetAudience || 'Not specified'}
              </Typography>
            </Box>
            <Box>
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {state?.status || 'In Progress'}
              </Typography>
            </Box>
          </Box>
        </Paper>

        {sessionId && state && (
          <AgentWorkflowVisualizer
            sessionId={sessionId}
            sessionState={state}
            loading={loading}
          />
        )}

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="dashboard tabs"
          >
            <Tab label="Generated Article" icon={<ArticleIcon />} iconPosition="start" />
            <Tab label="Agent Discussions" icon={<ChatIcon />} iconPosition="start" />
            <Tab label="Reasoning Process" icon={<PsychologyIcon />} iconPosition="start" />
            <Tab label="Artifacts" icon={<ManageSearchIcon />} iconPosition="start" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <ArticleViewer
            article={articleData}
            sessionId={sessionId}
            onSave={handleArticleSave}
          />

          <Box sx={{ mt: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <Typography variant="h6" gutterBottom>
              Provide Feedback
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Enter your feedback on the generated content..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              variant="outlined"
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              disabled={!feedback.trim() || loading}
              onClick={sendArticleFeedback}
            >
              Send Feedback
            </Button>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <AgentDiscussionPanel
            messages={combinedMessages}
            sessionId={sessionId}
            showInput={false}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <ChainOfThoughtVisualizer
            messages={state?.messages || []}
            artifacts={state?.artifacts || {}}
            consultations={state?.consultations || []}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Artifacts</Typography>
            <Box>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={handleRefreshArtifacts}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                {loading ? 'Refreshing...' : 'Refresh Artifacts'}
              </Button>

              <Button
                variant="contained"
                color="warning"
                onClick={handleForceArtifacts}
                disabled={loading}
              >
                Force Display Artifacts
              </Button>
            </Box>
          </Box>

          <EnhancedArtifactGalleryV2
            artifacts={extractedArtifacts}
            refreshContent={fetchState}
            loading={loading}
            onSendFeedback={handleArtifactFeedback}
          />
        </TabPanel>
      </Box>

      {/* Agent Activity Logs Panel */}
      <Box sx={{ mt: 2, mb: 2, position: 'relative' }}>
        <Paper
          elevation={2}
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Box
            sx={{
              p: 1,
              bgcolor: 'primary.main',
              color: 'white',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              cursor: 'pointer'
            }}
            onClick={toggleLogsPanel}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <VisibilityIcon sx={{ mr: 1 }} />
              <Typography variant="subtitle1">
                Agent Activity Logs {logsLoading && <CircularProgress size={16} color="inherit" sx={{ ml: 1 }} />}
              </Typography>
            </Box>
            <IconButton size="small" sx={{ color: 'white' }}>
              {showLogs ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
          </Box>

          <Collapse in={showLogs}>
            {logsError ? (
              <Box sx={{ p: 2 }}>
                <Alert severity="error">{logsError}</Alert>
              </Box>
            ) : (
              <AgentActivityLogger
                sessionId={sessionId || ''}
                logs={logs}
                showTimestamps={true}
                autoScroll={true}
                compact={false}
              />
            )}
          </Collapse>
        </Paper>
      </Box>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
      >
        <Alert
          severity={notification.severity}
          onClose={handleCloseNotification}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default IntegratedCollaborativeDashboard;
