'use client';

import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Chip,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import dynamic from 'next/dynamic';
import { IterativeMessage } from '../../app/(payload)/api/agents/collaborative-iteration/types';

// Dynamically import ForceGraph2D with no SSR to avoid window is not defined error
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), {
  ssr: false,
  loading: () => <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
    <CircularProgress />
  </Box>
});

interface AgentCollaborationNetworkProps {
  messages: IterativeMessage[];
  artifacts?: any[] | Record<string, any>;
  onRefresh?: () => void;
  loading?: boolean;
}

interface GraphNode {
  id: string;
  name: string;
  val: number;
  color: string;
  type: 'agent' | 'artifact';
  artifactType?: string;
}

interface GraphLink {
  source: string;
  target: string;
  value: number;
  type: string;
  messages?: IterativeMessage[];
}

/**
 * Component to visualize agent collaboration as a network graph
 * Shows interactions between agents and artifacts
 */
const AgentCollaborationNetwork: React.FC<AgentCollaborationNetworkProps> = ({
  messages,
  artifacts = [],
  onRefresh,
  loading = false
}) => {
  const graphRef = useRef<any>();
  const [graphData, setGraphData] = useState<{ nodes: GraphNode[], links: GraphLink[] }>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedLink, setSelectedLink] = useState<GraphLink | null>(null);
  const [hoveredNode, setHoveredNode] = useState<GraphNode | null>(null);

  // Convert artifacts to array if it's an object
  const artifactsArray = Array.isArray(artifacts)
    ? artifacts
    : Object.values(artifacts);

  // Agent colors
  const agentColors: Record<string, string> = {
    'market-research': '#2196f3',
    'seo-keyword': '#ff9800',
    'content-strategy': '#3f51b5',
    'content-generation': '#009688',
    'seo-optimization': '#f44336',
    'system': '#9e9e9e',
    'user': '#4caf50'
  };

  // Artifact colors
  const artifactColors: Record<string, string> = {
    'market-research-report': '#90caf9',
    'seo-keyword-analysis': '#ffcc80',
    'content-strategy-plan': '#9fa8da',
    'content-draft': '#80cbc4',
    'seo-optimization-report': '#ef9a9a',
    'final-content': '#a5d6a7',
    'default': '#e0e0e0'
  };

  // Agent display names
  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Generation',
    'seo-optimization': 'SEO Optimization',
    'system': 'System',
    'user': 'User'
  };

  // Build graph data from messages and artifacts
  useEffect(() => {
    if (!messages || messages.length === 0) return;

    const nodes: GraphNode[] = [];
    const links: GraphLink[] = [];
    const nodeMap = new Map<string, GraphNode>();
    const linkMap = new Map<string, GraphLink>();

    // Add agent nodes
    const addAgentNode = (agentId: string) => {
      if (!nodeMap.has(agentId)) {
        const node: GraphNode = {
          id: agentId,
          name: agentNames[agentId] || agentId,
          val: 5, // Base size
          color: agentColors[agentId] || '#999',
          type: 'agent'
        };
        nodes.push(node);
        nodeMap.set(agentId, node);
      }
    };

    // Add artifact nodes
    artifactsArray.forEach(artifact => {
      if (!artifact.id) return;

      const artifactType = artifact.type || 'default';
      const node: GraphNode = {
        id: artifact.id,
        name: artifact.name || `Artifact ${artifact.id.substring(0, 6)}`,
        val: 3, // Smaller than agents
        color: artifactColors[artifactType] || artifactColors.default,
        type: 'artifact',
        artifactType
      };
      nodes.push(node);
      nodeMap.set(artifact.id, node);

      // Add link from creator to artifact
      if (artifact.createdBy) {
        addAgentNode(artifact.createdBy);

        const linkId = `${artifact.createdBy}-${artifact.id}`;
        links.push({
          source: artifact.createdBy,
          target: artifact.id,
          value: 2,
          type: 'created'
        });
        linkMap.set(linkId, links[links.length - 1]);
      }
    });

    // Process messages to create links
    messages.forEach(message => {
      // Ensure nodes exist
      addAgentNode(message.from);

      // Handle messages to multiple agents
      const targets = Array.isArray(message.to) ? message.to : [message.to];

      targets.forEach(target => {
        addAgentNode(target);

        // Create or update link
        const linkId = `${message.from}-${target}`;
        if (linkMap.has(linkId)) {
          const link = linkMap.get(linkId)!;
          link.value += 1;
          link.messages = [...(link.messages || []), message];
        } else {
          const link: GraphLink = {
            source: message.from,
            target,
            value: 1,
            type: 'message',
            messages: [message]
          };
          links.push(link);
          linkMap.set(linkId, link);
        }
      });

      // If message references an artifact, add link to it
      if (message.artifactId && nodeMap.has(message.artifactId)) {
        const linkId = `${message.from}-${message.artifactId}`;
        if (linkMap.has(linkId)) {
          const link = linkMap.get(linkId)!;
          link.value += 1;
        } else {
          links.push({
            source: message.from,
            target: message.artifactId,
            value: 1,
            type: 'referenced'
          });
          linkMap.set(linkId, links[links.length - 1]);
        }
      }
    });

    // Update node sizes based on connections
    nodes.forEach(node => {
      const nodeLinks = links.filter(
        link => link.source === node.id || link.target === node.id
      );
      node.val = Math.max(node.val, 3 + nodeLinks.length * 0.5);
    });

    setGraphData({ nodes, links });
  }, [messages, artifactsArray]);

  // Handle node click
  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node === selectedNode ? null : node);
    setSelectedLink(null);

    if (graphRef.current && node) {
      graphRef.current.centerAt(
        node.x,
        node.y,
        1000
      );
      graphRef.current.zoom(2, 1000);
    }
  };

  // Handle link click
  const handleLinkClick = (link: GraphLink) => {
    setSelectedLink(link === selectedLink ? null : link);
    setSelectedNode(null);
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString();
    } catch (e) {
      return timestamp;
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          Agent Collaboration Network
        </Typography>

        {onRefresh && (
          <IconButton onClick={onRefresh} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        )}
      </Box>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
        <Chip
          label="Agents"
          size="small"
          sx={{ bgcolor: '#3f51b5', color: 'white' }}
        />
        <Chip
          label="Artifacts"
          size="small"
          sx={{ bgcolor: '#e0e0e0' }}
        />
        <Chip
          label="Messages"
          size="small"
          variant="outlined"
        />
      </Box>

      <Box sx={{ position: 'relative', flexGrow: 1, minHeight: '400px', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : graphData.nodes.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Typography variant="body1" color="text.secondary">
              No collaboration data available
            </Typography>
          </Box>
        ) : (
          <ForceGraph2D
            ref={graphRef}
            graphData={graphData}
            nodeLabel={(node: GraphNode) => `${node.name} (${node.type})`}
            nodeColor={(node: GraphNode) => node.color}
            nodeVal={(node: GraphNode) => node.val}
            linkWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
            linkDirectionalParticles={4}
            linkDirectionalParticleWidth={(link: GraphLink) => Math.sqrt(link.value) * 0.5}
            onNodeClick={handleNodeClick}
            onLinkClick={handleLinkClick}
            onNodeHover={setHoveredNode}
            cooldownTicks={100}
            linkDirectionalParticleSpeed={0.01}
            nodeCanvasObject={(node, ctx, globalScale) => {
              const label = node.name;
              const fontSize = 12/globalScale;
              ctx.font = `${fontSize}px Sans-Serif`;
              const textWidth = ctx.measureText(label).width;
              const bckgDimensions = [textWidth, fontSize].map(n => n + fontSize * 0.2);

              ctx.fillStyle = node.color;
              ctx.beginPath();
              ctx.arc(node.x!, node.y!, node.val, 0, 2 * Math.PI, false);
              ctx.fill();

              if (node === selectedNode || node === hoveredNode) {
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2/globalScale;
                ctx.stroke();

                // Draw label for selected/hovered node
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.fillRect(
                  node.x! - bckgDimensions[0] / 2,
                  node.y! - bckgDimensions[1] / 2 - 15,
                  bckgDimensions[0],
                  bckgDimensions[1]
                );

                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '#000';
                ctx.fillText(
                  label,
                  node.x!,
                  node.y! - 15
                );
              }
            }}
          />
        )}
      </Box>

      {/* Details panel */}
      <Box sx={{ mt: 2, maxHeight: '200px', overflow: 'auto' }}>
        {selectedNode && (
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                {selectedNode.name}
              </Typography>
              <Chip
                size="small"
                label={selectedNode.type === 'agent' ? 'Agent' : 'Artifact'}
                sx={{
                  bgcolor: selectedNode.type === 'agent' ? '#3f51b5' : '#e0e0e0',
                  color: selectedNode.type === 'agent' ? 'white' : 'black',
                  mb: 1
                }}
              />

              {selectedNode.type === 'artifact' && selectedNode.artifactType && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Type: {selectedNode.artifactType}
                </Typography>
              )}

              <Typography variant="body2">
                Connections: {graphData.links.filter(
                  link => link.source === selectedNode.id || link.target === selectedNode.id
                ).length}
              </Typography>
            </CardContent>
          </Card>
        )}

        {selectedLink && (
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Connection Details
              </Typography>

              <Typography variant="body2" gutterBottom>
                From: {typeof selectedLink.source === 'string'
                  ? selectedLink.source
                  : selectedLink.source.id}
              </Typography>

              <Typography variant="body2" gutterBottom>
                To: {typeof selectedLink.target === 'string'
                  ? selectedLink.target
                  : selectedLink.target.id}
              </Typography>

              <Typography variant="body2" gutterBottom>
                Type: {selectedLink.type}
              </Typography>

              <Typography variant="body2" gutterBottom>
                Interactions: {selectedLink.value}
              </Typography>

              {selectedLink.messages && selectedLink.messages.length > 0 && (
                <>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="body2" gutterBottom>
                    Recent Messages:
                  </Typography>

                  {selectedLink.messages.slice(0, 3).map((message, index) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(message.timestamp)}
                      </Typography>
                      <Typography variant="body2" sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}>
                        {typeof message.content === 'string'
                          ? message.content
                          : JSON.stringify(message.content)}
                      </Typography>
                    </Box>
                  ))}
                </>
              )}
            </CardContent>
          </Card>
        )}
      </Box>
    </Paper>
  );
};

export default AgentCollaborationNetwork;
