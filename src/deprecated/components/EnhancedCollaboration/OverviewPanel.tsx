// src/components/EnhancedCollaboration/OverviewPanel.tsx
import React, { useEffect } from 'react';

interface Goal {
  id?: string;
  title?: string;
  description?: string;
  status?: string;
  progress?: number;
  [key: string]: any;
}

interface CollaborationState {
  id?: string;
  topic?: string;
  contentType?: string;
  targetAudience?: string;
  tone?: string;
  keywords?: string[] | string;
  progress?: number;
  currentGoal?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
  duration?: string;
  goals?: Goal[];
  [key: string]: any;
}

interface OverviewPanelProps {
  collaborationState: CollaborationState | null | undefined;
}

const OverviewPanel: React.FC<OverviewPanelProps> = ({ collaborationState }) => {
  // Debug the incoming state
  useEffect(() => {
    console.log('OverviewPanel received state:', {
      hasState: !!collaborationState,
      stateKeys: collaborationState ? Object.keys(collaborationState) : [],
      hasKeywords: !!(collaborationState?.keywords),
      keywordsType: collaborationState?.keywords ? typeof collaborationState.keywords : 'undefined',
      keywordsIsArray: Array.isArray(collaborationState?.keywords)
    });
  }, [collaborationState]);
  
  // If no state is provided, show a placeholder
  if (!collaborationState) {
    return (
      <div className="overview-panel">
        <h2 className="panel-title">Collaboration Overview</h2>
        <div className="no-data">
          <p>No collaboration data available.</p>
        </div>
      </div>
    );
  }
  
  // Helper function to format keywords
  const formatKeywords = () => {
    if (!collaborationState.keywords) {
      return 'None';
    }
    
    if (typeof collaborationState.keywords === 'string') {
      return collaborationState.keywords;
    }
    
    if (Array.isArray(collaborationState.keywords)) {
      return collaborationState.keywords.join(', ');
    }
    
    return 'None';
  };
  
  // Helper function to format status
  const formatStatus = () => {
    if (!collaborationState.status) {
      return 'Unknown';
    }
    
    return collaborationState.status.charAt(0).toUpperCase() + collaborationState.status.slice(1);
  };
  
  // Helper function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) {
      return 'Unknown';
    }
    
    try {
      return new Date(dateString).toLocaleString();
    } catch (error) {
      return 'Invalid date';
    }
  };
  
  // Helper function to get goal status class
  const getGoalStatusClass = (status?: string) => {
    if (!status) return '';
    
    switch (status.toLowerCase()) {
      case 'completed':
        return 'completed';
      case 'active':
        return 'active';
      case 'pending':
        return 'pending';
      case 'failed':
        return 'failed';
      default:
        return '';
    }
  };

  return (
    <div className="overview-panel">
      <h2 className="panel-title">Collaboration Overview</h2>
      
      <div className="overview-grid">
        <div className="overview-section">
          <h3>Session Details</h3>
          <div className="detail-item">
            <span className="label">Topic:</span>
            <span className="value">{collaborationState.topic || 'Not specified'}</span>
          </div>
          <div className="detail-item">
            <span className="label">Content Type:</span>
            <span className="value">{collaborationState.contentType || 'Not specified'}</span>
          </div>
          <div className="detail-item">
            <span className="label">Target Audience:</span>
            <span className="value">{collaborationState.targetAudience || 'Not specified'}</span>
          </div>
          <div className="detail-item">
            <span className="label">Tone:</span>
            <span className="value">{collaborationState.tone || 'Not specified'}</span>
          </div>
          <div className="detail-item">
            <span className="label">Keywords:</span>
            <span className="value">{formatKeywords()}</span>
          </div>
        </div>
        
        <div className="overview-section">
          <h3>Progress</h3>
          <div className="detail-item">
            <span className="label">Status:</span>
            <span className={`value status-${collaborationState.status?.toLowerCase() || 'unknown'}`}>
              {formatStatus()}
            </span>
          </div>
          <div className="detail-item">
            <span className="label">Progress:</span>
            <div className="progress-container">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${collaborationState.progress || 0}%` }}
                ></div>
              </div>
              <span className="progress-text">{collaborationState.progress || 0}%</span>
            </div>
          </div>
          <div className="detail-item">
            <span className="label">Current Goal:</span>
            <span className="value">{collaborationState.currentGoal || 'None'}</span>
          </div>
          <div className="detail-item">
            <span className="label">Started:</span>
            <span className="value">{formatDate(collaborationState.startTime)}</span>
          </div>
          {collaborationState.endTime && (
            <div className="detail-item">
              <span className="label">Completed:</span>
              <span className="value">{formatDate(collaborationState.endTime)}</span>
            </div>
          )}
          <div className="detail-item">
            <span className="label">Duration:</span>
            <span className="value">{collaborationState.duration || 'In progress'}</span>
          </div>
        </div>
      </div>
      
      {collaborationState.goals && collaborationState.goals.length > 0 && (
        <div className="goals-section">
          <h3>Goals</h3>
          <div className="goals-list">
            {collaborationState.goals.map((goal, index) => (
              <div key={goal.id || index} className={`goal-item ${getGoalStatusClass(goal.status)}`}>
                <div className="goal-status-indicator"></div>
                <div className="goal-content">
                  <h4>{goal.title || `Goal ${index + 1}`}</h4>
                  <p>{goal.description || 'No description provided'}</p>
                  <div className="goal-meta">
                    <span className="goal-status">Status: {goal.status || 'Unknown'}</span>
                    {goal.progress !== undefined && (
                      <span className="goal-progress">Progress: {goal.progress}%</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <style jsx>{`
        .overview-panel {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }
        
        .panel-title {
          font-size: 1.5rem;
          margin-bottom: 1rem;
          color: #111827;
        }
        
        .overview-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1.5rem;
        }
        
        .overview-section {
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1.25rem;
        }
        
        .overview-section h3 {
          font-size: 1.125rem;
          margin-bottom: 1rem;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.5rem;
        }
        
        .detail-item {
          display: flex;
          margin-bottom: 0.75rem;
          align-items: center;
        }
        
        .label {
          font-size: 0.875rem;
          color: #6b7280;
          width: 120px;
          flex-shrink: 0;
        }
        
        .value {
          font-size: 0.875rem;
          color: #111827;
          font-weight: 500;
          flex-grow: 1;
        }
        
        .status-active {
          color: #0070f3;
        }
        
        .status-completed {
          color: #10b981;
        }
        
        .status-paused {
          color: #f59e0b;
        }
        
        .status-failed {
          color: #ef4444;
        }
        
        .status-unknown {
          color: #9ca3af;
        }
        
        .progress-container {
          display: flex;
          align-items: center;
          flex-grow: 1;
          gap: 0.5rem;
        }
        
        .progress-bar {
          height: 8px;
          background-color: #e5e7eb;
          border-radius: 4px;
          overflow: hidden;
          flex-grow: 1;
        }
        
        .progress-fill {
          height: 100%;
          background-color: #0070f3;
          border-radius: 4px;
        }
        
        .progress-text {
          font-size: 0.75rem;
          color: #6b7280;
          min-width: 40px;
          text-align: right;
        }
        
        .goals-section {
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1.25rem;
        }
        
        .goals-section h3 {
          font-size: 1.125rem;
          margin-bottom: 1rem;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.5rem;
        }
        
        .goals-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
        
        .goal-item {
          display: flex;
          background-color: #f9fafb;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e5e7eb;
        }
        
        .goal-status-indicator {
          width: 4px;
          background-color: #9ca3af;
        }
        
        .goal-item.completed .goal-status-indicator {
          background-color: #10b981;
        }
        
        .goal-item.active .goal-status-indicator {
          background-color: #0070f3;
        }
        
        .goal-item.pending .goal-status-indicator {
          background-color: #f59e0b;
        }
        
        .goal-item.failed .goal-status-indicator {
          background-color: #ef4444;
        }
        
        .goal-content {
          flex-grow: 1;
          padding: 1rem;
        }
        
        .goal-content h4 {
          font-size: 1rem;
          margin-bottom: 0.5rem;
          color: #111827;
        }
        
        .goal-content p {
          font-size: 0.875rem;
          color: #4b5563;
          margin-bottom: 0.75rem;
        }
        
        .goal-meta {
          display: flex;
          justify-content: space-between;
          font-size: 0.75rem;
          color: #6b7280;
        }
        
        .no-data {
          background-color: #f9fafb;
          border-radius: 8px;
          padding: 2rem;
          text-align: center;
          color: #6b7280;
        }
      `}</style>
    </div>
  );
};

export default OverviewPanel;
