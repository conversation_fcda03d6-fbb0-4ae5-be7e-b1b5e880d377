'use client'

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Tooltip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import SpeedIcon from '@mui/icons-material/Speed';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import SearchIcon from '@mui/icons-material/Search';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

// Define the props interface
interface QualityMetricsPanelProps {
  qualityAssessment: {
    overallScore: number;
    meetsStandards: boolean;
    dimensions: {
      content: number;
      structure: number;
      seo: number;
      readability: number;
      engagement: number;
    };
    strengths: string[];
    weaknesses: string[];
    suggestions: string[];
    timestamp: string;
    id: string;
  };
  contentMetrics?: {
    readability: {
      fleschKincaidScore: number;
      smogIndex?: number;
      colemanLiauIndex?: number;
      automatedReadabilityIndex?: number;
      readingTimeMinutes: number;
    };
    structure: {
      headingCount: number;
      paragraphCount: number;
      listCount: number;
      structureScore: number;
    };
    engagement: {
      engagementScore: number;
      questionCount?: number;
      callToActionCount?: number;
    };
    coherence: {
      coherenceScore: number;
      topicConsistency?: number;
    };
    seo: {
      seoScore: number;
      keywordDensity?: Record<string, number>;
    };
  };
  seoOptimization?: {
    overallScore: number;
    onPageSeo: {
      titleTag: { score: number };
      metaDescription: { score: number };
      headings: { score: number };
      content: { score: number };
    };
    semanticSeo: {
      score: number;
      topicClusters: string[];
    };
    serpFeatures: {
      score: number;
      featuredSnippetPotential: number;
      faqSchemaPotential: number;
      howToSchemaPotential: number;
    };
  };
}

/**
 * Component to display comprehensive quality metrics for content
 */
const QualityMetricsPanel: React.FC<QualityMetricsPanelProps> = ({
  qualityAssessment,
  contentMetrics,
  seoOptimization
}) => {
  const [expandedSection, setExpandedSection] = useState<string | false>('panel1');

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? panel : false);
  };

  // Helper function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'success.main';
    if (score >= 0.6) return 'warning.main';
    return 'error.main';
  };

  // Helper function to format score as percentage
  const formatScore = (score: number) => `${Math.round(score * 100)}%`;

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        Content Quality Assessment
      </Typography>
      <Divider sx={{ mb: 3 }} />

      {/* Overall Score */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: `conic-gradient(${getScoreColor(qualityAssessment.overallScore)} ${qualityAssessment.overallScore * 360}deg, #e0e0e0 0)`,
              mr: 2
            }}
          >
            <Box
              sx={{
                width: 70,
                height: 70,
                borderRadius: '50%',
                bgcolor: 'background.paper',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {formatScore(qualityAssessment.overallScore)}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="h6">Overall Quality Score</Typography>
            <Typography variant="body2" color="text.secondary">
              Last assessed: {new Date(qualityAssessment.timestamp).toLocaleString()}
            </Typography>
          </Box>
        </Box>
        <Chip
          label={qualityAssessment.meetsStandards ? 'Meets Quality Standards' : 'Needs Improvement'}
          color={qualityAssessment.meetsStandards ? 'success' : 'warning'}
          sx={{ fontWeight: 'bold' }}
        />
      </Box>

      {/* Dimension Scores */}
      <Typography variant="h6" gutterBottom>
        Quality Dimensions
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {Object.entries(qualityAssessment.dimensions).map(([dimension, score]) => (
          <Grid item xs={12} sm={6} md={4} lg={2.4} key={dimension}>
            <Card variant="outlined">
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {dimension === 'content' && <MenuBookIcon sx={{ mr: 1, color: getScoreColor(score) }} />}
                  {dimension === 'structure' && <FormatListBulletedIcon sx={{ mr: 1, color: getScoreColor(score) }} />}
                  {dimension === 'seo' && <SearchIcon sx={{ mr: 1, color: getScoreColor(score) }} />}
                  {dimension === 'readability' && <VisibilityIcon sx={{ mr: 1, color: getScoreColor(score) }} />}
                  {dimension === 'engagement' && <TrendingUpIcon sx={{ mr: 1, color: getScoreColor(score) }} />}
                  <Typography variant="subtitle1" sx={{ textTransform: 'capitalize' }}>
                    {dimension}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={score * 100}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    mb: 1,
                    bgcolor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: getScoreColor(score)
                    }
                  }}
                />
                <Typography variant="h6" align="right">
                  {formatScore(score)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Strengths, Weaknesses, and Suggestions */}
      <Accordion expanded={expandedSection === 'panel1'} onChange={handleChange('panel1')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleIcon sx={{ mr: 1, color: 'success.main' }} />
            <Typography variant="subtitle1">Strengths ({qualityAssessment.strengths.length})</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {qualityAssessment.strengths.map((strength, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={strength} />
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>

      <Accordion expanded={expandedSection === 'panel2'} onChange={handleChange('panel2')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ErrorIcon sx={{ mr: 1, color: 'error.main' }} />
            <Typography variant="subtitle1">Weaknesses ({qualityAssessment.weaknesses.length})</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {qualityAssessment.weaknesses.map((weakness, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText primary={weakness} />
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>

      <Accordion expanded={expandedSection === 'panel3'} onChange={handleChange('panel3')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ mr: 1, color: 'info.main' }} />
            <Typography variant="subtitle1">Improvement Suggestions ({qualityAssessment.suggestions.length})</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {qualityAssessment.suggestions.map((suggestion, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <InfoIcon color="info" />
                </ListItemIcon>
                <ListItemText primary={suggestion} />
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default QualityMetricsPanel;
