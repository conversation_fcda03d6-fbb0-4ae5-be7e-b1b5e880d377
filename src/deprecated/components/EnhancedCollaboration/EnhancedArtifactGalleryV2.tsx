// src/components/EnhancedCollaboration/EnhancedArtifactGalleryV2.tsx

import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardActions,
  Button,
  Divider,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Avatar
} from '@mui/material';
import StarIcon from '@mui/icons-material/Star';
import DownloadIcon from '@mui/icons-material/Download';
import FeedbackIcon from '@mui/icons-material/Feedback';
import CloseIcon from '@mui/icons-material/Close';
import RefreshIcon from '@mui/icons-material/Refresh';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Define feedback structure
interface ArtifactFeedback {
  id?: string;
  from: string;
  timestamp: string;
  content: string;
  rating?: number;
}

// Define reasoning structure
interface Reasoning {
  process?: string;
  steps?: string[];
  thoughts?: string[];
  considerations?: string[];
  decision?: string;
  confidence?: number;
  agentId?: string;
  timestamp?: string;
  conclusion?: string;
  supportingEvidence?: string[];
  insights?: string[];
}

// Define the iteration structure based on the Redis data
interface Iteration {
  version: number;
  timestamp: string;
  agent: string;
  content: string | Record<string, unknown>;
  feedback?: ArtifactFeedback[];
  incorporatedConsultations?: string[];
  reasoning?: Reasoning;
  changes?: string;
}

// Define the artifact structure based on the standardized IterativeArtifact format
interface Artifact {
  id: string;
  title: string;
  description?: string;
  type: string;
  status: string;
  content: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  createdAt: string;
  updatedAt?: string;
  version: number;

  // Legacy fields for backward compatibility
  name?: string;
  createdBy?: string;
  currentVersion?: number;
  iterations?: Iteration[];
  qualityScore?: number;
  data?: unknown;
  agent?: string;
  creator?: string;
  timestamp?: string;
  created?: string;
  date?: string;
  text?: string;
  body?: string;
  value?: string;
  reasoningSteps?: unknown[];
  reasoning?: Reasoning;
  [key: string]: unknown; // Index signature to allow string indexing
}

interface EnhancedArtifactGalleryProps {
  artifacts: unknown; // Accept unknown type as input to handle different structures
  onSendFeedback?: (artifactId: string, feedback: string) => void;
  refreshContent?: () => void;
  loading?: boolean;
}

const EnhancedArtifactGalleryV2: React.FC<EnhancedArtifactGalleryProps> = ({
  artifacts,
  onSendFeedback,
  refreshContent,
  loading = false
}): React.ReactElement => {
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState<boolean>(false);
  const [feedbackContent, setFeedbackContent] = useState<string>('');
  const [historyDialogOpen, setHistoryDialogOpen] = useState<boolean>(false);
  const [activeHistoryTab, setActiveHistoryTab] = useState<number>(0);
  const [normalizedArtifacts, setNormalizedArtifacts] = useState<Artifact[]>([]);

  // Define normalization function using useMemo to avoid recreation on each render
  const normalizeArtifacts = useMemo(() => {
    return (artifactsInput: unknown): Artifact[] => {
      if (!artifactsInput) {
        return [];
      }

      console.log('Normalizing artifacts:', typeof artifactsInput);
      let extractedArtifacts: Artifact[] = [];

      try {
        // PRIORITY CASE: Handle the Redis structure (object with artifact IDs as keys)
        if (artifactsInput && typeof artifactsInput === 'object' && !Array.isArray(artifactsInput)) {
          const artifactsObj = artifactsInput as Record<string, unknown>;

          // Check if it's the direct artifacts object structure from Redis
          const hasValidArtifacts = Object.values(artifactsObj).some(v =>
            v !== null && typeof v === 'object' &&
            ((v as Record<string, unknown>).type !== undefined ||
             (v as Record<string, unknown>).createdBy !== undefined ||
             (v as Record<string, unknown>).iterations !== undefined)
          );

          if (hasValidArtifacts) {
            console.log('Processing Redis-style artifacts object with ID keys');
            const artifacts = Object.entries(artifactsObj).map(([id, data]) => {
              if (!data || typeof data !== 'object') return null;

              const artifactData = data as Record<string, unknown>;

              // Create artifact from the Redis data structure
              return {
                id: (artifactData.id as string) || id,
                name: (artifactData.name as string) || (artifactData.title as string) || 'Unnamed Artifact',
                type: (artifactData.type as string) || 'unknown',
                createdBy: (artifactData.createdBy as string) || (artifactData.creator as string) || (artifactData.agent as string) || 'Unknown',
                createdAt: (artifactData.createdAt as string) || (artifactData.timestamp as string) || (artifactData.created as string) || (artifactData.date as string) || new Date().toISOString(),
                status: (artifactData.status as string) || 'draft',
                content: artifactData.content || artifactData.text || artifactData.data || '',
                currentVersion: typeof artifactData.currentVersion === 'number' ? artifactData.currentVersion :
                              (Array.isArray(artifactData.iterations) ? artifactData.iterations.length : 1),
                qualityScore: typeof artifactData.qualityScore === 'number' ? artifactData.qualityScore :
                            (typeof artifactData.qualityScore === 'string' ?
                              parseFloat(artifactData.qualityScore) : 0),
                iterations: Array.isArray(artifactData.iterations) ?
                            artifactData.iterations as Iteration[] : [],
                metadata: (artifactData.metadata as Record<string, unknown>) || {}
              } as Artifact;
            }).filter(Boolean) as Artifact[];

            if (artifacts.length > 0) {
              return artifacts.sort((a, b) => {
                const dateA = new Date(a.createdAt).getTime();
                const dateB = new Date(b.createdAt).getTime();
                return dateB - dateA; // newest first
              });
            }
          }
        }

        // Check if it's already an array of artifacts
        if (Array.isArray(artifactsInput)) {
          console.log('Processing array of artifacts');
          extractedArtifacts = artifactsInput
            .filter(item => item && typeof item === 'object')
            .map(item => {
              const artifactItem = item as Record<string, unknown>;

              // Ensure each item has the required fields
              return {
                id: (artifactItem.id as string) || Math.random().toString(36).substring(7),
                name: (artifactItem.name as string) || (artifactItem.title as string) || 'Unnamed Artifact',
                type: (artifactItem.type as string) || 'unknown',
                createdBy: (artifactItem.createdBy as string) || (artifactItem.creator as string) || (artifactItem.agent as string) || 'Unknown',
                createdAt: (artifactItem.createdAt as string) || (artifactItem.timestamp as string) || (artifactItem.created as string) || (artifactItem.date as string) || new Date().toISOString(),
                status: (artifactItem.status as string) || 'draft',
                content: artifactItem.content || artifactItem.text || artifactItem.data || '',
                currentVersion: typeof artifactItem.currentVersion === 'number' ? artifactItem.currentVersion :
                              (Array.isArray(artifactItem.iterations) ? artifactItem.iterations.length : 1),
                qualityScore: typeof artifactItem.qualityScore === 'number' ? artifactItem.qualityScore :
                            (typeof artifactItem.qualityScore === 'string' ?
                              parseFloat(artifactItem.qualityScore as string) : 0),
                iterations: Array.isArray(artifactItem.iterations) ? artifactItem.iterations as Iteration[] : [],
                metadata: (artifactItem.metadata as Record<string, unknown>) || {}
              } as Artifact;
            }) as Artifact[];
        }
      } catch (error) {
        console.error('Error normalizing artifacts:', error);
        return [];
      }

      // Sort artifacts by creation date, newest first
      return extractedArtifacts
        .filter(art => art && art.id) // Ensure only valid artifacts with IDs
        .sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime();
          const dateB = new Date(b.createdAt).getTime();
          return dateB - dateA; // newest first
        });
    };
  }, []);

  // Effect to normalize artifacts when the input changes
  useEffect(() => {
    console.log('Artifacts input type:', typeof artifacts);

    // Convert object format to array if needed
    let artifactsArray: any[] = [];

    if (artifacts) {
      if (Array.isArray(artifacts)) {
        console.log('Artifacts is an array, converting to standardized format');
        artifactsArray = artifacts;
      } else if (typeof artifacts === 'object') {
        // Convert object with ID keys to array
        console.log('Artifacts is an object, extracting values');

        // Check if this is the generatedArtifacts array format
        if ((artifacts as any).generatedArtifacts && Array.isArray((artifacts as any).generatedArtifacts)) {
          console.log('Found generatedArtifacts array in state');
          const artifactIds = (artifacts as any).generatedArtifacts;
          const artifactsObj = (artifacts as any).artifacts || {};

          artifactsArray = artifactIds.map(id => {
            const artifact = artifactsObj[id];
            if (artifact) {
              return {
                ...artifact,
                id: artifact.id || id
              };
            }
            return null;
          }).filter(Boolean);

          console.log(`Extracted ${artifactsArray.length} artifacts from generatedArtifacts array`);
        }
        // Handle Record<string, IterativeArtifact> format (our standardized format)
        else if (Object.keys(artifacts).length > 0) {
          console.log(`Processing ${Object.keys(artifacts).length} artifacts from object format`);

          // Check if this is the standardized format with artifacts as a Record<string, IterativeArtifact>
          const isStandardizedFormat = Object.values(artifacts).some(
            art => art && typeof art === 'object' &&
            (art as any).type && (art as any).content && (art as any).status
          );

          if (isStandardizedFormat) {
            console.log('Detected standardized IterativeArtifact format');
            artifactsArray = Object.entries(artifacts).map(([id, artifact]) => {
              if (typeof artifact === 'object' && artifact !== null) {
                // For standardized format, just ensure ID is set
                return {
                  ...artifact,
                  id: (artifact as any).id || id,
                  // Ensure backward compatibility
                  name: (artifact as any).name || (artifact as any).title || `Artifact ${id.substring(0, 8)}`,
                  createdBy: (artifact as any).createdBy || (artifact as any).creator || (artifact as any).agent || 'system',
                  currentVersion: (artifact as any).currentVersion || (artifact as any).version || 1
                };
              }
              return null;
            }).filter(Boolean) as any[];
          } else {
            // Legacy format handling
            console.log('Processing legacy artifact format');
            artifactsArray = Object.entries(artifacts).map(([id, artifact]) => {
              if (typeof artifact === 'object' && artifact !== null) {
                // Create a more robust artifact object
                return {
                  ...artifact,
                  id: (artifact as any).id || id,
                  title: (artifact as any).title || (artifact as any).name || `Artifact ${id.substring(0, 8)}`,
                  type: (artifact as any).type || 'unknown',
                  status: (artifact as any).status || 'completed',
                  createdAt: (artifact as any).createdAt || (artifact as any).timestamp || (artifact as any).created || new Date().toISOString(),
                  content: (artifact as any).content || (artifact as any).text || (artifact as any).data || (artifact as any).value || '',
                  version: (artifact as any).version || (artifact as any).currentVersion || 1,

                  // Legacy fields for backward compatibility
                  name: (artifact as any).name || (artifact as any).title || `Artifact ${id.substring(0, 8)}`,
                  createdBy: (artifact as any).createdBy || (artifact as any).creator || (artifact as any).agent || 'system',
                  currentVersion: (artifact as any).currentVersion || (artifact as any).version || 1
                };
              }
              return null;
            }).filter(Boolean) as any[];
          }

          console.log(`Extracted ${artifactsArray.length} artifacts from object format`);
        } else {
          // Handle case where artifacts might be a single artifact object
          console.log('Artifacts might be a single artifact object');
          if ((artifacts as any).id) {
            artifactsArray = [artifacts];
            console.log('Added single artifact to array');
          } else {
            console.log('No valid artifacts found in object');
          }
        }
      }
    }

    console.log(`Converted ${artifactsArray.length} artifacts for normalization`);

    // Add a manual refresh if no artifacts were found but we know they exist
    if (artifactsArray.length === 0 && refreshContent) {
      console.log('No artifacts found, but we know they exist. Triggering refresh...');
      setTimeout(() => {
        refreshContent();
      }, 2000);
    }

    setNormalizedArtifacts(normalizeArtifacts(artifactsArray));
  }, [artifacts, normalizeArtifacts, refreshContent]);

  // Helper functions for the component
  const handleOpenArtifact = (artifact: Artifact): void => {
    setSelectedArtifact(artifact);
  };

  const handleCloseArtifact = (): void => {
    setSelectedArtifact(null);
  };

  const handleOpenFeedbackDialog = (): void => {
    setFeedbackDialogOpen(true);
  };

  const handleCloseFeedbackDialog = (): void => {
    setFeedbackDialogOpen(false);
    setFeedbackContent('');
  };

  const handleSubmitFeedback = (): void => {
    if (selectedArtifact && feedbackContent.trim() && onSendFeedback) {
      onSendFeedback(selectedArtifact.id, feedbackContent);
      handleCloseFeedbackDialog();
    }
  };

  const handleOpenHistoryDialog = (): void => {
    setHistoryDialogOpen(true);
  };

  const handleCloseHistoryDialog = (): void => {
    setHistoryDialogOpen(false);
    setActiveHistoryTab(0);
  };

  const handleHistoryTabChange = (_event: React.SyntheticEvent, newValue: number): void => {
    setActiveHistoryTab(newValue);
  };

  // Download artifact as JSON
  const downloadArtifact = (artifact: Artifact): void => {
    const data = JSON.stringify(artifact, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.name.replace(/\s+/g, '-').toLowerCase()}-v${artifact.currentVersion}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string: string): string => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp: string): string => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  // Render the history dialog
  const renderHistoryDialog = (): React.ReactNode => {
    if (!selectedArtifact) return null;

    return (
      <Dialog
        open={historyDialogOpen}
        onClose={handleCloseHistoryDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Version History: {selectedArtifact.name}</Typography>
            <IconButton onClick={handleCloseHistoryDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          {selectedArtifact.iterations.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No history available for this artifact.
            </Typography>
          ) : (
            <>
              <Tabs
                value={activeHistoryTab}
                onChange={handleHistoryTabChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                {selectedArtifact.iterations.map((iteration, index) => (
                  <Tab
                    key={index}
                    label={`v${iteration.version}`}
                  />
                ))}
              </Tabs>

              <Box sx={{ mt: 2 }}>
                {selectedArtifact.iterations.map((iteration, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: activeHistoryTab === index ? 'block' : 'none',
                      mt: 2
                    }}
                  >
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2">
                        Version {iteration.version}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" display="block">
                        {formatTimestamp(iteration.timestamp)} by {iteration.agent}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {iteration.changes || 'Content updated'}
                      </Typography>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Content
                      </Typography>
                      <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                        <Box component="pre" sx={{ margin: 0, whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                          {(() => {
                            const content = iteration.content;
                            if (typeof content === 'string') {
                              return content;
                            } else if (typeof content === 'object' && content !== null) {
                              return JSON.stringify(content, null, 2);
                            }
                            return 'No content available';
                          })()}
                        </Box>
                      </Paper>
                    </Box>

                    {iteration.reasoning && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Reasoning
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                          <Box component="pre" sx={{ margin: 0, whiteSpace: 'pre-wrap', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                            {(() => {
                              const reasoning = iteration.reasoning;
                              if (typeof reasoning === 'string') {
                                return reasoning;
                              } else if (typeof reasoning === 'object' && reasoning !== null) {
                                return JSON.stringify(reasoning, null, 2);
                              }
                              return 'No reasoning available';
                            })()}
                          </Box>
                        </Paper>
                      </Box>
                    )}
                  </Box>
                ))}
              </Box>
            </>
          )}
        </DialogContent>
      </Dialog>
    );
  };

  // Render the feedback dialog
  const renderFeedbackDialog = (): React.ReactNode => {
    if (!selectedArtifact) return null;

    return (
      <Dialog
        open={feedbackDialogOpen}
        onClose={handleCloseFeedbackDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Provide Feedback</Typography>
            <IconButton onClick={handleCloseFeedbackDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Your Feedback"
            fullWidth
            multiline
            rows={4}
            value={feedbackContent}
            onChange={(e) => setFeedbackContent(e.target.value)}
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseFeedbackDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitFeedback}
            variant="contained"
            color="primary"
            disabled={!feedbackContent.trim()}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Enhanced recursive function to find text content in nested objects (similar to AgentDiscussionPanel)
  const findTextContent = (obj: any): string | null => {
    if (!obj) return null;

    // If it's a string, return it directly
    if (typeof obj === 'string') return obj;

    // Special handling for content generation artifacts
    if (obj.type === 'content' || obj.type === 'article' || obj.type === 'content-draft' || obj.type === 'article-draft') {
      console.log('Found content/article artifact, checking special fields');

      // Check for article-specific fields first
      const articleFields = ['content', 'text', 'body', 'article', 'generatedContent', 'markdown'];
      for (const field of articleFields) {
        if (typeof obj[field] === 'string' && obj[field].trim().length > 100) {
          console.log(`Found article content in ${field} field`);
          return obj[field];
        }

        // Check one level deeper for these important fields
        if (obj[field] && typeof obj[field] === 'object') {
          for (const subField of ['content', 'text', 'body', 'data']) {
            if (typeof obj[field][subField] === 'string' && obj[field][subField].trim().length > 100) {
              console.log(`Found article content in ${field}.${subField} field`);
              return obj[field][subField];
            }
          }
        }
      }

      // Check for sections content (common in article artifacts)
      if (obj.sections && Array.isArray(obj.sections)) {
        const sectionsContent = obj.sections
          .map((section: any) => {
            if (typeof section === 'string') return section;
            if (section.content) return section.content;
            if (section.text) return section.text;
            if (section.body) return section.body;
            return null;
          })
          .filter(Boolean)
          .join('\n\n');

        if (sectionsContent.length > 0) {
          console.log('Found content in sections array');
          return sectionsContent;
        }
      }
    }

    // Check common text fields
    const possibleTextFields = ['text', 'content', 'body', 'message', 'description', 'data', 'value', 'question', 'answer', 'feedback', 'summary', 'response'];
    for (const field of possibleTextFields) {
      if (typeof obj[field] === 'string' && obj[field].trim()) {
        return obj[field];
      }
    }

    // Check for nested text fields
    for (const field of possibleTextFields) {
      if (obj[field] && typeof obj[field] === 'object') {
        const nestedText = findTextContent(obj[field]);
        if (nestedText) return nestedText;
      }
    }

    // Handle arrays - check if any array element contains text content
    if (Array.isArray(obj)) {
      for (const item of obj) {
        if (typeof item === 'string' && item.trim()) {
          return item;
        }
        if (typeof item === 'object' && item !== null) {
          const nestedText = findTextContent(item);
          if (nestedText) return nestedText;
        }
      }
    }

    // Recursively search all object properties
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const nestedText = findTextContent(obj[key]);
        if (nestedText) return nestedText;
      }
    }

    return null;
  };

  // Enhanced extract readable content from an artifact using recursive approach
  const extractReadableContent = (artifact: Artifact): string => {
    if (!artifact) return 'No content available';

    console.log(`Extracting content from artifact: ${artifact.id}, type: ${artifact.type}`);
    console.log('Artifact structure:', {
      hasContent: !!artifact.content,
      contentType: artifact.content ? typeof artifact.content : 'none',
      hasData: !!artifact.data,
      hasIterations: !!artifact.iterations,
      iterationsCount: artifact.iterations ? artifact.iterations.length : 0,
      keys: Object.keys(artifact)
    });

    // Special handling for content/article artifacts
    if (artifact.type === 'content' || artifact.type === 'article' ||
        artifact.type === 'content-draft' || artifact.type === 'article-draft' ||
        artifact.type === 'blog-post' || artifact.type === 'draft-content' ||
        artifact.type === 'content-generation') {
      console.log('Processing content/article artifact');

      // First, try to find substantial content directly in the artifact
      const directContent = findTextContent(artifact);
      if (directContent && directContent.length > 100) {
        console.log('Found substantial content directly in the artifact');
        return directContent;
      }

      // Check data field which is often used for content
      if (artifact.data) {
        console.log('Checking data field for content');
        if (typeof artifact.data === 'string' && artifact.data.length > 100) {
          console.log('Found string content in data field');
          return artifact.data;
        } else if (typeof artifact.data === 'object') {
          console.log('Checking data object for content fields');
          const dataContent = findTextContent(artifact.data);
          if (dataContent && dataContent.length > 100) {
            console.log('Found content in data object');
            return dataContent;
          }
        }
      }
    }

    // Try direct string content first
    if (typeof artifact.content === 'string' && artifact.content.trim()) {
      console.log('Found direct string content');
      return artifact.content;
    }

    // Try to find content recursively in the content object
    if (artifact.content && typeof artifact.content === 'object') {
      console.log('Searching content object recursively');
      const textContent = findTextContent(artifact.content);
      if (textContent) {
        console.log('Found content in content object');
        return textContent;
      }
    }

    // Try to extract content from iterations if available
    if (Array.isArray(artifact.iterations) && artifact.iterations.length > 0) {
      console.log(`Checking ${artifact.iterations.length} iterations`);

      // Sort iterations by version number or timestamp
      const sortedIterations = [...artifact.iterations].sort((a, b) => {
        // First try to sort by version number
        if (a.version !== undefined && b.version !== undefined) {
          return b.version - a.version; // Latest version first
        }
        // Fall back to timestamp
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });

      // Try to extract content from each iteration, starting with the most recent
      for (const iteration of sortedIterations) {
        console.log(`Checking iteration version ${iteration.version}`);

        // Direct string content
        if (typeof iteration.content === 'string' && iteration.content.trim()) {
          console.log('Found direct string content in iteration');
          return iteration.content;
        }

        // Recursive search in object content
        if (iteration.content && typeof iteration.content === 'object') {
          console.log('Searching iteration content object recursively');
          const textContent = findTextContent(iteration.content);
          if (textContent) {
            console.log('Found content in iteration content object');
            return textContent;
          }
        }
      }
    }

    // Check other common fields
    const otherFields = ['text', 'data', 'body', 'description', 'value', 'message', 'question', 'answer', 'feedback', 'summary', 'response'];
    for (const field of otherFields) {
      // Direct string content
      if (typeof artifact[field] === 'string' && artifact[field].trim()) {
        console.log(`Found content in ${field} field`);
        return artifact[field] as string;
      }

      // Recursive search in object
      if (artifact[field] && typeof artifact[field] === 'object') {
        console.log(`Searching ${field} object recursively`);
        const textContent = findTextContent(artifact[field] as Record<string, unknown>);
        if (textContent) {
          console.log(`Found content in ${field} object`);
          return textContent;
        }
      }
    }

    // Check for nested content structures
    const nestedContentFields = ['data', 'details', 'result', 'output', 'response', 'payload', 'artifact'];
    for (const field of nestedContentFields) {
      if (artifact[field] && typeof artifact[field] === 'object') {
        console.log(`Checking nested field: ${field}`);
        const nestedContent = artifact[field] as Record<string, unknown>;

        // Check for common text fields in the nested content
        for (const nestedField of otherFields) {
          if (typeof nestedContent[nestedField] === 'string' && (nestedContent[nestedField] as string).trim()) {
            console.log(`Found content in ${field}.${nestedField}`);
            return nestedContent[nestedField] as string;
          }
        }

        // Try recursive search
        const textContent = findTextContent(nestedContent);
        if (textContent) {
          console.log(`Found content in ${field} through recursive search`);
          return textContent;
        }
      }
    }

    // Special handling for messages that might contain article content
    if (artifact.type === 'message' || artifact.type === 'response') {
      console.log('Checking message-specific fields');

      // Check for artifact delivery messages
      if ((artifact.content && typeof artifact.content === 'object' &&
           ((artifact.content as any).type === 'ARTIFACT_DELIVERY' ||
            (artifact.content as any).artifactId))) {

        console.log('Found artifact delivery message');

        // Check for artifact content
        if ((artifact.content as any).artifact) {
          const artifactContent = findTextContent((artifact.content as any).artifact);
          if (artifactContent) {
            console.log('Found content in delivered artifact');
            return artifactContent;
          }
        }
      }
    }

    // If we still don't have content, try to extract something meaningful from the artifact
    console.log('No content found, falling back to JSON representation');
    const artifactJson = JSON.stringify(artifact, null, 2);
    if (artifactJson.length > 2) {
      return artifactJson;
    }

    // Last resort fallback
    return `No content available for this ${artifact.type || 'unknown'} artifact`;
  };

  // Render content based on its format (markdown, JSON, or plain text)
  const renderFormattedContent = (content: string): React.ReactNode => {
    if (!content) return <Typography>No content available</Typography>;

    // Check if it's JSON
    try {
      if ((content.trim().startsWith('{') && content.trim().endsWith('}')) ||
          (content.trim().startsWith('[') && content.trim().endsWith(']'))) {
        const parsedJson = JSON.parse(content);
        return (
          <Box component="pre" sx={{
            margin: 0,
            whiteSpace: 'pre-wrap',
            overflow: 'auto',
            backgroundColor: '#f5f5f5',
            padding: 2,
            borderRadius: 1,
            fontSize: '0.875rem',
            maxHeight: '500px'
          }}>
            {JSON.stringify(parsedJson, null, 2)}
          </Box>
        );
      }
    } catch (e) {
      // Not valid JSON, continue to other formats
    }

    // Check if it looks like markdown
    const markdownIndicators = [
      '#', // Headers
      '*', // Bold/italic or lists
      '```', // Code blocks
      '---', // Horizontal rule
      '| ---', // Tables
      '[', // Links
      '- ', // Lists
      '1. ', // Numbered lists
      '> ', // Blockquotes
      '![', // Images
      '**', // Bold
      '_', // Italic
      '~~' // Strikethrough
    ];

    const isLikelyMarkdown = markdownIndicators.some(indicator => content.includes(indicator));

    if (isLikelyMarkdown) {
      return (
        <Box sx={{
          p: 2,
          maxHeight: '500px',
          overflow: 'auto',
          '& h1, & h2, & h3, & h4, & h5, & h6': {
            mt: 2,
            mb: 1,
            fontWeight: 'bold',
            lineHeight: 1.2
          },
          '& h1': { fontSize: '1.8rem' },
          '& h2': { fontSize: '1.5rem' },
          '& h3': { fontSize: '1.3rem' },
          '& h4': { fontSize: '1.1rem' },
          '& h5': { fontSize: '1rem' },
          '& h6': { fontSize: '0.9rem' },
          '& p': { mb: 1.5 },
          '& ul, & ol': { pl: 3, mb: 1.5 },
          '& li': { mb: 0.5 },
          '& blockquote': {
            borderLeft: '3px solid #ccc',
            pl: 2,
            py: 0.5,
            my: 1.5,
            color: 'text.secondary'
          },
          '& pre': {
            backgroundColor: '#f5f5f5',
            p: 1.5,
            borderRadius: 1,
            overflow: 'auto',
            mb: 1.5
          },
          '& code': {
            backgroundColor: '#f5f5f5',
            p: 0.5,
            borderRadius: 0.5,
            fontFamily: 'monospace'
          },
          '& table': {
            borderCollapse: 'collapse',
            width: '100%',
            mb: 1.5
          },
          '& th': {
            backgroundColor: '#f5f5f5',
            fontWeight: 'bold',
            textAlign: 'left',
            p: 1,
            border: '1px solid #ddd'
          },
          '& td': {
            p: 1,
            border: '1px solid #ddd'
          },
          '& hr': {
            border: 'none',
            borderTop: '1px solid #ddd',
            my: 2
          },
          '& img': {
            maxWidth: '100%',
            height: 'auto'
          },
          '& a': {
            color: 'primary.main',
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline'
            }
          }
        }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {content}
          </ReactMarkdown>
        </Box>
      );
    }

    // Default to plain text
    return (
      <Box sx={{
        p: 2,
        maxHeight: '500px',
        overflow: 'auto',
        whiteSpace: 'pre-wrap',
        fontFamily: 'inherit',
        fontSize: '0.9rem',
        lineHeight: 1.5
      }}>
        {content}
      </Box>
    );
  };

  // Render the artifact detail dialog with improved content display
  const renderArtifactDetail = (): React.ReactNode => {
    if (!selectedArtifact) return null;

    // Extract readable content
    const readableContent = extractReadableContent(selectedArtifact);

    // Get all contributors including creator and iteration contributors
    const getContributors = () => {
      const contributors = new Set<string>();

      // Add creator
      if (selectedArtifact.createdBy) {
        contributors.add(selectedArtifact.createdBy);
      }

      // Add contributors from iterations
      if (Array.isArray(selectedArtifact.iterations)) {
        selectedArtifact.iterations.forEach(iteration => {
          if (iteration.agent) {
            contributors.add(iteration.agent);
          }
        });
      }

      return Array.from(contributors);
    };

    // Enhanced function to get reasoning information if available
    const getReasoningInfo = () => {
      // Check various locations where reasoning might be stored
      const possibleReasoningLocations = [
        selectedArtifact.reasoning,
        selectedArtifact.metadata?.reasoning,
        // Handle content object or string
        typeof selectedArtifact.content === 'object' ?
          (selectedArtifact.content as Record<string, unknown>)?.reasoning : undefined,
        // Check most recent iteration
        selectedArtifact.iterations?.[selectedArtifact.iterations.length - 1]?.reasoning
      ];

      // Return the first non-null reasoning object found
      for (const reasoning of possibleReasoningLocations) {
        if (reasoning) {
          return reasoning;
        }
      }

      // If no reasoning object found, try to extract reasoning from other fields
      if (typeof selectedArtifact.reasoningSteps === 'object' &&
          Array.isArray(selectedArtifact.reasoningSteps) &&
          selectedArtifact.reasoningSteps.length > 0) {
        return {
          steps: selectedArtifact.reasoningSteps
        } as Reasoning;
      }

      // Check for reasoning in nested content
      if (typeof selectedArtifact.content === 'object') {
        const content = selectedArtifact.content as Record<string, unknown>;
        if (content.reasoningSteps || content.steps || content.thoughts || content.considerations) {
          return {
            steps: content.reasoningSteps || content.steps || content.thoughts || content.considerations
          } as Reasoning;
        }
      }

      return null;
    };

    const reasoning = getReasoningInfo();
    const contributors = getContributors();

    return (
      <Dialog
        open={selectedArtifact !== null}
        onClose={handleCloseArtifact}
        fullWidth
        maxWidth="lg" // Larger dialog for better content display
        scroll="paper"
      >
        <DialogTitle sx={{ borderBottom: '1px solid', borderColor: 'divider', pb: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h6" component="h2">
                {selectedArtifact.name || selectedArtifact.title || `Unnamed ${selectedArtifact.type} Artifact`}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {selectedArtifact.type} • Created by {getAgentDisplayName(selectedArtifact.createdBy)} •
                {formatTimestamp(selectedArtifact.createdAt)}
              </Typography>
            </Box>
            <IconButton onClick={handleCloseArtifact} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Box>


        </DialogTitle>

        <DialogContent dividers sx={{ p: 0 }}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' } }}>
            {/* Main content area - 70% width */}
            <Box sx={{
              width: { xs: '100%', md: '70%' },
              borderRight: { md: '1px solid' },
              borderColor: 'divider'
            }}>
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: getColorForType(selectedArtifact.type),
                      mr: 1
                    }}
                  />
                  Content
                </Typography>

                <Paper
                  variant="outlined"
                  sx={{
                    p: 0,
                    mb: 3,
                    maxHeight: '500px',
                    overflow: 'auto'
                  }}
                >
                  {renderFormattedContent(readableContent)}
                </Paper>

                {/* Reasoning section if available */}
                {reasoning && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Reasoning Process
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      {(reasoning as any).process && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          <strong>Process:</strong> {(reasoning as any).process}
                        </Typography>
                      )}

                      {(reasoning as any).confidence && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          <strong>Confidence:</strong> {typeof (reasoning as any).confidence === 'number'
                            ? `${((reasoning as any).confidence * 100).toFixed(0)}%`
                            : (reasoning as any).confidence}
                        </Typography>
                      )}

                      {(reasoning as any).steps && Array.isArray((reasoning as any).steps) && (reasoning as any).steps.length > 0 && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="subtitle2" gutterBottom>Steps:</Typography>
                          <ol>
                            {((reasoning as any).steps as string[]).map((step: string, index: number) => (
                              <li key={index}>
                                <Typography variant="body2">{step}</Typography>
                              </li>
                            ))}
                          </ol>
                        </Box>
                      )}

                      {(reasoning as any).conclusion && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="subtitle2" gutterBottom>Conclusion:</Typography>
                          <Typography variant="body2">{(reasoning as any).conclusion}</Typography>
                        </Box>
                      )}
                    </Paper>
                  </Box>
                )}

                {/* Iterations section if available */}
                {Array.isArray(selectedArtifact.iterations) && selectedArtifact.iterations.length > 0 && (
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Version History
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 0 }}>
                      {selectedArtifact.iterations.map((iteration, index) => (
                        <Box
                          key={index}
                          sx={{
                            p: 2,
                            borderBottom: index < selectedArtifact.iterations.length - 1 ? '1px solid' : 'none',
                            borderColor: 'divider',
                            '&:hover': { bgcolor: 'action.hover' }
                          }}
                        >
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="subtitle2">
                              Version {iteration.version || index + 1}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatTimestamp(iteration.timestamp)} by {getAgentDisplayName(iteration.agent)}
                            </Typography>
                          </Box>
                          {iteration.changes && (
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              {iteration.changes}
                            </Typography>
                          )}
                          <Button
                            size="small"
                            sx={{ mt: 1 }}
                            onClick={() => handleOpenHistoryDialog()}
                          >
                            View Content
                          </Button>
                        </Box>
                      ))}
                    </Paper>
                  </Box>
                )}
              </Box>
            </Box>

            {/* Sidebar with metadata - 30% width */}
            <Box sx={{ width: { xs: '100%', md: '30%' } }}>
              <Box sx={{ p: 3 }}>
                {/* Status section */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>Status</Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Chip
                        label={selectedArtifact.status || 'Draft'}
                        color={getStatusColor(selectedArtifact.status)}
                        size="small"
                      />
                      {selectedArtifact.qualityScore !== undefined && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <StarIcon sx={{ color: 'gold', mr: 0.5, fontSize: '1rem' }} />
                          <Typography variant="body2">
                            {typeof selectedArtifact.qualityScore === 'number'
                              ? `${(selectedArtifact.qualityScore * 100).toFixed(0)}%`
                              : selectedArtifact.qualityScore}
                          </Typography>
                        </Box>
                      )}
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        <strong>Version:</strong> {selectedArtifact.currentVersion || 1}
                        {Array.isArray(selectedArtifact.iterations) && selectedArtifact.iterations.length > 0 &&
                          ` of ${selectedArtifact.iterations.length}`}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Created:</strong> {formatTimestamp(selectedArtifact.createdAt)}
                      </Typography>
                      {selectedArtifact.updatedAt && (
                        <Typography variant="body2">
                          <strong>Updated:</strong> {formatTimestamp(selectedArtifact.updatedAt)}
                        </Typography>
                      )}
                    </Box>
                  </Paper>
                </Box>

                {/* Contributors section */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>Contributors</Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {contributors.map((contributor, index) => (
                        <Chip
                          key={index}
                          label={getAgentDisplayName(contributor)}
                          size="small"
                          avatar={
                            <Avatar
                              sx={{
                                bgcolor: getColorForType(contributor)
                              }}
                            >
                              {getAgentDisplayName(contributor).charAt(0)}
                            </Avatar>
                          }
                        />
                      ))}
                    </Box>
                  </Paper>
                </Box>

                {/* Additional metadata section */}
                {selectedArtifact.metadata && Object.keys(selectedArtifact.metadata).length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>Additional Metadata</Typography>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      {Object.entries(selectedArtifact.metadata)
                        .filter(([key]) => key !== 'reasoning') // Skip reasoning as it's shown separately
                        .map(([key, value]) => (
                          <Typography key={key} variant="body2" sx={{ mb: 0.5 }}>
                            <strong>{key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}:</strong>{' '}
                            {typeof value === 'string'
                              ? value
                              : typeof value === 'object' && value !== null
                                ? JSON.stringify(value)
                                : String(value)}
                          </Typography>
                        ))}
                    </Paper>
                  </Box>
                )}

                {/* Actions section */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>Actions</Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<DownloadIcon />}
                        onClick={() => downloadArtifact(selectedArtifact)}
                      >
                        Download Artifact
                      </Button>

                      {onSendFeedback && (
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<FeedbackIcon />}
                          onClick={handleOpenFeedbackDialog}
                        >
                          Provide Feedback
                        </Button>
                      )}
                    </Box>
                  </Paper>
                </Box>
              </Box>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
    );
  };

  // Group artifacts by type
  const groupedArtifacts: Record<string, Artifact[]> = {};
  normalizedArtifacts.forEach((artifact: Artifact) => {
    if (!artifact.type) {
      // For artifacts without a type, assign a default type
      const defaultType = 'unknown';
      if (!groupedArtifacts[defaultType]) {
        groupedArtifacts[defaultType] = [];
      }
      groupedArtifacts[defaultType].push({
        ...artifact,
        type: defaultType
      });
      return;
    }

    if (!groupedArtifacts[artifact.type]) {
      groupedArtifacts[artifact.type] = [];
    }
    groupedArtifacts[artifact.type].push(artifact);
  });

  // If we have no artifacts, add a placeholder for better UX
  if (Object.keys(groupedArtifacts).length === 0 && !loading && normalizedArtifacts.length === 0) {
    console.log('No artifacts found, adding placeholder group');
    groupedArtifacts['no-artifacts'] = [];
  }

  // Get agent display name
  const getAgentDisplayName = (agentId: string): string => {
    const displayNames: {[key: string]: string} = {
      'market-research': 'Market Research',
      'seo-keyword': 'SEO Keywords',
      'content-strategy': 'Content Strategy',
      'content-generation': 'Content Writer',
      'seo-optimization': 'SEO Optimizer',
      'editorial-review': 'Editorial Review',
      'user': 'User',
      'system': 'System'
    };

    return displayNames[agentId] ||
           agentId.charAt(0).toUpperCase() +
           agentId.slice(1).replace(/-/g, ' ');
  };

  // Get color for agent/artifact type
  const getColorForType = (type: string): string => {
    const colorMap: {[key: string]: string} = {
      'market-research': '#ff9800',
      'seo-keyword': '#00bcd4',
      'content-strategy': '#9c27b0',
      'content-generation': '#4caf50',
      'seo-optimization': '#2196f3',
      'editorial-review': '#f44336',
      'user': '#795548',
      'system': '#757575',
      'unknown': '#9e9e9e'
    };

    return colorMap[type] || colorMap.unknown;
  };

  // Get MUI color for status
  const getStatusColor = (status: string): 'success' | 'info' | 'warning' | 'error' | 'default' => {
    if (!status) return 'default';

    const statusLower = status.toLowerCase();
    if (statusLower.includes('complet') || statusLower === 'done' || statusLower === 'finished') {
      return 'success';
    }
    if (statusLower.includes('progress') || statusLower === 'active' || statusLower === 'running') {
      return 'info';
    }
    if (statusLower.includes('draft') || statusLower === 'pending' || statusLower === 'waiting') {
      return 'default';
    }
    if (statusLower.includes('error') || statusLower === 'failed') {
      return 'error';
    }
    return 'default';
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Header with refresh button and filter options */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Artifacts
        </Typography>
        {refreshContent && (
          <Button
            startIcon={<RefreshIcon />}
            onClick={refreshContent}
            variant="outlined"
            disabled={loading}
          >
            Refresh
          </Button>
        )}
      </Box>

      {/* Loading state */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
          <CircularProgress />
          <Typography variant="body1" color="text.secondary" sx={{ ml: 2 }}>
            Loading artifacts...
          </Typography>
        </Box>
      ) : normalizedArtifacts.length === 0 ? (
        /* Empty state */
        <Paper sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No artifacts available
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Artifacts will appear here when they are created by agents during the collaboration process.
          </Typography>
          {refreshContent && (
            <Button
              startIcon={<RefreshIcon />}
              onClick={refreshContent}
              variant="contained"
            >
              Refresh
            </Button>
          )}
        </Paper>
      ) : (
        /* Artifact list view */
        <>
          {/* Artifact list by type */}
          {Object.entries(groupedArtifacts).map(([artifactType, typeArtifacts]) => (
            <Paper
              key={artifactType}
              sx={{
                mb: 3,
                overflow: 'hidden',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              {/* Section header with type name and count */}
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'background.default',
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      borderRadius: '50%',
                      bgcolor: getColorForType(artifactType),
                      mr: 1.5
                    }}
                  />
                  <Typography variant="h6">
                    {capitalizeFirstLetter(artifactType.replace(/-/g, ' '))}
                  </Typography>
                </Box>
                <Chip
                  label={`${typeArtifacts.length} artifact${typeArtifacts.length !== 1 ? 's' : ''}`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Box>

              {/* List of artifacts */}
              <Box sx={{ p: 2 }}>
                {typeArtifacts.map((artifact: Artifact) => (
                  <Paper
                    key={artifact.id || Math.random().toString(36).substring(7)}
                    variant="outlined"
                    sx={{
                      mb: 2,
                      p: 0,
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                        borderColor: 'primary.main'
                      },
                      cursor: 'pointer',
                      overflow: 'hidden',
                      // Add special styling for placeholder artifacts
                      ...((artifact as any).isPlaceholder ? {
                        border: '1px dashed',
                        borderColor: 'warning.main',
                        position: 'relative'
                      } : {})
                    }}
                    onClick={() => handleOpenArtifact(artifact)}
                  >
                    <Box sx={{ p: 2 }}>


                      {/* Artifact header with name and status */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 'medium',
                            pr: 2
                          }}
                        >
                          {artifact.name || artifact.title || `Unnamed ${artifactType}`}
                        </Typography>
                        <Chip
                          size="small"
                          label={(artifact as any).isPlaceholder ? 'Placeholder' : (artifact.status || 'Draft')}
                          color={(artifact as any).isPlaceholder ? 'warning' : getStatusColor(artifact.status)}
                          sx={{ flexShrink: 0 }}
                        />
                      </Box>

                      {/* Artifact metadata */}
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', mb: 1, gap: 2 }}>
                        <Box sx={{ width: { xs: '45%', sm: '30%' } }}>
                          <Typography variant="caption" color="text.secondary" display="block">
                            Created by
                          </Typography>
                          <Typography variant="body2">
                            {getAgentDisplayName(artifact.createdBy)}
                          </Typography>
                        </Box>
                        <Box sx={{ width: { xs: '45%', sm: '30%' } }}>
                          <Typography variant="caption" color="text.secondary" display="block">
                            Date
                          </Typography>
                          <Typography variant="body2">
                            {artifact.createdAt ? formatTimestamp(artifact.createdAt).split(',')[0] :
                            artifact.timestamp ? formatTimestamp(artifact.timestamp as string).split(',')[0] :
                            artifact.created ? formatTimestamp(artifact.created as string).split(',')[0] : 'Unknown'}
                          </Typography>
                        </Box>
                        <Box sx={{ width: { xs: '45%', sm: '30%' } }}>
                          <Typography variant="caption" color="text.secondary" display="block">
                            Version
                          </Typography>
                          <Typography variant="body2">
                            {Array.isArray(artifact.iterations) && artifact.iterations.length > 0 ?
                            `${artifact.currentVersion || 1} of ${artifact.iterations.length}` :
                            `${artifact.currentVersion || 1}`}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Content preview */}
                      <Box sx={{
                        position: 'relative',
                        mb: 1,
                        bgcolor: 'background.default',
                        borderRadius: 1,
                        p: 1.5,
                        maxHeight: '80px',
                        overflow: 'hidden'
                      }}>
                        <Typography
                          variant="body2"
                          sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: 'vertical',
                            lineHeight: 1.5,
                            fontSize: '0.875rem'
                          }}
                        >
                          {(() => {
                            try {
                              // Extract content using our helper function
                              const content = extractReadableContent(artifact);
                              // Remove markdown formatting for preview
                              const plainContent = content
                                .replace(/#{1,6}\s+/g, '') // Remove headers
                                .replace(/\*\*(.+?)\*\*/g, '$1') // Remove bold
                                .replace(/\*(.+?)\*/g, '$1') // Remove italic
                                .replace(/`(.+?)`/g, '$1') // Remove inline code
                                .replace(/```[\s\S]+?```/g, '[Code Block]') // Replace code blocks
                                .replace(/\[(.+?)\]\(.+?\)/g, '$1') // Replace links with just text
                                .replace(/!\[.+?\]\(.+?\)/g, '[Image]') // Replace images
                                .replace(/\n\n/g, ' '); // Replace double newlines with space

                              return plainContent;
                            } catch (error) {
                              console.error('Error displaying artifact content:', error);
                              return 'Error displaying content';
                            }
                          })()}
                        </Typography>
                        <Box sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: '24px',
                          background: 'linear-gradient(rgba(250,250,250,0), rgba(250,250,250,1))'
                        }} />
                      </Box>

                      {/* Footer with quality score and action buttons */}
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mt: 1
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {artifact.qualityScore !== undefined && artifact.qualityScore !== null && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                              <StarIcon sx={{ color: 'gold', mr: 0.5, fontSize: '1rem' }} />
                              <Typography variant="body2">
                                {typeof artifact.qualityScore === 'number'
                                  ? `${(artifact.qualityScore * 100).toFixed(0)}%`
                                  : String(artifact.qualityScore)}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                        <Box>
                          <Button
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              downloadArtifact(artifact);
                            }}
                            startIcon={<DownloadIcon />}
                            sx={{ mr: 1 }}
                          >
                            Download
                          </Button>
                          <Button
                            size="small"
                            variant="contained"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenArtifact(artifact);
                            }}
                          >
                            View Details
                          </Button>
                        </Box>
                      </Box>
                    </Box>
                  </Paper>
                ))}
              </Box>
            </Paper>
          ))}

          {/* Render dialogs */}
          {renderArtifactDetail()}
          {renderHistoryDialog()}
          {renderFeedbackDialog()}
        </>
      )}
    </Box>
  );
};

export default EnhancedArtifactGalleryV2;
