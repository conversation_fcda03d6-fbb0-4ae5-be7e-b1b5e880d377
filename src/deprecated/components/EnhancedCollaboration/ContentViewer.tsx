// src/components/EnhancedCollaboration/ContentViewer.tsx
import React, { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface FinalOutput {
  title?: string;
  content?: string;
  seoScore?: number;
  marketResearch?: any;
  keywords?: any;
  metadata?: any;
  [key: string]: any;
}

interface ContentViewerProps {
  finalOutput?: FinalOutput | null;
  topic?: string;
  refreshContent: () => void;
  loading: boolean;
}

const ContentViewer: React.FC<ContentViewerProps> = ({
  finalOutput,
  topic = 'Generated Content',
  refreshContent,
  loading
}) => {
  const [processedContent, setProcessedContent] = useState<string>('');
  const [contentSource, setContentSource] = useState<string>('');

  // Debug the incoming finalOutput
  useEffect(() => {
    console.log('ContentViewer received:', {
      hasFinalOutput: !!finalOutput,
      finalOutputKeys: finalOutput ? Object.keys(finalOutput) : [],
      topic,
      contentType: finalOutput?.content ? typeof finalOutput.content : 'undefined',
      contentLength: finalOutput?.content?.length || 0,
      finalOutputRaw: finalOutput ? JSON.stringify(finalOutput).substring(0, 500) : 'null',
      hasArtifacts: finalOutput && finalOutput.artifacts ? finalOutput.artifacts.length > 0 : false,
      artifactsLength: finalOutput && finalOutput.artifacts ? finalOutput.artifacts.length : 0,
      hasRawContent: !!finalOutput?.rawContent,
      rawContentLength: finalOutput?.rawContent?.length || 0
    });

    // Check if we have a direct content field with substantial content
    if (finalOutput?.content && typeof finalOutput.content === 'string' && finalOutput.content.length > 500) {
      console.log('Found substantial content directly in finalOutput.content field');
    }
  }, [finalOutput, topic]);

  // Process content when finalOutput changes
  useEffect(() => {
    if (!finalOutput) {
      setProcessedContent('');
      setContentSource('');
      return;
    }

    // Find the best content source
    let content: any = null;
    let source = '';

    // Helper function to check if content is substantial
    const isSubstantialContent = (text: string): boolean => {
      // Check if the content is more than just a placeholder or debug message
      if (!text || text.length < 50) return false;

      // Check if it contains common placeholder phrases
      const placeholderPhrases = [
        "generated by content-generation-agent",
        "This is the output artefact",
        "This is the final output",
        "Content for",
        "don't you think we are going wrong here",
        "is being generated"
      ];

      // If it contains placeholder phrases and is short, it's probably not real content
      const containsPlaceholder = placeholderPhrases.some(phrase =>
        text.toLowerCase().includes(phrase.toLowerCase())
      );

      // If it's short and contains a placeholder phrase, it's not substantial
      if (containsPlaceholder && text.length < 300) return false;

      // Check if it has multiple paragraphs or markdown headings (signs of real content)
      const hasParagraphs = text.split('\n\n').length > 2;
      const hasHeadings = text.match(/^#+\s+.+$/m) !== null;

      // If it's long enough and has structure, it's likely real content
      if (text.length > 300 && (hasParagraphs || hasHeadings)) return true;

      return text.length > 500; // Default to length check if other checks don't pass
    };

    // Function to extract content from artifacts
    const extractContentFromArtifacts = (): { content: string, source: string } | null => {
      if (!finalOutput.artifacts || !Array.isArray(finalOutput.artifacts) || finalOutput.artifacts.length === 0) {
        return null;
      }

      console.log('Looking for content in artifacts:', {
        artifactsCount: finalOutput.artifacts.length,
        artifactTypes: finalOutput.artifacts.map(a => a.type).join(', ')
      });

      // First, try to find optimized-content artifacts (they should have the final version)
      const optimizedContentArtifacts = finalOutput.artifacts
        .filter(a => a.type === 'optimized-content')
        .sort((a, b) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime());

      if (optimizedContentArtifacts.length > 0) {
        console.log('Found optimized content artifacts:', optimizedContentArtifacts.length);

        // Check each optimized content artifact for substantial content
        for (const artifact of optimizedContentArtifacts) {
          // Try to get content from various possible locations
          const possibleContentSources = [
            { path: 'data.content', value: artifact.data?.content },
            { path: 'content', value: artifact.content },
            { path: 'data.data.content', value: artifact.data?.data?.content },
            { path: 'data.rawContent', value: artifact.data?.rawContent },
            { path: 'originalContent', value: artifact.originalContent }
          ];

          for (const source of possibleContentSources) {
            if (source.value && typeof source.value === 'string' && source.value.length > 200) {
              console.log(`Found substantial content in optimized-content artifact at ${source.path}, length: ${source.value.length}`);
              return {
                content: source.value,
                source: `optimized-content.${source.path}`
              };
            }
          }
        }
      }

      // If no optimized content with substantial content, try final-content artifacts
      const finalContentArtifacts = finalOutput.artifacts
        .filter(a => a.type === 'final-content')
        .sort((a, b) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime());

      if (finalContentArtifacts.length > 0) {
        console.log('Found final content artifacts:', finalContentArtifacts.length);

        // Check each final content artifact for substantial content
        for (const artifact of finalContentArtifacts) {
          // Try to get content from various possible locations
          const possibleContentSources = [
            { path: 'data.content', value: artifact.data?.content },
            { path: 'content', value: artifact.content },
            { path: 'data.data.content', value: artifact.data?.data?.content },
            { path: 'data.rawContent', value: artifact.data?.rawContent },
            { path: 'originalContent', value: artifact.originalContent }
          ];

          for (const source of possibleContentSources) {
            if (source.value && typeof source.value === 'string' && source.value.length > 200) {
              console.log(`Found substantial content in final-content artifact at ${source.path}, length: ${source.value.length}`);
              return {
                content: source.value,
                source: `final-content.${source.path}`
              };
            }
          }
        }
      }

      // If still no substantial content, try any content artifact
      const contentArtifacts = finalOutput.artifacts
        .filter(a => a.type?.toLowerCase().includes('content'))
        .sort((a, b) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime());

      if (contentArtifacts.length > 0) {
        // Check each content artifact for substantial content
        for (const artifact of contentArtifacts) {
          // Try to get content from various possible locations
          const possibleContentSources = [
            { path: 'data.content', value: artifact.data?.content },
            { path: 'content', value: artifact.content },
            { path: 'data.data.content', value: artifact.data?.data?.content },
            { path: 'data.rawContent', value: artifact.data?.rawContent }
          ];

          for (const source of possibleContentSources) {
            if (source.value && typeof source.value === 'string' && source.value.length > 200) {
              console.log(`Found substantial content in ${artifact.type} artifact at ${source.path}, length: ${source.value.length}`);
              return {
                content: source.value,
                source: `${artifact.type}.${source.path}`
              };
            }
          }
        }
      }

      return null;
    };

    // Function to extract content from messages
    const extractContentFromMessages = (): { content: string, source: string } | null => {
      if (!finalOutput.messages || !Array.isArray(finalOutput.messages) || finalOutput.messages.length === 0) {
        return null;
      }

      console.log('Looking for content in messages:', {
        messagesCount: finalOutput.messages.length
      });

      // First, look for content-generation messages with forceRealContent flag
      const contentGenerationMessages = finalOutput.messages
        .filter(m =>
          (m.from === 'content-generation' || m.to === 'content-generation') &&
          (m.type === 'ARTIFACT_DELIVERY' || m.type === 'DISCUSSION_CONTRIBUTION')
        )
        .sort((a, b) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime());

      if (contentGenerationMessages.length > 0) {
        console.log('Found content generation messages:', contentGenerationMessages.length);

        // Check each message for content
        for (const message of contentGenerationMessages) {
          // Check if this is a response to a forceRealContent request
          const isRealContentResponse =
            message.from === 'content-generation' &&
            contentGenerationMessages.some(m =>
              m.to === 'content-generation' &&
              m.content?.forceRealContent === true &&
              m.conversationId === message.conversationId
            );

          if (isRealContentResponse) {
            console.log('Found response to forceRealContent request');
          }

          // Try to get content from various possible locations in the message
          const possibleContentSources = [
            { path: 'content.data', value: message.content?.data },
            { path: 'content.content', value: message.content?.content },
            { path: 'content.text', value: message.content?.text },
            { path: 'content.artifact.content', value: message.content?.artifact?.content },
            { path: 'content.artifact.data.content', value: message.content?.artifact?.data?.content },
            { path: 'content.response', value: message.content?.response }
          ];

          for (const source of possibleContentSources) {
            if (source.value && typeof source.value === 'string' && source.value.length > 200) {
              console.log(`Found substantial content in message at ${source.path}, length: ${source.value.length}`);
              return {
                content: source.value,
                source: `message.${message.from}.${source.path}`
              };
            }
          }
        }
      }

      return null;
    };

    // Extract the actual content from the artifact
    const extractActualContent = (obj: any): { content: string, source: string } => {
      // First check if we have direct content in the finalOutput that's substantial
      if (obj.content && typeof obj.content === 'string' && isSubstantialContent(obj.content)) {
        console.log('Using substantial content directly from finalOutput.content field');
        return { content: obj.content, source: 'direct.content' };
      }

      // Check if we have rawContent in the finalOutput that's substantial
      if (obj.rawContent && typeof obj.rawContent === 'string' && isSubstantialContent(obj.rawContent)) {
        console.log('Using substantial content from finalOutput.rawContent field');
        return { content: obj.rawContent, source: 'direct.rawContent' };
      }

      // Try to extract content from artifacts if available
      const artifactContent = extractContentFromArtifacts();
      if (artifactContent) {
        return artifactContent;
      }

      // Try to extract content from messages (especially from content-generation agent)
      const messageContent = extractContentFromMessages();
      if (messageContent) {
        return messageContent;
      }

      // If the object itself is a string and substantial, use it
      if (typeof obj === 'string' && isSubstantialContent(obj)) {
        return { content: obj, source: 'direct' };
      }

      // Check for content in messages array directly
      if (obj.messages && Array.isArray(obj.messages)) {
        // Look for the most recent content-generation message with substantial content
        const contentGenMessages = obj.messages
          .filter((msg: any) => msg.from === 'content-generation')
          .sort((a: any, b: any) => new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime());

        for (const msg of contentGenMessages) {
          if (msg.content) {
            // Check if this is a response to a forceRealContent request
            const isRealContentResponse = obj.messages.some((m: any) =>
              m.to === 'content-generation' &&
              m.content?.forceRealContent === true &&
              m.conversationId === msg.conversationId
            );

            if (isRealContentResponse) {
              console.log('Found a response to forceRealContent request in messages array');

              // Try to extract content from this message
              const contentValue = typeof msg.content === 'string'
                ? msg.content
                : msg.content?.content || msg.content?.data || msg.content?.text || msg.content?.response;

              if (contentValue && typeof contentValue === 'string' && contentValue.length > 100) {
                console.log(`Found content in response to forceRealContent, length: ${contentValue.length}`);
                return { content: contentValue, source: 'message.forceRealContent.response' };
              }
            }
          }
        }
      }

      // Check all possible locations for content
      const contentLocations = [
        { path: 'content', value: obj.content },
        { path: 'rawContent', value: obj.rawContent },
        { path: 'fullContent', value: obj.fullContent },
        { path: 'text', value: obj.text },
        { path: 'body', value: obj.body },
        { path: 'article', value: obj.article },
        { path: 'data.content', value: obj.data?.content },
        { path: 'data.rawContent', value: obj.data?.rawContent },
        { path: 'data.fullContent', value: obj.data?.fullContent },
        { path: 'metadata.content', value: obj.metadata?.content },
        { path: 'metadata.fullContent', value: obj.metadata?.fullContent },
        { path: 'finalContent', value: obj.finalContent },
        { path: 'generatedContent', value: obj.generatedContent }
      ];

      // First try to find substantial content
      for (const location of contentLocations) {
        if (location.value && typeof location.value === 'string' && isSubstantialContent(location.value)) {
          console.log(`Found substantial content in ${location.path}, length: ${location.value.length}`);
          return { content: location.value, source: location.path };
        }
      }

      // If no substantial content was found, look for any content
      for (const location of contentLocations) {
        if (location.value && typeof location.value === 'string' && location.value.length > 100) {
          console.log(`Found some content in ${location.path}, length: ${location.value.length}`);
          return { content: location.value, source: location.path };
        }
      }

      // If we still don't have content, create a placeholder
      return {
        content: `# ${obj.title || topic}\n\n## Content Generation in Progress\n\nThe AI agents are still working on generating high-quality content for your topic. Please check back later or click the Refresh button to see if new content is available.`,
        source: 'placeholder'
      };
    };

    // Extract the actual content
    const extracted = extractActualContent(finalOutput);
    content = extracted.content;
    source = extracted.source;

    // Convert content to string if it's not already
    const contentStr = typeof content === 'string'
      ? content
      : JSON.stringify(content, null, 2);

    console.log(`Using content from ${source}, length: ${contentStr.length}`);

    setProcessedContent(contentStr);
    setContentSource(source);

  }, [finalOutput, topic]);

  // If no finalOutput is provided, show a placeholder
  if (!finalOutput) {
    return (
      <div className="content-placeholder">
        <h3>No Content Available</h3>
        <p>Content generation has not started or is still in progress.</p>
        <button
          className="refresh-button"
          onClick={refreshContent}
          disabled={loading}
        >
          {loading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>
    );
  }

  // If we have finalOutput but no processed content, show a manual generation option
  if (!processedContent || processedContent.length < 100) {
    return (
      <div className="content-placeholder">
        <h3>Content Generation In Progress</h3>
        <p>The system is still working on generating content for "{topic}".</p>
        <p>If this persists, you can try manually refreshing or forcing content generation.</p>
        <div className="action-buttons">
          <button
            className="refresh-button"
            onClick={refreshContent}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh Content'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="content-viewer">
      <div className="content-header">
        <h2>{finalOutput.title || topic}</h2>

        {finalOutput.seoScore !== undefined && (
          <div className="seo-score-container">
            <span className="seo-label">SEO Score: {finalOutput.seoScore}/100</span>
            <div className="score-bar">
              <div
                className="score-fill"
                style={{
                  width: `${finalOutput.seoScore}%`,
                  backgroundColor: finalOutput.seoScore > 80 ? '#4caf50' :
                                    finalOutput.seoScore > 60 ? '#ff9800' : '#f44336'
                }}
              ></div>
            </div>
          </div>
        )}
      </div>

      <div className="content-body">
        {processedContent ? (
          <div className="markdown-container">
            <div className="content-actions">
              <div className="action-group">
                <button
                  className="action-button copy-button"
                  onClick={() => {
                    navigator.clipboard.writeText(processedContent);
                    alert('Content copied to clipboard!');
                  }}
                  title="Copy content to clipboard"
                >
                  <span className="icon">📋</span> Copy Content
                </button>
                <button
                  className="action-button export-button"
                  onClick={() => {
                    const blob = new Blob([processedContent], { type: 'text/markdown' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${finalOutput.title || topic}.md`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  title="Export as Markdown file"
                >
                  <span className="icon">📥</span> Export as MD
                </button>
                <button
                  className="action-button html-button"
                  onClick={() => {
                    // Create HTML version with basic styling
                    const htmlContent = `
                      <!DOCTYPE html>
                      <html>
                      <head>
                        <title>${finalOutput.title || topic}</title>
                        <style>
                          body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
                          h1 { color: #333; }
                          h2 { color: #444; margin-top: 25px; }
                          h3 { color: #555; }
                          pre { background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto; }
                          code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
                          blockquote { border-left: 4px solid #ddd; padding-left: 15px; color: #666; }
                          table { border-collapse: collapse; width: 100%; }
                          th, td { border: 1px solid #ddd; padding: 8px; }
                          th { background-color: #f2f2f2; }
                        </style>
                      </head>
                      <body>
                        <div id="content">
                          ${processedContent}
                        </div>
                      </body>
                      </html>
                    `;
                    const blob = new Blob([htmlContent], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${finalOutput.title || topic}.html`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  title="Export as HTML file"
                >
                  <span className="icon">📄</span> Export as HTML
                </button>
              </div>
              <div className="action-group">
                <button
                  className="action-button refresh-button"
                  onClick={refreshContent}
                  disabled={loading}
                  title="Refresh content from server"
                >
                  <span className="icon">{loading ? '⏳' : '🔄'}</span> {loading ? 'Refreshing...' : 'Refresh'}
                </button>
              </div>
            </div>

            <div className="content-preview">
              {contentSource !== 'placeholder' && (
                <div className="content-source">Source: {contentSource}</div>
              )}
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  // Custom handling for code blocks to ensure proper rendering
                  code: ({className, children, ...props}: any) => {
                    const inline = props.inline;
                    const match = /language-(\w+)/.exec(className || '');
                    return !inline && match ? (
                      <pre className={`language-${match[1]}`}>
                        <code className={`language-${match[1]}`} {...props}>
                          {String(children).replace(/\n$/, '')}
                        </code>
                      </pre>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  }
                }}
              >
                {processedContent}
              </ReactMarkdown>
            </div>

            {finalOutput.metadata && (
              <div className="content-metadata">
                <h3>Content Metadata</h3>
                <div className="metadata-grid">
                  {finalOutput.metadata.wordCount && (
                    <div className="metadata-item">
                      <span className="metadata-label">Word Count:</span>
                      <span className="metadata-value">{finalOutput.metadata.wordCount}</span>
                    </div>
                  )}
                  {finalOutput.metadata.readingTime && (
                    <div className="metadata-item">
                      <span className="metadata-label">Reading Time:</span>
                      <span className="metadata-value">{finalOutput.metadata.readingTime} min</span>
                    </div>
                  )}
                  {finalOutput.metadata.generatedAt && (
                    <div className="metadata-item">
                      <span className="metadata-label">Generated:</span>
                      <span className="metadata-value">{new Date(finalOutput.metadata.generatedAt).toLocaleString()}</span>
                    </div>
                  )}
                </div>

                {finalOutput.metadata.keywords && finalOutput.metadata.keywords.length > 0 && (
                  <div className="keywords-section">
                    <h4>Keywords</h4>
                    <div className="keywords-list">
                      {finalOutput.metadata.keywords.map((keyword: string, index: number) => (
                        <span key={index} className="keyword-tag">{keyword}</span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="content-placeholder">
            <p>No content available. The generation process may still be in progress.</p>
            <button
              className="refresh-button"
              onClick={refreshContent}
              disabled={loading}
            >
              <span className="icon">{loading ? '⏳' : '🔄'}</span> {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        )}
      </div>

      <div className="debug-section">
        <details>
          <summary>Debug Information</summary>
          <div className="debug-content">
            <p><strong>Content Source:</strong> {contentSource || 'None'}</p>
            <p><strong>Content Length:</strong> {processedContent.length} characters</p>
            <p><strong>Has Metadata:</strong> {finalOutput.metadata ? 'Yes' : 'No'}</p>
            <p><strong>SEO Score:</strong> {finalOutput.seoScore || 'N/A'}</p>
            <p><strong>Available Fields:</strong> {Object.keys(finalOutput).join(', ')}</p>
            {processedContent.length > 0 && (
              <div className="raw-content">
                <h4>Raw Content Preview (first 300 chars)</h4>
                <pre>
                  {processedContent.substring(0, 300)}
                  {processedContent.length > 300 ? '...' : ''}
                </pre>
              </div>
            )}
          </div>
        </details>
      </div>

      <style jsx>{`
        .content-viewer {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .content-header {
          margin-bottom: 1rem;
        }

        .content-header h2 {
          margin-bottom: 0.5rem;
          font-size: 1.75rem;
          color: #111827;
        }

        .seo-score-container {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          margin-top: 0.5rem;
        }

        .seo-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #4b5563;
        }

        .score-bar {
          height: 8px;
          background-color: #e5e7eb;
          border-radius: 4px;
          overflow: hidden;
        }

        .score-fill {
          height: 100%;
          border-radius: 4px;
        }

        .content-body {
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .markdown-container {
          display: flex;
          flex-direction: column;
        }

        .content-actions {
          display: flex;
          justify-content: space-between;
          padding: 1rem;
          background-color: #f9fafb;
          border-bottom: 1px solid #e5e7eb;
        }

        .action-group {
          display: flex;
          gap: 0.5rem;
        }

        .action-button {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.5rem 0.75rem;
          border: none;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .copy-button {
          background-color: #eff6ff;
          color: #1d4ed8;
        }

        .copy-button:hover {
          background-color: #dbeafe;
        }

        .export-button {
          background-color: #f0fdf4;
          color: #16a34a;
        }

        .export-button:hover {
          background-color: #dcfce7;
        }

        .html-button {
          background-color: #fef3c7;
          color: #d97706;
        }

        .html-button:hover {
          background-color: #fde68a;
        }

        .refresh-button {
          background-color: #f3f4f6;
          color: #4b5563;
        }

        .refresh-button:hover:not(:disabled) {
          background-color: #e5e7eb;
        }

        .refresh-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .icon {
          font-size: 1rem;
        }

        .content-preview {
          padding: 1.5rem;
          overflow-x: auto;
        }

        .content-source {
          font-size: 0.75rem;
          color: #6b7280;
          margin-bottom: 1rem;
          padding: 0.25rem 0.5rem;
          background-color: #f3f4f6;
          border-radius: 4px;
          display: inline-block;
        }

        .content-metadata {
          padding: 1.5rem;
          border-top: 1px solid #e5e7eb;
          background-color: #f9fafb;
        }

        .content-metadata h3 {
          font-size: 1.125rem;
          margin-bottom: 1rem;
          color: #374151;
        }

        .metadata-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 1rem;
          margin-bottom: 1.5rem;
        }

        .metadata-item {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
        }

        .metadata-label {
          font-size: 0.75rem;
          color: #6b7280;
        }

        .metadata-value {
          font-size: 0.875rem;
          font-weight: 500;
          color: #111827;
        }

        .keywords-section h4 {
          font-size: 1rem;
          margin-bottom: 0.75rem;
          color: #374151;
        }

        .keywords-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .keyword-tag {
          padding: 0.25rem 0.5rem;
          background-color: #eff6ff;
          color: #1d4ed8;
          border-radius: 4px;
          font-size: 0.75rem;
        }

        .content-placeholder {
          padding: 3rem;
          text-align: center;
          color: #6b7280;
        }

        .content-placeholder h3 {
          font-size: 1.25rem;
          margin-bottom: 0.5rem;
          color: #374151;
        }

        .content-placeholder p {
          margin-bottom: 1.5rem;
        }

        .content-placeholder .refresh-button {
          padding: 0.75rem 1.5rem;
          background-color: #3b82f6;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .content-placeholder .refresh-button:hover:not(:disabled) {
          background-color: #2563eb;
        }

        .content-placeholder .refresh-button:disabled {
          background-color: #9ca3af;
          cursor: not-allowed;
        }

        .debug-section {
          margin-top: 1rem;
        }

        .debug-section summary {
          cursor: pointer;
          padding: 0.5rem;
          background-color: #f3f4f6;
          border-radius: 4px;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .debug-content {
          padding: 1rem;
          background-color: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          margin-top: 0.5rem;
          font-size: 0.875rem;
        }

        .raw-content {
          margin-top: 1rem;
        }

        .raw-content h4 {
          font-size: 0.875rem;
          margin-bottom: 0.5rem;
          color: #4b5563;
        }

        .raw-content pre {
          background-color: #f3f4f6;
          padding: 0.75rem;
          border-radius: 4px;
          overflow-x: auto;
          font-size: 0.75rem;
          color: #1f2937;
        }
      `}</style>
    </div>
  );
};

export default ContentViewer;
