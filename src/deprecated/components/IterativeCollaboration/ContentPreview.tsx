// src/components/IterativeCollaboration/ContentPreview.tsx

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Tabs,
  Tab,
  Card,
  CardContent,
  Button,
  Chip,
  Grid
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CodeIcon from '@mui/icons-material/Code';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';

// Import types from our iterative collaboration system
import { IterativeArtifact } from '../../app/(payload)/api/agents/collaborative-iteration/types';

interface ContentPreviewProps {
  artifacts: IterativeArtifact[];
  contentType: string;
}

const ContentPreview: React.FC<ContentPreviewProps> = ({ artifacts, contentType }) => {
  const [viewMode, setViewMode] = useState<'preview' | 'html' | 'json'>('preview');
  
  // Find the final content artifact
  const findFinalContentArtifact = () => {
    // First look for artifacts with 'final' status
    const finalArtifacts = artifacts.filter(
      a => (a.type === 'draft-content' || a.type === 'optimized-content') && a.status === 'final'
    );
    
    if (finalArtifacts.length > 0) {
      // Return the one with the highest quality score
      return finalArtifacts.sort((a, b) => b.qualityScore - a.qualityScore)[0];
    }
    
    // If no final artifacts, look for the latest draft or optimized content
    const contentArtifacts = artifacts.filter(
      a => a.type === 'draft-content' || a.type === 'optimized-content'
    );
    
    if (contentArtifacts.length > 0) {
      // Return the one with the highest version number
      return contentArtifacts.sort((a, b) => b.currentVersion - a.currentVersion)[0];
    }
    
    return null;
  };
  
  const finalContentArtifact = findFinalContentArtifact();
  
  // If no content artifact is found, show a message
  if (!finalContentArtifact) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          No content has been generated yet. The agents will create content as they collaborate.
        </Typography>
      </Paper>
    );
  }
  
  // Get the latest iteration of the content
  const latestIteration = finalContentArtifact.iterations[finalContentArtifact.iterations.length - 1];
  const contentData = latestIteration.content;
  
  // Extract content based on the structure
  const extractContent = () => {
    if (!contentData) return '';
    
    // Handle different content structures
    if (typeof contentData === 'string') {
      return contentData;
    }
    
    if (contentData.fullContent) {
      return contentData.fullContent;
    }
    
    if (contentData.content) {
      return contentData.content;
    }
    
    if (contentData.sectionsContent) {
      // Combine all sections
      return Object.values(contentData.sectionsContent).join('\n\n');
    }
    
    if (contentData.sampleSection && contentData.sampleSection.content) {
      return contentData.sampleSection.content;
    }
    
    // If we can't find content in expected places, return JSON
    return JSON.stringify(contentData, null, 2);
  };
  
  // Extract metadata
  const extractMetadata = () => {
    if (!contentData) return {};
    
    if (contentData.metadata) {
      return contentData.metadata;
    }
    
    // Create a basic metadata object if none exists
    return {
      title: contentData.title || `Content for ${contentType}`,
      description: contentData.description || '',
      readabilityScore: contentData.readabilityScore || finalContentArtifact.qualityScore,
      keywordDensity: contentData.keywordDensity || {}
    };
  };
  
  const content = extractContent();
  const metadata = extractMetadata();
  
  // Render content based on view mode
  const renderContent = () => {
    switch (viewMode) {
      case 'preview':
        return (
          <Box sx={{ mt: 2 }}>
            <Paper 
              sx={{ 
                p: 3, 
                maxHeight: '600px', 
                overflow: 'auto',
                bgcolor: 'background.paper'
              }}
            >
              <div dangerouslySetInnerHTML={{ __html: content }} />
            </Paper>
          </Box>
        );
      
      case 'html':
        return (
          <Box sx={{ mt: 2 }}>
            <Paper 
              sx={{ 
                p: 3, 
                maxHeight: '600px', 
                overflow: 'auto',
                bgcolor: '#f5f5f5',
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                whiteSpace: 'pre-wrap'
              }}
            >
              {content}
            </Paper>
          </Box>
        );
      
      case 'json':
        return (
          <Box sx={{ mt: 2 }}>
            <Paper 
              sx={{ 
                p: 3, 
                maxHeight: '600px', 
                overflow: 'auto',
                bgcolor: '#f5f5f5',
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }}
            >
              <pre>{JSON.stringify(contentData, null, 2)}</pre>
            </Paper>
          </Box>
        );
    }
  };
  
  // Render metadata
  const renderMetadata = () => (
    <Card variant="outlined" sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          {metadata.title || 'Untitled Content'}
        </Typography>
        
        {metadata.description && (
          <Typography variant="body1" color="text.secondary" paragraph>
            {metadata.description}
          </Typography>
        )}
        
        <Divider sx={{ my: 2 }} />
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" color="text.secondary">
              Content Type
            </Typography>
            <Typography variant="body1">
              {contentType.replace('-', ' ')}
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" color="text.secondary">
              Quality Score
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body1" sx={{ mr: 1 }}>
                {finalContentArtifact.qualityScore}/100
              </Typography>
              {finalContentArtifact.qualityScore >= 85 && (
                <CheckCircleIcon color="success" fontSize="small" />
              )}
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" color="text.secondary">
              Readability Score
            </Typography>
            <Typography variant="body1">
              {metadata.readabilityScore || 'N/A'}
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="subtitle2" color="text.secondary">
              Status
            </Typography>
            <Chip 
              label={finalContentArtifact.status} 
              color={
                finalContentArtifact.status === 'final' ? 'success' :
                finalContentArtifact.status === 'revised' ? 'secondary' :
                finalContentArtifact.status === 'in-review' ? 'warning' : 'default'
              }
              size="small"
            />
          </Grid>
          
          {metadata.keywordDensity && Object.keys(metadata.keywordDensity).length > 0 && (
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Keyword Density
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Object.entries(metadata.keywordDensity).map(([keyword, density]) => (
                  <Chip 
                    key={keyword}
                    label={`${keyword}: ${density}`}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Grid>
          )}
          
          {contentData.improvement_suggestions && contentData.improvement_suggestions.length > 0 && (
            <Grid item xs={12}>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TipsAndUpdatesIcon color="warning" fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2">
                    Improvement Suggestions
                  </Typography>
                </Box>
                <Box component="ul" sx={{ pl: 4, mt: 0 }}>
                  {contentData.improvement_suggestions.map((suggestion: string, index: number) => (
                    <Box component="li" key={index}>
                      <Typography variant="body2">{suggestion}</Typography>
                    </Box>
                  ))}
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Content Preview
      </Typography>
      
      {renderMetadata()}
      
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Tabs
          value={viewMode}
          onChange={(_, newValue) => setViewMode(newValue)}
          aria-label="content view mode"
        >
          <Tab 
            icon={<VisibilityIcon fontSize="small" />} 
            label="Preview" 
            value="preview" 
          />
          <Tab 
            icon={<CodeIcon fontSize="small" />} 
            label="HTML" 
            value="html" 
          />
          <Tab 
            icon={<CodeIcon fontSize="small" />} 
            label="JSON" 
            value="json" 
          />
        </Tabs>
        
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          size="small"
        >
          Edit Content
        </Button>
      </Box>
      
      {renderContent()}
    </Box>
  );
};

export default ContentPreview;
