// src/components/IterativeCollaboration/AgentStateVisualizer.tsx

import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import DescriptionIcon from '@mui/icons-material/Description';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HistoryIcon from '@mui/icons-material/History';

// Import types from our iterative collaboration system
import { AgentState, IterativeArtifact, Consultation } from '../../app/(payload)/api/agents/collaborative-iteration/types';

interface AgentStateVisualizerProps {
  agentStates: AgentState[];
  artifacts: Record<string, IterativeArtifact>;
  consultations: Record<string, Consultation>;
}

const AgentStateVisualizer: React.FC<AgentStateVisualizerProps> = ({ 
  agentStates, 
  artifacts, 
  consultations 
}) => {
  // Get agent color
  const getAgentColor = (agentId: string) => {
    switch (agentId) {
      case 'market-research':
        return '#4caf50'; // green
      case 'seo-keyword':
        return '#2196f3'; // blue
      case 'content-strategy':
        return '#9c27b0'; // purple
      case 'content-generation':
        return '#ff9800'; // orange
      case 'seo-optimization':
        return '#f44336'; // red
      case 'system':
        return '#607d8b'; // blue-grey
      default:
        return '#9e9e9e'; // grey
    }
  };

  // Get agent name
  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'market-research':
        return 'Market Research Agent';
      case 'seo-keyword':
        return 'SEO Keyword Agent';
      case 'content-strategy':
        return 'Content Strategy Agent';
      case 'content-generation':
        return 'Content Generation Agent';
      case 'seo-optimization':
        return 'SEO Optimization Agent';
      case 'system':
        return 'System';
      default:
        return agentId;
    }
  };

  // Get agent role
  const getAgentRole = (agentId: string) => {
    switch (agentId) {
      case 'market-research':
        return 'Researches target audience and market trends';
      case 'seo-keyword':
        return 'Identifies optimal keywords for content';
      case 'content-strategy':
        return 'Develops content structure and approach';
      case 'content-generation':
        return 'Creates content based on strategy and research';
      case 'seo-optimization':
        return 'Optimizes content for search engines';
      case 'system':
        return 'Coordinates the collaboration process';
      default:
        return 'Unknown role';
    }
  };

  // Get artifact name by ID
  const getArtifactName = (artifactId: string) => {
    const artifact = artifacts[artifactId];
    return artifact ? artifact.name : `Unknown (${artifactId})`;
  };

  // Render agent cards
  const renderAgentCards = () => (
    <Grid container spacing={3}>
      {agentStates.map((agentState) => (
        <Grid item xs={12} md={6} key={agentState.id}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  sx={{ 
                    bgcolor: getAgentColor(agentState.id),
                    mr: 2
                  }}
                >
                  {agentState.id.charAt(0).toUpperCase()}
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {getAgentName(agentState.id)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {getAgentRole(agentState.id)}
                  </Typography>
                </Box>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4">
                      {agentState.processedRequests.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Requests Processed
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4">
                      {agentState.generatedArtifacts.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Artifacts Created
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4">
                      {agentState.consultationsProvided.length + agentState.consultationsReceived.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Consultations
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 2 }} />
              
              {/* Generated Artifacts */}
              {agentState.generatedArtifacts.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Generated Artifacts
                  </Typography>
                  <List dense disablePadding>
                    {agentState.generatedArtifacts.map((artifactId) => {
                      const artifact = artifacts[artifactId];
                      if (!artifact) return null;
                      
                      return (
                        <ListItem key={artifactId} sx={{ px: 0 }}>
                          <ListItemAvatar sx={{ minWidth: 36 }}>
                            <DescriptionIcon color="primary" fontSize="small" />
                          </ListItemAvatar>
                          <ListItemText 
                            primary={artifact.name}
                            secondary={`${artifact.type} - ${artifact.iterations.length} iterations`}
                          />
                          <Chip 
                            label={artifact.status} 
                            size="small" 
                            color={
                              artifact.status === 'final' ? 'success' :
                              artifact.status === 'revised' ? 'secondary' :
                              artifact.status === 'in-review' ? 'warning' : 'default'
                            }
                          />
                        </ListItem>
                      );
                    })}
                  </List>
                </Box>
              )}
              
              {/* Consultations Provided */}
              {agentState.consultationsProvided.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Consultations Provided
                  </Typography>
                  <List dense disablePadding>
                    {agentState.consultationsProvided.map((consultationId) => {
                      const consultation = consultations[consultationId];
                      if (!consultation) return null;
                      
                      return (
                        <ListItem key={consultationId} sx={{ px: 0 }}>
                          <ListItemAvatar sx={{ minWidth: 36 }}>
                            <QuestionAnswerIcon color="info" fontSize="small" />
                          </ListItemAvatar>
                          <ListItemText 
                            primary={`To: ${getAgentName(consultation.toAgent)}`}
                            secondary={`On: ${getArtifactName(consultation.artifactId)}`}
                          />
                          <Chip 
                            label={consultation.incorporated ? 'Incorporated' : 'Pending'} 
                            size="small" 
                            color={consultation.incorporated ? 'success' : 'warning'}
                          />
                        </ListItem>
                      );
                    })}
                  </List>
                </Box>
              )}
              
              {/* Consultations Received */}
              {agentState.consultationsReceived.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Consultations Received
                  </Typography>
                  <List dense disablePadding>
                    {agentState.consultationsReceived.map((consultationId) => {
                      const consultation = consultations[consultationId];
                      if (!consultation) return null;
                      
                      return (
                        <ListItem key={consultationId} sx={{ px: 0 }}>
                          <ListItemAvatar sx={{ minWidth: 36 }}>
                            <HistoryIcon color="secondary" fontSize="small" />
                          </ListItemAvatar>
                          <ListItemText 
                            primary={`From: ${getAgentName(consultation.fromAgent)}`}
                            secondary={`On: ${getArtifactName(consultation.artifactId)}`}
                          />
                          <Chip 
                            label={consultation.incorporated ? 'Incorporated' : 'Pending'} 
                            size="small" 
                            color={consultation.incorporated ? 'success' : 'warning'}
                          />
                        </ListItem>
                      );
                    })}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Agent States ({agentStates.length})
      </Typography>
      
      {agentStates.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1">
            No agent states available yet.
          </Typography>
        </Paper>
      ) : (
        renderAgentCards()
      )}
    </Box>
  );
};

export default AgentStateVisualizer;
