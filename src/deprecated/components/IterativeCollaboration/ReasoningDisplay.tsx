import React from "react";

type Reasoning = {
  thought: string;
  confidence?: number;
  timestamp?: string;
};

type ReasoningDisplayProps = {
  reasoning: Reasoning[];
};

const ReasoningDisplay: React.FC<ReasoningDisplayProps> = ({ reasoning }) => {
  if (!reasoning || reasoning.length === 0) {
    return <div>No reasoning available.</div>;
  }

  return (
    <div>
      {reasoning.map((item, idx) => (
        <div
          key={idx}
          style={{
            marginBottom: 12,
            padding: 12,
            border: "1px solid #ddd",
            borderRadius: 6,
            background: "#fafbfc"
          }}
        >
          <div><strong>Thought:</strong> {item.thought}</div>
          {item.confidence !== undefined && (
            <div><strong>Confidence:</strong> {item.confidence}</div>
          )}
          {item.timestamp && (
            <div style={{ color: "#888", fontSize: 12 }}>
              <strong>Timestamp:</strong> {item.timestamp}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ReasoningDisplay;
