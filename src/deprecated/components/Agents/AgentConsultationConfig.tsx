/**
 * Agent Consultation Configuration Component
 * 
 * Interface for configuring agent consultation settings and triggers
 */

'use client';

import { useState, useEffect } from 'react';

interface ConsultationTrigger {
  type: 'always' | 'quality_threshold' | 'feedback_keywords' | 'content_complexity';
  agents: string[];
  priority: 'low' | 'medium' | 'high';
  condition?: {
    threshold?: number;
    keywords?: string[];
  };
}

interface AgentConsultationConfig {
  enabled: boolean;
  triggers: ConsultationTrigger[];
  maxConsultations: number;
  timeoutMs: number;
  fallbackBehavior: 'continue' | 'fail';
}

interface AgentCapability {
  agentId: string;
  capabilities: string[];
  isAvailable: boolean;
}

interface Props {
  onConfigUpdate: (config: AgentConsultationConfig) => void;
  onNotification: (message: string) => void;
  initialConfig?: AgentConsultationConfig;
}

export default function AgentConsultationConfig({ 
  onConfigUpdate, 
  onNotification, 
  initialConfig 
}: Props) {
  const [config, setConfig] = useState<AgentConsultationConfig>(
    initialConfig || {
      enabled: true,
      triggers: [
        {
          type: 'always',
          agents: ['seo-keyword'],
          priority: 'high'
        }
      ],
      maxConsultations: 3,
      timeoutMs: 30000,
      fallbackBehavior: 'continue'
    }
  );

  const [availableAgents, setAvailableAgents] = useState<AgentCapability[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'basic' | 'triggers' | 'advanced'>('basic');

  useEffect(() => {
    loadAvailableAgents();
  }, []);

  useEffect(() => {
    onConfigUpdate(config);
  }, [config, onConfigUpdate]);

  const loadAvailableAgents = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/agents/selection/capabilities');
      const result = await response.json();
      
      if (result.success) {
        setAvailableAgents(result.data.agents);
      }
    } catch (error) {
      console.error('Failed to load agent capabilities:', error);
      onNotification('Failed to load agent capabilities');
    } finally {
      setIsLoading(false);
    }
  };

  const addTrigger = () => {
    setConfig(prev => ({
      ...prev,
      triggers: [
        ...prev.triggers,
        {
          type: 'quality_threshold',
          agents: ['content-strategy'],
          priority: 'medium',
          condition: { threshold: 0.8 }
        }
      ]
    }));
  };

  const updateTrigger = (index: number, updates: Partial<ConsultationTrigger>) => {
    setConfig(prev => ({
      ...prev,
      triggers: prev.triggers.map((trigger, i) => 
        i === index ? { ...trigger, ...updates } : trigger
      )
    }));
  };

  const removeTrigger = (index: number) => {
    setConfig(prev => ({
      ...prev,
      triggers: prev.triggers.filter((_, i) => i !== index)
    }));
  };

  const toggleAgent = (triggerIndex: number, agentId: string) => {
    const trigger = config.triggers[triggerIndex];
    const agents = trigger.agents.includes(agentId)
      ? trigger.agents.filter(id => id !== agentId)
      : [...trigger.agents, agentId];
    
    updateTrigger(triggerIndex, { agents });
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      default: return agentId;
    }
  };

  const getTriggerDescription = (type: string) => {
    switch (type) {
      case 'always': return 'Consult agents for every workflow execution';
      case 'quality_threshold': return 'Consult when content quality falls below threshold';
      case 'feedback_keywords': return 'Consult when specific keywords appear in feedback';
      case 'content_complexity': return 'Consult when content complexity exceeds threshold';
      default: return 'Custom trigger condition';
    }
  };

  const validateConfig = () => {
    const issues = [];
    
    if (config.triggers.length === 0) {
      issues.push('At least one trigger must be configured');
    }
    
    if (config.maxConsultations < 1) {
      issues.push('Max consultations must be at least 1');
    }
    
    if (config.timeoutMs < 1000) {
      issues.push('Timeout must be at least 1000ms');
    }
    
    config.triggers.forEach((trigger, index) => {
      if (trigger.agents.length === 0) {
        issues.push(`Trigger ${index + 1} must have at least one agent selected`);
      }
    });
    
    return issues;
  };

  const validationIssues = validateConfig();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Agent Consultation Configuration</h2>
            <p className="text-sm text-gray-600">
              Configure how and when agents should be consulted during workflow execution
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.enabled}
                onChange={(e) => setConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">Enable Agent Consultation</span>
            </label>
          </div>
        </div>

        {/* Validation Issues */}
        {validationIssues.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Configuration Issues</h3>
                <div className="mt-2 text-sm text-red-700">
                  <ul className="list-disc pl-5 space-y-1">
                    {validationIssues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            {(['basic', 'triggers', 'advanced'] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-3 px-6 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab === 'basic' && '⚙️ Basic Settings'}
                {tab === 'triggers' && '🎯 Consultation Triggers'}
                {tab === 'advanced' && '🔧 Advanced Options'}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Basic Settings Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Consultations
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={config.maxConsultations}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      maxConsultations: parseInt(e.target.value) || 1 
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maximum number of agents to consult per step
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Timeout (seconds)
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="300"
                    value={config.timeoutMs / 1000}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      timeoutMs: (parseInt(e.target.value) || 30) * 1000 
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    How long to wait for agent responses
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fallback Behavior
                  </label>
                  <select
                    value={config.fallbackBehavior}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      fallbackBehavior: e.target.value as 'continue' | 'fail' 
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="continue">Continue workflow</option>
                    <option value="fail">Fail workflow</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    What to do when consultation fails
                  </p>
                </div>
              </div>

              {/* Available Agents */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Available Agents</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {availableAgents.map((agent) => (
                    <div key={agent.agentId} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <span className="text-2xl">{getAgentIcon(agent.agentId)}</span>
                        <div>
                          <h4 className="font-medium text-gray-900">{getAgentName(agent.agentId)}</h4>
                          <p className="text-sm text-gray-500">{agent.agentId}</p>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div>
                          <span className="text-sm font-medium text-gray-700">Capabilities:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {agent.capabilities.map((capability) => (
                              <span
                                key={capability}
                                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {capability.replace('-', ' ')}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <span className={`w-2 h-2 rounded-full ${
                            agent.isAvailable ? 'bg-green-500' : 'bg-red-500'
                          }`}></span>
                          <span className="text-sm text-gray-600">
                            {agent.isAvailable ? 'Available' : 'Unavailable'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Triggers Tab */}
          {activeTab === 'triggers' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Consultation Triggers</h3>
                  <p className="text-sm text-gray-600">
                    Define when agents should be consulted during workflow execution
                  </p>
                </div>
                <button
                  onClick={addTrigger}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Add Trigger
                </button>
              </div>

              <div className="space-y-4">
                {config.triggers.map((trigger, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">Trigger {index + 1}</h4>
                      <button
                        onClick={() => removeTrigger(index)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Trigger Type
                        </label>
                        <select
                          value={trigger.type}
                          onChange={(e) => updateTrigger(index, { 
                            type: e.target.value as any,
                            condition: e.target.value === 'quality_threshold' ? { threshold: 0.8 } :
                                     e.target.value === 'feedback_keywords' ? { keywords: ['improve'] } :
                                     e.target.value === 'content_complexity' ? { threshold: 0.6 } : undefined
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="always">Always</option>
                          <option value="quality_threshold">Quality Threshold</option>
                          <option value="feedback_keywords">Feedback Keywords</option>
                          <option value="content_complexity">Content Complexity</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Priority
                        </label>
                        <select
                          value={trigger.priority}
                          onChange={(e) => updateTrigger(index, { priority: e.target.value as any })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>

                      {/* Condition Input */}
                      {trigger.type === 'quality_threshold' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Quality Threshold
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="1"
                            step="0.1"
                            value={trigger.condition?.threshold || 0.8}
                            onChange={(e) => updateTrigger(index, {
                              condition: { threshold: parseFloat(e.target.value) }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      )}

                      {trigger.type === 'feedback_keywords' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Keywords (comma-separated)
                          </label>
                          <input
                            type="text"
                            value={trigger.condition?.keywords?.join(', ') || ''}
                            onChange={(e) => updateTrigger(index, {
                              condition: { 
                                keywords: e.target.value.split(',').map(k => k.trim()).filter(Boolean)
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                            placeholder="improve, better, enhance"
                          />
                        </div>
                      )}

                      {trigger.type === 'content_complexity' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Complexity Threshold
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="1"
                            step="0.1"
                            value={trigger.condition?.threshold || 0.6}
                            onChange={(e) => updateTrigger(index, {
                              condition: { threshold: parseFloat(e.target.value) }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      )}
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-2">
                        {getTriggerDescription(trigger.type)}
                      </p>
                    </div>

                    {/* Agent Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Agents ({trigger.agents.length} selected)
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {availableAgents.map((agent) => (
                          <label
                            key={agent.agentId}
                            className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                              trigger.agents.includes(agent.agentId)
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <input
                              type="checkbox"
                              checked={trigger.agents.includes(agent.agentId)}
                              onChange={() => toggleAgent(index, agent.agentId)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-lg">{getAgentIcon(agent.agentId)}</span>
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">
                                {getAgentName(agent.agentId)}
                              </div>
                              <div className="text-xs text-gray-500">
                                {agent.capabilities.length} capabilities
                              </div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {config.triggers.length === 0 && (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Triggers Configured</h3>
                  <p className="text-gray-600 mb-4">
                    Add consultation triggers to enable agent consultation during workflow execution.
                  </p>
                  <button
                    onClick={addTrigger}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add Your First Trigger
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Advanced Configuration</h3>
                <p className="text-sm text-gray-600 mb-6">
                  Fine-tune agent consultation behavior and performance settings.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Performance Settings</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Parallel Consultation
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">
                        Allow multiple agents to be consulted simultaneously
                      </span>
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Retry Failed Consultations
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">
                        Automatically retry failed agent consultations
                      </span>
                    </label>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Logging & Monitoring</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Detailed Logging
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">
                        Log detailed consultation requests and responses
                      </span>
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Performance Metrics
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked={true}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">
                        Collect and store consultation performance metrics
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Configuration Preview */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Configuration Preview</h4>
                <pre className="text-sm text-gray-700 overflow-x-auto">
                  {JSON.stringify(config, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
