/**
 * Real-Time Consultation Monitor Component
 *
 * Monitors active consultations and displays real-time status with backend integration
 */

'use client';

import { useState, useEffect, useRef, useId } from 'react';

interface ActiveConsultation {
  consultationId: string;
  agentId: string;
  workflowExecutionId: string;
  stepId: string;
  startedAt: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  estimatedTimeRemaining?: number;
}

interface ConsultationEvent {
  id: string;
  timestamp: string;
  type: 'consultation_started' | 'consultation_progress' | 'consultation_completed' | 'consultation_failed';
  agentId: string;
  consultationId: string;
  message: string;
  data?: any;
}

interface Props {
  metrics: any;
  agentStatus: any[];
  onNotification: (message: string) => void;
}

export default function RealTimeConsultationMonitor({ metrics, agentStatus, onNotification }: Props) {
  const uniqueId = useId();
  const [activeConsultations, setActiveConsultations] = useState<ActiveConsultation[]>([]);
  const [recentEvents, setRecentEvents] = useState<ConsultationEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [clientTimestamp, setClientTimestamp] = useState<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const eventCountRef = useRef(0);

  useEffect(() => {
    // Set client timestamp to avoid hydration issues
    setClientTimestamp(Date.now());
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => stopMonitoring();
  }, [autoRefresh]);

  const startMonitoring = () => {
    if (intervalRef.current) return;

    setIsMonitoring(true);
    setConnectionStatus('connecting');

    // Simulate connection establishment
    setTimeout(() => {
      setConnectionStatus('connected');
      onNotification('Real-time monitoring started');
    }, 1000);

    // Poll for updates every 2 seconds
    intervalRef.current = setInterval(async () => {
      await fetchActiveConsultations();
      await fetchRecentEvents();
    }, 2000);
  };

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setIsMonitoring(false);
    setConnectionStatus('disconnected');
  };

  const fetchActiveConsultations = async () => {
    try {
      // In a real implementation, this would fetch from a real-time endpoint
      // For now, we'll simulate active consultations based on current metrics
      const simulatedConsultations: ActiveConsultation[] = [];

      // Simulate some active consultations if agents are available
      if (agentStatus.some(agent => agent.isAvailable)) {
        const activeAgents = agentStatus.filter(agent => agent.isAvailable).slice(0, 2);

        activeAgents.forEach((agent, index) => {
          if (clientTimestamp && Math.random() > 0.7) { // 30% chance of having an active consultation
            simulatedConsultations.push({
              consultationId: `consultation-${uniqueId}-${index}`,
              agentId: agent.agentId,
              workflowExecutionId: `exec-${uniqueId}`,
              stepId: 'content-creation',
              startedAt: new Date(clientTimestamp - Math.random() * 30000).toISOString(),
              status: Math.random() > 0.8 ? 'processing' : 'pending',
              progress: Math.floor(Math.random() * 100),
              estimatedTimeRemaining: Math.floor(Math.random() * 15000) + 5000
            });
          }
        });
      }

      setActiveConsultations(simulatedConsultations);
    } catch (error) {
      console.error('Failed to fetch active consultations:', error);
    }
  };

  const fetchRecentEvents = async () => {
    try {
      // Simulate real-time events
      if (Math.random() > 0.6) { // 40% chance of new event
        const eventTypes: ConsultationEvent['type'][] = [
          'consultation_started',
          'consultation_progress',
          'consultation_completed',
          'consultation_failed'
        ];

        const availableAgents = agentStatus.filter(agent => agent.isAvailable);
        if (availableAgents.length === 0) return;

        const randomAgent = availableAgents[Math.floor(Math.random() * availableAgents.length)];
        const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];

        const newEvent: ConsultationEvent = {
          id: `event-${++eventCountRef.current}`,
          timestamp: new Date().toISOString(),
          type: eventType,
          agentId: randomAgent.agentId,
          consultationId: `consultation-${uniqueId}`,
          message: generateEventMessage(eventType, randomAgent.agentId),
          data: generateEventData(eventType)
        };

        setRecentEvents(prev => [newEvent, ...prev.slice(0, 19)]); // Keep last 20 events
      }
    } catch (error) {
      console.error('Failed to fetch recent events:', error);
    }
  };

  const generateEventMessage = (type: ConsultationEvent['type'], agentId: string): string => {
    const agentName = getAgentName(agentId);

    switch (type) {
      case 'consultation_started':
        return `${agentName} consultation initiated`;
      case 'consultation_progress':
        return `${agentName} processing consultation`;
      case 'consultation_completed':
        return `${agentName} consultation completed successfully`;
      case 'consultation_failed':
        return `${agentName} consultation failed`;
      default:
        return `${agentName} event occurred`;
    }
  };

  const generateEventData = (type: ConsultationEvent['type']): any => {
    switch (type) {
      case 'consultation_started':
        return { priority: 'high', timeout: 30000 };
      case 'consultation_progress':
        return { progress: Math.floor(Math.random() * 100) };
      case 'consultation_completed':
        return {
          confidence: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
          processingTime: Math.floor(Math.random() * 5000) + 1000,
          suggestions: Math.floor(Math.random() * 5) + 1
        };
      case 'consultation_failed':
        return {
          error: 'Timeout exceeded',
          processingTime: Math.floor(Math.random() * 10000) + 5000
        };
      default:
        return {};
    }
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      default: return agentId;
    }
  };

  const getStatusColor = (status: ActiveConsultation['status']) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEventTypeIcon = (type: ConsultationEvent['type']) => {
    switch (type) {
      case 'consultation_started': return '🚀';
      case 'consultation_progress': return '⏳';
      case 'consultation_completed': return '✅';
      case 'consultation_failed': return '❌';
      default: return '📝';
    }
  };

  const getEventTypeColor = (type: ConsultationEvent['type']) => {
    switch (type) {
      case 'consultation_started': return 'text-blue-600';
      case 'consultation_progress': return 'text-yellow-600';
      case 'consultation_completed': return 'text-green-600';
      case 'consultation_failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatTimeRemaining = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    return `${seconds}s remaining`;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="space-y-6">
      {/* Connection Status and Controls */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Real-Time Consultation Monitor</h2>
            <p className="text-sm text-gray-600">
              Live monitoring of agent consultations and system activity
            </p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
              }`}></div>
              <span className="text-sm text-gray-700 capitalize">{connectionStatus}</span>
            </div>

            {/* Auto Refresh Toggle */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Auto Refresh</span>
            </label>

            {/* Manual Refresh */}
            <button
              onClick={() => {
                fetchActiveConsultations();
                fetchRecentEvents();
              }}
              className="px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">
              {metrics?.totalConsultations || 0}
            </div>
            <div className="text-sm text-gray-600">Total Consultations</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">
              {agentStatus.filter(a => a.isAvailable).length}
            </div>
            <div className="text-sm text-gray-600">Available Agents</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600">
              {activeConsultations.length}
            </div>
            <div className="text-sm text-gray-600">Active Consultations</div>
          </div>

          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">
              {Math.round((metrics?.successRate || 0) * 100)}%
            </div>
            <div className="text-sm text-gray-600">Success Rate</div>
          </div>
        </div>

        {/* Agent Status Grid */}
        <div>
          <h3 className="font-medium text-gray-900 mb-3">Agent Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {agentStatus.map((agent) => {
              const activeCount = activeConsultations.filter(c => c.agentId === agent.agentId).length;

              return (
                <div key={agent.agentId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">{getAgentIcon(agent.agentId)}</span>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{getAgentName(agent.agentId)}</h4>
                      <p className="text-sm text-gray-500">{agent.agentId}</p>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${agent.isAvailable ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className={agent.isAvailable ? 'text-green-600' : 'text-red-600'}>
                        {agent.isAvailable ? 'Available' : 'Unavailable'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active:</span>
                      <span className="text-gray-900">{activeCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Max Concurrent:</span>
                      <span className="text-gray-900">{agent.status?.maxConcurrentConsultations || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Active Consultations */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Active Consultations ({activeConsultations.length})
          </h3>
          {activeConsultations.length > 0 && (
            <span className="text-sm text-gray-600">
              Real-time updates every 2 seconds
            </span>
          )}
        </div>

        {activeConsultations.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">No Active Consultations</h4>
            <p className="text-gray-600">
              Active consultations will appear here when workflows with agent consultation are running.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {activeConsultations.map((consultation) => (
              <div key={consultation.consultationId} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{getAgentIcon(consultation.agentId)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {getAgentName(consultation.agentId)}
                      </h4>
                      <p className="text-sm text-gray-500">
                        Step: {consultation.stepId} | Execution: {consultation.workflowExecutionId.slice(-8)}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(consultation.status)}`}>
                      {consultation.status}
                    </span>
                    <div className="text-sm text-gray-500 mt-1">
                      Started: {formatTimestamp(consultation.startedAt)}
                    </div>
                  </div>
                </div>

                {consultation.status === 'processing' && (
                  <div className="mb-3">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{consultation.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${consultation.progress}%` }}
                      ></div>
                    </div>
                    {consultation.estimatedTimeRemaining && (
                      <div className="text-sm text-gray-500 mt-1">
                        {formatTimeRemaining(consultation.estimatedTimeRemaining)}
                      </div>
                    )}
                  </div>
                )}

                <div className="text-xs text-gray-500">
                  Consultation ID: {consultation.consultationId.slice(-12)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Recent Events */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Recent Events ({recentEvents.length})
          </h3>
          {recentEvents.length > 0 && (
            <button
              onClick={() => setRecentEvents([])}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear Events
            </button>
          )}
        </div>

        {recentEvents.length === 0 ? (
          <div className="text-center py-6">
            <div className="text-gray-400 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-gray-600">No recent events</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {recentEvents.map((event) => (
              <div key={event.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <span className="text-lg">{getEventTypeIcon(event.type)}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{getAgentIcon(event.agentId)}</span>
                    <span className={`text-sm font-medium ${getEventTypeColor(event.type)}`}>
                      {event.message}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {formatTimestamp(event.timestamp)} | {event.consultationId.slice(-8)}
                  </div>
                  {event.data && Object.keys(event.data).length > 0 && (
                    <div className="text-xs text-gray-600 mt-1">
                      {event.type === 'consultation_completed' && event.data.confidence && (
                        <span>Confidence: {Math.round(event.data.confidence * 100)}% | </span>
                      )}
                      {event.data.processingTime && (
                        <span>Time: {event.data.processingTime}ms</span>
                      )}
                      {event.data.suggestions && (
                        <span> | Suggestions: {event.data.suggestions}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
