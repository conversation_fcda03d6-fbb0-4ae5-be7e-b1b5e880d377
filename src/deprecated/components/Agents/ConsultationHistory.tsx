/**
 * Consultation History Component
 * 
 * Displays historical agent consultation data and results
 */

'use client';

import { useState, useEffect } from 'react';

interface ConsultationRecord {
  consultationId: string;
  agentId: string;
  workflowExecutionId: string;
  stepId: string;
  confidence: number;
  processingTime: number;
  suggestions: Array<{
    area: string;
    suggestion: string;
    priority: string;
  }>;
  timestamp: string;
}

interface Props {
  onNotification: (message: string) => void;
}

export default function ConsultationHistory({ onNotification }: Props) {
  const [history, setHistory] = useState<Record<string, ConsultationRecord[]>>({});
  const [selectedExecution, setSelectedExecution] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/agents/consultation?type=history');
      const result = await response.json();
      
      if (result.success) {
        setHistory(result.data.history || {});
      }
    } catch (error) {
      console.error('Failed to load consultation history:', error);
      onNotification('Failed to load consultation history');
    } finally {
      setIsLoading(false);
    }
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      default: return agentId;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const executionIds = Object.keys(history);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading consultation history...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Consultation History</h2>
            <p className="text-sm text-gray-600">
              Review past agent consultations and their results
            </p>
          </div>
          <button
            onClick={loadHistory}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Refresh
          </button>
        </div>

        {executionIds.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Consultation History</h3>
            <p className="text-gray-600">
              Agent consultations will appear here once workflows with agent consultation are executed.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Execution List */}
            <div className="lg:col-span-1">
              <h3 className="font-medium text-gray-900 mb-3">Workflow Executions</h3>
              <div className="space-y-2">
                {executionIds.map((executionId) => {
                  const consultations = history[executionId];
                  const totalConsultations = consultations.length;
                  const avgConfidence = consultations.reduce((sum, c) => sum + c.confidence, 0) / totalConsultations;
                  
                  return (
                    <button
                      key={executionId}
                      onClick={() => setSelectedExecution(executionId)}
                      className={`w-full text-left p-3 rounded-lg border transition-colors ${
                        selectedExecution === executionId
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-gray-900 mb-1">
                        {executionId.slice(-8)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {totalConsultations} consultations
                      </div>
                      <div className="text-sm text-gray-600">
                        {Math.round(avgConfidence * 100)}% avg confidence
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Consultation Details */}
            <div className="lg:col-span-2">
              {selectedExecution ? (
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">
                    Consultations for {selectedExecution.slice(-8)}
                  </h3>
                  <div className="space-y-4">
                    {history[selectedExecution].map((consultation) => (
                      <div key={consultation.consultationId} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{getAgentIcon(consultation.agentId)}</span>
                            <div>
                              <h4 className="font-medium text-gray-900">
                                {getAgentName(consultation.agentId)}
                              </h4>
                              <p className="text-sm text-gray-500">
                                Step: {consultation.stepId}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">
                              {Math.round(consultation.confidence * 100)}% confidence
                            </div>
                            <div className="text-sm text-gray-500">
                              {consultation.processingTime}ms
                            </div>
                          </div>
                        </div>

                        {consultation.suggestions.length > 0 && (
                          <div>
                            <h5 className="text-sm font-medium text-gray-900 mb-2">
                              Suggestions ({consultation.suggestions.length})
                            </h5>
                            <div className="space-y-2">
                              {consultation.suggestions.slice(0, 3).map((suggestion, index) => (
                                <div key={index} className="flex items-start space-x-2">
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(suggestion.priority)}`}>
                                    {suggestion.priority}
                                  </span>
                                  <div className="flex-1">
                                    <div className="text-sm font-medium text-gray-900">
                                      {suggestion.area}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                      {suggestion.suggestion}
                                    </div>
                                  </div>
                                </div>
                              ))}
                              {consultation.suggestions.length > 3 && (
                                <div className="text-sm text-gray-500">
                                  +{consultation.suggestions.length - 3} more suggestions
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="text-xs text-gray-500">
                            {new Date(consultation.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Select an Execution</h3>
                  <p className="text-gray-600">
                    Choose a workflow execution from the list to view its consultation details.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
