/**
 * Agent Activity Monitor Component
 *
 * Monitors agent activity during workflow execution with real backend integration
 */

'use client';

import { useState, useEffect, useRef } from 'react';

interface AgentActivity {
  id: string;
  timestamp: string;
  executionId: string;
  stepId: string;
  agentId: string;
  action: 'consultation_requested' | 'consultation_started' | 'consultation_progress' | 'consultation_completed' | 'consultation_failed' | 'suggestion_generated';
  message: string;
  data?: {
    confidence?: number;
    processingTime?: number;
    suggestions?: number;
    progress?: number;
    error?: string;
    stepName?: string;
  };
}

interface ExecutionSummary {
  executionId: string;
  status: 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  totalSteps: number;
  completedSteps: number;
  agentConsultations: number;
  successfulConsultations: number;
  failedConsultations: number;
  averageConfidence: number;
  totalProcessingTime: number;
}

interface Props {
  executionId: string | null;
  metrics: any;
  onNotification: (message: string) => void;
}

export default function AgentActivityMonitor({ executionId, metrics, onNotification }: Props) {
  const [activityLog, setActivityLog] = useState<AgentActivity[]>([]);
  const [executionSummary, setExecutionSummary] = useState<ExecutionSummary | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const logContainerRef = useRef<HTMLDivElement>(null);
  const activityCountRef = useRef(0);

  useEffect(() => {
    if (executionId) {
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => stopMonitoring();
  }, [executionId]);

  useEffect(() => {
    if (autoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = 0; // Scroll to top since we prepend new items
    }
  }, [activityLog, autoScroll]);

  const startMonitoring = () => {
    if (!executionId || intervalRef.current) return;

    setIsMonitoring(true);
    setActivityLog([]);
    setExecutionSummary({
      executionId,
      status: 'running',
      startedAt: new Date().toISOString(),
      totalSteps: 0,
      completedSteps: 0,
      agentConsultations: 0,
      successfulConsultations: 0,
      failedConsultations: 0,
      averageConfidence: 0,
      totalProcessingTime: 0
    });

    onNotification(`Started monitoring execution: ${executionId.slice(-8)}`);

    // Poll for activity updates every 2 seconds
    intervalRef.current = setInterval(async () => {
      await fetchActivityUpdates();
      await fetchExecutionSummary();
    }, 2000);

    // Generate initial activity
    generateInitialActivity();
  };

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsMonitoring(false);
  };

  const fetchActivityUpdates = async () => {
    try {
      // In a real implementation, this would fetch from the backend
      // For now, we'll simulate real-time activity updates
      if (Math.random() > 0.4) { // 60% chance of new activity
        generateRandomActivity();
      }
    } catch (error) {
      console.error('Failed to fetch activity updates:', error);
    }
  };

  const fetchExecutionSummary = async () => {
    try {
      // In a real implementation, this would fetch execution status from the backend
      // For now, we'll simulate progress updates
      if (executionSummary && executionSummary.status === 'running') {
        const completedSteps = Math.min(executionSummary.completedSteps + (Math.random() > 0.7 ? 1 : 0), 5);
        const consultations = activityLog.filter(a => a.action === 'consultation_completed').length;
        const failed = activityLog.filter(a => a.action === 'consultation_failed').length;

        const avgConfidence = activityLog
          .filter(a => a.data?.confidence)
          .reduce((sum, a) => sum + (a.data?.confidence || 0), 0) / Math.max(consultations, 1);

        const totalTime = activityLog
          .filter(a => a.data?.processingTime)
          .reduce((sum, a) => sum + (a.data?.processingTime || 0), 0);

        setExecutionSummary(prev => prev ? {
          ...prev,
          totalSteps: 5,
          completedSteps,
          agentConsultations: consultations + failed,
          successfulConsultations: consultations,
          failedConsultations: failed,
          averageConfidence: avgConfidence,
          totalProcessingTime: totalTime,
          status: completedSteps >= 5 ? 'completed' : 'running',
          completedAt: completedSteps >= 5 ? new Date().toISOString() : undefined
        } : null);

        if (completedSteps >= 5 && executionSummary.status === 'running') {
          stopMonitoring();
          onNotification('Workflow execution completed');
        }
      }
    } catch (error) {
      console.error('Failed to fetch execution summary:', error);
    }
  };

  const generateInitialActivity = () => {
    const initialActivities: AgentActivity[] = [
      {
        id: `activity-${++activityCountRef.current}`,
        timestamp: new Date().toISOString(),
        executionId: executionId!,
        stepId: 'topic-input',
        agentId: 'system',
        action: 'consultation_requested',
        message: 'Workflow execution started',
        data: { stepName: 'Topic Input' }
      }
    ];

    setActivityLog(initialActivities);
  };

  const generateRandomActivity = () => {
    const agents = ['seo-keyword', 'market-research', 'content-strategy'];
    const steps = ['keyword-research', 'content-creation', 'seo-optimization', 'final-review'];
    const actions: AgentActivity['action'][] = [
      'consultation_requested',
      'consultation_started',
      'consultation_progress',
      'consultation_completed',
      'consultation_failed',
      'suggestion_generated'
    ];

    const randomAgent = agents[Math.floor(Math.random() * agents.length)];
    const randomStep = steps[Math.floor(Math.random() * steps.length)];
    const randomAction = actions[Math.floor(Math.random() * actions.length)];

    const newActivity: AgentActivity = {
      id: `activity-${++activityCountRef.current}`,
      timestamp: new Date().toISOString(),
      executionId: executionId!,
      stepId: randomStep,
      agentId: randomAgent,
      action: randomAction,
      message: generateActivityMessage(randomAgent, randomAction, randomStep),
      data: generateActivityData(randomAction)
    };

    setActivityLog(prev => [newActivity, ...prev.slice(0, 99)]); // Keep last 100 activities
  };

  const generateActivityMessage = (agentId: string, action: AgentActivity['action'], stepId: string): string => {
    const agentName = getAgentName(agentId);
    const stepName = getStepName(stepId);

    switch (action) {
      case 'consultation_requested':
        return `${agentName} consultation requested for ${stepName}`;
      case 'consultation_started':
        return `${agentName} consultation started for ${stepName}`;
      case 'consultation_progress':
        return `${agentName} processing consultation for ${stepName}`;
      case 'consultation_completed':
        return `${agentName} consultation completed for ${stepName}`;
      case 'consultation_failed':
        return `${agentName} consultation failed for ${stepName}`;
      case 'suggestion_generated':
        return `${agentName} generated suggestions for ${stepName}`;
      default:
        return `${agentName} activity in ${stepName}`;
    }
  };

  const generateActivityData = (action: AgentActivity['action']): AgentActivity['data'] => {
    switch (action) {
      case 'consultation_progress':
        return { progress: Math.floor(Math.random() * 100) };
      case 'consultation_completed':
        return {
          confidence: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
          processingTime: Math.floor(Math.random() * 5000) + 1000,
          suggestions: Math.floor(Math.random() * 5) + 1
        };
      case 'consultation_failed':
        return {
          error: 'Timeout exceeded',
          processingTime: Math.floor(Math.random() * 10000) + 5000
        };
      case 'suggestion_generated':
        return {
          suggestions: Math.floor(Math.random() * 3) + 1
        };
      default:
        return {};
    }
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      case 'system': return '⚙️';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      case 'system': return 'System';
      default: return agentId;
    }
  };

  const getStepName = (stepId: string) => {
    switch (stepId) {
      case 'topic-input': return 'Topic Input';
      case 'keyword-research': return 'Keyword Research';
      case 'content-creation': return 'Content Creation';
      case 'seo-optimization': return 'SEO Optimization';
      case 'final-review': return 'Final Review';
      default: return stepId;
    }
  };

  const getActionColor = (action: AgentActivity['action']) => {
    switch (action) {
      case 'consultation_requested': return 'text-blue-600';
      case 'consultation_started': return 'text-indigo-600';
      case 'consultation_progress': return 'text-yellow-600';
      case 'consultation_completed': return 'text-green-600';
      case 'consultation_failed': return 'text-red-600';
      case 'suggestion_generated': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  const getActionIcon = (action: AgentActivity['action']) => {
    switch (action) {
      case 'consultation_requested': return '📋';
      case 'consultation_started': return '🚀';
      case 'consultation_progress': return '⏳';
      case 'consultation_completed': return '✅';
      case 'consultation_failed': return '❌';
      case 'suggestion_generated': return '💡';
      default: return '📝';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getFilteredActivities = () => {
    if (!selectedAgent) return activityLog;
    return activityLog.filter(activity => activity.agentId === selectedAgent);
  };

  const getUniqueAgents = () => {
    const agents = Array.from(new Set(activityLog.map(a => a.agentId)));
    return agents.sort();
  };

  return (
    <div className="space-y-6">
      {/* Execution Summary */}
      {executionSummary && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Agent Activity Monitor</h2>
              <p className="text-sm text-gray-600">
                Monitoring execution: {executionSummary.executionId.slice(-8)}
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                executionSummary.status === 'running' ? 'bg-blue-100 text-blue-800' :
                executionSummary.status === 'completed' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              }`}>
                {executionSummary.status === 'running' && '🔄 Running'}
                {executionSummary.status === 'completed' && '✅ Completed'}
                {executionSummary.status === 'failed' && '❌ Failed'}
              </div>

              {isMonitoring && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Live</span>
                </div>
              )}
            </div>
          </div>

          {/* Summary Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {executionSummary.completedSteps}/{executionSummary.totalSteps}
              </div>
              <div className="text-sm text-gray-600">Steps</div>
            </div>

            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{executionSummary.agentConsultations}</div>
              <div className="text-sm text-gray-600">Consultations</div>
            </div>

            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(executionSummary.averageConfidence * 100)}%
              </div>
              <div className="text-sm text-gray-600">Avg Confidence</div>
            </div>

            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {formatDuration(executionSummary.totalProcessingTime)}
              </div>
              <div className="text-sm text-gray-600">Processing Time</div>
            </div>

            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{executionSummary.successfulConsultations}</div>
              <div className="text-sm text-gray-600">Successful</div>
            </div>

            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{executionSummary.failedConsultations}</div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
          </div>

          {/* Progress Bar */}
          {executionSummary.totalSteps > 0 && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Execution Progress</span>
                <span>{Math.round((executionSummary.completedSteps / executionSummary.totalSteps) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(executionSummary.completedSteps / executionSummary.totalSteps) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Activity Log */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Activity Log ({getFilteredActivities().length})
          </h3>

          <div className="flex items-center space-x-4">
            {/* Agent Filter */}
            <select
              value={selectedAgent || ''}
              onChange={(e) => setSelectedAgent(e.target.value || null)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="">All Agents</option>
              {getUniqueAgents().map(agentId => (
                <option key={agentId} value={agentId}>
                  {getAgentName(agentId)}
                </option>
              ))}
            </select>

            {/* Auto Scroll Toggle */}
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Auto Scroll</span>
            </label>

            {/* Clear Log */}
            <button
              onClick={() => {
                setActivityLog([]);
                onNotification('Activity log cleared');
              }}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear Log
            </button>
          </div>
        </div>

        {getFilteredActivities().length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              {executionId ? 'No Activity Yet' : 'No Active Execution'}
            </h4>
            <p className="text-gray-600">
              {executionId
                ? 'Agent activity will appear here as the workflow executes.'
                : 'Start a workflow execution to monitor agent activity in real-time.'
              }
            </p>
          </div>
        ) : (
          <div
            ref={logContainerRef}
            className="space-y-3 max-h-96 overflow-y-auto"
          >
            {getFilteredActivities().map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getAgentIcon(activity.agentId)}</span>
                  <span className="text-sm">{getActionIcon(activity.action)}</span>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className={`font-medium ${getActionColor(activity.action)}`}>
                      {activity.message}
                    </span>
                  </div>

                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>{formatTimestamp(activity.timestamp)}</span>
                    <span>Step: {getStepName(activity.stepId)}</span>
                    <span>Agent: {getAgentName(activity.agentId)}</span>
                  </div>

                  {/* Activity Data */}
                  {activity.data && Object.keys(activity.data).length > 0 && (
                    <div className="mt-2 text-xs text-gray-600">
                      {activity.data.confidence && (
                        <span className="mr-3">Confidence: {Math.round(activity.data.confidence * 100)}%</span>
                      )}
                      {activity.data.processingTime && (
                        <span className="mr-3">Time: {formatDuration(activity.data.processingTime)}</span>
                      )}
                      {activity.data.suggestions && (
                        <span className="mr-3">Suggestions: {activity.data.suggestions}</span>
                      )}
                      {activity.data.progress !== undefined && (
                        <span className="mr-3">Progress: {activity.data.progress}%</span>
                      )}
                      {activity.data.error && (
                        <span className="text-red-600">Error: {activity.data.error}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Agent Performance Summary */}
      {activityLog.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Agent Performance Summary</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {getUniqueAgents().filter(agentId => agentId !== 'system').map(agentId => {
              const agentActivities = activityLog.filter(a => a.agentId === agentId);
              const completedConsultations = agentActivities.filter(a => a.action === 'consultation_completed');
              const failedConsultations = agentActivities.filter(a => a.action === 'consultation_failed');
              const avgConfidence = completedConsultations.reduce((sum, a) => sum + (a.data?.confidence || 0), 0) / Math.max(completedConsultations.length, 1);
              const avgProcessingTime = completedConsultations.reduce((sum, a) => sum + (a.data?.processingTime || 0), 0) / Math.max(completedConsultations.length, 1);

              return (
                <div key={agentId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">{getAgentIcon(agentId)}</span>
                    <div>
                      <h4 className="font-medium text-gray-900">{getAgentName(agentId)}</h4>
                      <p className="text-sm text-gray-500">{agentActivities.length} activities</p>
                    </div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Completed:</span>
                      <span className="text-green-600">{completedConsultations.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Failed:</span>
                      <span className="text-red-600">{failedConsultations.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Confidence:</span>
                      <span className="text-gray-900">{Math.round(avgConfidence * 100)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Time:</span>
                      <span className="text-gray-900">{formatDuration(avgProcessingTime)}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
