/**
 * Agent Selection Tester Component
 * 
 * Interactive tool for testing agent selection logic and consultation triggers
 */

'use client';

import { useState, useId, useEffect } from 'react';

interface Props {
  onNotification: (message: string) => void;
}

interface SelectionResult {
  recommendedAgents: string[];
  agentDetails: Array<{
    agentId: string;
    capabilities: string[];
    isAvailable: boolean;
    status: any;
  }>;
  shouldTriggerConsultation: boolean;
  triggerReasons: string[];
  selectionReasoning: string[];
  contextAnalysis: {
    topic: string;
    contentType: string;
    targetAudience: string;
    complexity: number;
    hasFeedback: boolean;
    qualityScore?: number;
  };
}

interface ConsultationResult {
  consultations: Array<{
    consultationId: string;
    agentId: string;
    response: any;
    confidence: number;
    suggestions: any[];
    processingTime: number;
  }>;
  summary: {
    totalConsultations: number;
    averageConfidence: number;
    consultedAgents: string[];
  };
}

export default function AgentSelectionTester({ onNotification }: Props) {
  const uniqueId = useId();
  const [clientId, setClientId] = useState<string>('');
  const [context, setContext] = useState({
    topic: 'sustainable fashion trends',
    contentType: 'blog-post',
    targetAudience: 'eco-conscious consumers',
    primaryKeyword: 'sustainable fashion',
    industry: 'fashion',
    goals: ['educate', 'inspire action'],
    feedback: '',
    qualityScore: 0.7,
    complexity: 0.5
  });

  const [consultationConfig, setConsultationConfig] = useState({
    enabled: true,
    triggers: [
      {
        type: 'always',
        agents: ['seo-keyword', 'market-research'],
        priority: 'high'
      }
    ],
    maxConsultations: 3,
    timeoutMs: 30000,
    fallbackBehavior: 'continue'
  });

  const [selectionResult, setSelectionResult] = useState<SelectionResult | null>(null);
  const [consultationResult, setConsultationResult] = useState<ConsultationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'context' | 'config' | 'results'>('context');

  // Generate client-side ID to avoid hydration issues
  useEffect(() => {
    setClientId(`test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  }, []);

  const testAgentSelection = async () => {
    try {
      setIsLoading(true);
      onNotification('Testing agent selection...');

      const response = await fetch('/api/agents/selection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          context,
          consultationConfig
        })
      });

      const result = await response.json();

      if (result.success) {
        setSelectionResult(result.data);
        onNotification(`Selected ${result.data.recommendedAgents.length} agents for consultation`);
      } else {
        onNotification(`Selection failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Agent selection test failed:', error);
      onNotification('Agent selection test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testConsultation = async () => {
    if (!selectionResult?.recommendedAgents.length) {
      onNotification('No agents selected. Run agent selection first.');
      return;
    }

    try {
      setIsLoading(true);
      onNotification('Testing agent consultation...');

      const response = await fetch('/api/agents/consultation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          workflowExecutionId: clientId || `test-${uniqueId}`,
          stepId: 'test-step',
          context,
          consultationConfig
        })
      });

      const result = await response.json();

      if (result.success) {
        setConsultationResult(result.data);
        onNotification(`Consultation completed with ${result.data.summary.totalConsultations} agents`);
      } else {
        onNotification(`Consultation failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Agent consultation test failed:', error);
      onNotification('Agent consultation test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const addTrigger = () => {
    setConsultationConfig(prev => ({
      ...prev,
      triggers: [
        ...prev.triggers,
        {
          type: 'quality_threshold',
          agents: ['content-strategy'],
          priority: 'medium'
        }
      ]
    }));
  };

  const removeTrigger = (index: number) => {
    setConsultationConfig(prev => ({
      ...prev,
      triggers: prev.triggers.filter((_, i) => i !== index)
    }));
  };

  const getAgentIcon = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return '🔍';
      case 'market-research': return '📊';
      case 'content-strategy': return '📝';
      default: return '🤖';
    }
  };

  const getAgentName = (agentId: string) => {
    switch (agentId) {
      case 'seo-keyword': return 'SEO Keyword Agent';
      case 'market-research': return 'Market Research Agent';
      case 'content-strategy': return 'Content Strategy Agent';
      default: return agentId;
    }
  };

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            {(['context', 'config', 'results'] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-3 px-6 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab === 'context' && '📝 Context'}
                {tab === 'config' && '⚙️ Configuration'}
                {tab === 'results' && '📊 Results'}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Context Tab */}
          {activeTab === 'context' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Test Context</h3>
              <p className="text-sm text-gray-600">
                Configure the context that will be used for agent selection testing.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Topic</label>
                  <input
                    type="text"
                    value={context.topic}
                    onChange={(e) => setContext(prev => ({ ...prev, topic: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Content Type</label>
                  <select
                    value={context.contentType}
                    onChange={(e) => setContext(prev => ({ ...prev, contentType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="blog-post">Blog Post</option>
                    <option value="article">Article</option>
                    <option value="guide">Guide</option>
                    <option value="whitepaper">Whitepaper</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Target Audience</label>
                  <input
                    type="text"
                    value={context.targetAudience}
                    onChange={(e) => setContext(prev => ({ ...prev, targetAudience: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Primary Keyword</label>
                  <input
                    type="text"
                    value={context.primaryKeyword}
                    onChange={(e) => setContext(prev => ({ ...prev, primaryKeyword: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                  <input
                    type="text"
                    value={context.industry}
                    onChange={(e) => setContext(prev => ({ ...prev, industry: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Quality Score</label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={context.qualityScore}
                    onChange={(e) => setContext(prev => ({ ...prev, qualityScore: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                  <div className="text-sm text-gray-500 mt-1">{context.qualityScore}</div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Feedback</label>
                <textarea
                  value={context.feedback}
                  onChange={(e) => setContext(prev => ({ ...prev, feedback: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter feedback that might trigger specific agents..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Goals</label>
                <input
                  type="text"
                  value={context.goals.join(', ')}
                  onChange={(e) => setContext(prev => ({ 
                    ...prev, 
                    goals: e.target.value.split(',').map(g => g.trim()).filter(Boolean)
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  placeholder="educate, inspire action, generate leads"
                />
              </div>
            </div>
          )}

          {/* Configuration Tab */}
          {activeTab === 'config' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Consultation Configuration</h3>
              <p className="text-sm text-gray-600">
                Configure how agent consultation should be triggered and managed.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Max Consultations</label>
                  <input
                    type="number"
                    value={consultationConfig.maxConsultations}
                    onChange={(e) => setConsultationConfig(prev => ({ 
                      ...prev, 
                      maxConsultations: parseInt(e.target.value) 
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Timeout (ms)</label>
                  <input
                    type="number"
                    value={consultationConfig.timeoutMs}
                    onChange={(e) => setConsultationConfig(prev => ({ 
                      ...prev, 
                      timeoutMs: parseInt(e.target.value) 
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Fallback Behavior</label>
                  <select
                    value={consultationConfig.fallbackBehavior}
                    onChange={(e) => setConsultationConfig(prev => ({ 
                      ...prev, 
                      fallbackBehavior: e.target.value as 'continue' | 'fail' 
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="continue">Continue</option>
                    <option value="fail">Fail</option>
                  </select>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-md font-medium text-gray-900">Consultation Triggers</h4>
                  <button
                    onClick={addTrigger}
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                  >
                    Add Trigger
                  </button>
                </div>

                <div className="space-y-3">
                  {consultationConfig.triggers.map((trigger, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h5 className="font-medium text-gray-900">Trigger {index + 1}</h5>
                        <button
                          onClick={() => removeTrigger(index)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Remove
                        </button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                          <select
                            value={trigger.type}
                            onChange={(e) => {
                              const newTriggers = [...consultationConfig.triggers];
                              newTriggers[index] = { ...trigger, type: e.target.value as any };
                              setConsultationConfig(prev => ({ ...prev, triggers: newTriggers }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="always">Always</option>
                            <option value="quality_threshold">Quality Threshold</option>
                            <option value="feedback_keywords">Feedback Keywords</option>
                            <option value="content_complexity">Content Complexity</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                          <select
                            value={trigger.priority}
                            onChange={(e) => {
                              const newTriggers = [...consultationConfig.triggers];
                              newTriggers[index] = { ...trigger, priority: e.target.value as any };
                              setConsultationConfig(prev => ({ ...prev, triggers: newTriggers }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Agents</label>
                          <div className="text-sm text-gray-600">
                            {trigger.agents.join(', ')}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Results Tab */}
          {activeTab === 'results' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Test Results</h3>

              {/* Selection Results */}
              {selectionResult && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Agent Selection Results</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Recommended Agents</h5>
                      <div className="space-y-2">
                        {selectionResult.recommendedAgents.map(agentId => (
                          <div key={agentId} className="flex items-center space-x-2">
                            <span>{getAgentIcon(agentId)}</span>
                            <span className="text-sm">{getAgentName(agentId)}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Trigger Analysis</h5>
                      <div className="text-sm">
                        <div className="mb-1">
                          Should Trigger: {selectionResult.shouldTriggerConsultation ? '✅ Yes' : '❌ No'}
                        </div>
                        {selectionResult.triggerReasons.length > 0 && (
                          <div>
                            <div className="font-medium">Reasons:</div>
                            <ul className="list-disc list-inside text-gray-600">
                              {selectionResult.triggerReasons.map((reason, index) => (
                                <li key={index}>{reason}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Selection Reasoning</h5>
                    <ul className="list-disc list-inside text-sm text-gray-600">
                      {selectionResult.selectionReasoning.map((reason, index) => (
                        <li key={index}>{reason}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Consultation Results */}
              {consultationResult && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Consultation Results</h4>
                  
                  <div className="mb-4">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{consultationResult.summary.totalConsultations}</div>
                        <div className="text-sm text-gray-600">Total Consultations</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">
                          {Math.round(consultationResult.summary.averageConfidence * 100)}%
                        </div>
                        <div className="text-sm text-gray-600">Avg Confidence</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-purple-600">{consultationResult.summary.consultedAgents.length}</div>
                        <div className="text-sm text-gray-600">Agents Used</div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {consultationResult.consultations.map((consultation, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span>{getAgentIcon(consultation.agentId)}</span>
                            <span className="font-medium">{getAgentName(consultation.agentId)}</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            {consultation.processingTime}ms | {Math.round(consultation.confidence * 100)}% confidence
                          </div>
                        </div>
                        <div className="text-sm text-gray-600">
                          {consultation.suggestions.length} suggestions provided
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {!selectionResult && !consultationResult && (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Test Results</h3>
                  <p className="text-gray-600 mb-4">
                    Run the tests to see agent selection and consultation results here.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <button
          onClick={testAgentSelection}
          disabled={isLoading}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {isLoading ? 'Testing...' : 'Test Agent Selection'}
        </button>

        <button
          onClick={testConsultation}
          disabled={isLoading || !selectionResult?.recommendedAgents.length}
          className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
        >
          {isLoading ? 'Testing...' : 'Test Consultation'}
        </button>
      </div>
    </div>
  );
}
