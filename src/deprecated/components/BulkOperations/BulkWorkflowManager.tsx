/**
 * Bulk Workflow Manager
 * UI for managing bulk operations with CSV import/export
 */

'use client';

import { useState, useEffect, useRef } from 'react';

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  sampleInputs: Record<string, any>;
}

interface BulkJob {
  id: string;
  templateId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalItems: number;
  processedItems: number;
  failedItems: number;
  progress: number;
  startedAt: string;
  completedAt?: string;
}

export default function BulkWorkflowManager() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [csvData, setCsvData] = useState('');
  const [userApiKey, setUserApiKey] = useState('');
  const [jobs, setJobs] = useState<BulkJob[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    loadTemplates();
    loadJobs();
    
    // Poll for job updates every 5 seconds
    const interval = setInterval(loadJobs, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/workflow/create');
      const result = await response.json();
      
      if (result.success) {
        setTemplates(result.data.templates);
      }
    } catch (err) {
      console.error('Failed to load templates:', err);
    }
  };

  const loadJobs = async () => {
    try {
      const response = await fetch('/api/workflow/bulk');
      const result = await response.json();
      
      if (result.success) {
        setJobs(result.data.jobs);
      }
    } catch (err) {
      console.error('Failed to load jobs:', err);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvData(content);
    };
    reader.readAsText(file);
  };

  const generateSampleCSV = () => {
    if (!selectedTemplate) return;

    const headers = Object.keys(selectedTemplate.sampleInputs);
    const sampleRow = Object.values(selectedTemplate.sampleInputs);
    
    const csv = [
      headers.join(','),
      sampleRow.map(value => `"${value}"`).join(','),
      // Add a few more sample rows
      sampleRow.map(value => `"${value} (example 2)"`).join(','),
      sampleRow.map(value => `"${value} (example 3)"`).join(',')
    ].join('\n');

    setCsvData(csv);
  };

  const downloadSampleCSV = () => {
    if (!selectedTemplate) return;

    generateSampleCSV();
    
    const headers = Object.keys(selectedTemplate.sampleInputs);
    const sampleRow = Object.values(selectedTemplate.sampleInputs);
    
    const csv = [
      headers.join(','),
      sampleRow.map(value => `"${value}"`).join(',')
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedTemplate.id}_sample.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const submitBulkJob = async () => {
    if (!selectedTemplate || !csvData.trim()) {
      setError('Please select a template and provide CSV data');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/workflow/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateId: selectedTemplate.id,
          csvData,
          userApiKey: userApiKey || undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        setCsvData('');
        loadJobs();
        setError('');
      } else {
        setError(result.error || 'Failed to create bulk job');
      }
    } catch (err) {
      setError('Failed to create bulk job');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const downloadResults = async (jobId: string) => {
    try {
      const response = await fetch(`/api/workflow/bulk?jobId=${jobId}&format=csv`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bulk_results_${jobId}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      } else {
        setError('Failed to download results');
      }
    } catch (err) {
      setError('Failed to download results');
      console.error(err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Bulk Workflow Operations</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Create Bulk Job */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Create Bulk Job</h2>
          
          {/* Template Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Template
            </label>
            <select
              value={selectedTemplate?.id || ''}
              onChange={(e) => {
                const template = templates.find(t => t.id === e.target.value);
                setSelectedTemplate(template || null);
                setCsvData('');
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Choose a template...</option>
              {templates.map(template => (
                <option key={template.id} value={template.id}>
                  {template.name} ({template.category})
                </option>
              ))}
            </select>
          </div>

          {selectedTemplate && (
            <>
              {/* Template Info */}
              <div className="bg-gray-50 p-3 rounded mb-4">
                <h4 className="font-medium mb-1">{selectedTemplate.name}</h4>
                <p className="text-sm text-gray-600 mb-2">{selectedTemplate.description}</p>
                <div className="flex gap-2">
                  <button
                    onClick={downloadSampleCSV}
                    className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                  >
                    Download Sample CSV
                  </button>
                  <button
                    onClick={generateSampleCSV}
                    className="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                  >
                    Generate Sample
                  </button>
                </div>
              </div>

              {/* API Key */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OpenAI API Key (Optional - BYOK)
                </label>
                <input
                  type="password"
                  value={userApiKey}
                  onChange={(e) => setUserApiKey(e.target.value)}
                  placeholder="sk-..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* CSV Upload */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Upload CSV File
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* CSV Data */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CSV Data
                </label>
                <textarea
                  value={csvData}
                  onChange={(e) => setCsvData(e.target.value)}
                  rows={8}
                  placeholder="Paste CSV data here or upload a file..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum 1000 items per batch
                </p>
              </div>

              {/* Submit Button */}
              <button
                onClick={submitBulkJob}
                disabled={loading || !csvData.trim()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating Bulk Job...' : 'Start Bulk Processing'}
              </button>
            </>
          )}
        </div>

        {/* Job Status */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Bulk Jobs</h2>
          
          {jobs.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <p>No bulk jobs yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {jobs.map(job => (
                <div key={job.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Job {job.id}</h4>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(job.status)}`}>
                      {job.status}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-3">
                    <p>Template: {job.templateId}</p>
                    <p>Items: {job.processedItems + job.failedItems}/{job.totalItems}</p>
                    {job.failedItems > 0 && (
                      <p className="text-red-600">Failed: {job.failedItems}</p>
                    )}
                    <p>Started: {new Date(job.startedAt).toLocaleString()}</p>
                    {job.completedAt && (
                      <p>Completed: {new Date(job.completedAt).toLocaleString()}</p>
                    )}
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Progress</span>
                      <span>{job.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${job.progress}%` }}
                      />
                    </div>
                  </div>

                  {/* Actions */}
                  {job.status === 'completed' && (
                    <button
                      onClick={() => downloadResults(job.id)}
                      className="text-sm bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
                    >
                      Download Results CSV
                    </button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
