'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Divider,
  TextField,
  Button,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Card,
  CardContent,
  Grid,
  useTheme
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import PsychologyIcon from '@mui/icons-material/Psychology';
import PersonIcon from '@mui/icons-material/Person';
import dynamic from 'next/dynamic';
import remarkGfm from 'remark-gfm';

// Dynamically import ReactMarkdown to avoid SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false });
import { DynamicCollaborationState, DynamicAgentMessage, DynamicMessageType } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';
import { dynamicCollaborationClient } from '../../lib/dynamic-collaboration-client';

interface AgentCommunicationPanelProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const AgentCommunicationPanel: React.FC<AgentCommunicationPanelProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [messages, setMessages] = useState<DynamicAgentMessage[]>([]);
  const [userMessage, setUserMessage] = useState('');
  const [filterAgent, setFilterAgent] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterMenuAnchor, setFilterMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<DynamicAgentMessage | null>(null);
  const [messageMenuAnchor, setMessageMenuAnchor] = useState<null | HTMLElement>(null);
  const [sendingMessage, setSendingMessage] = useState(false);

  // Agent colors and names
  const agentColors: Record<string, string> = {
    'market-research': theme.palette.primary.main,
    'seo-keyword': theme.palette.secondary.main,
    'content-strategy': theme.palette.info.main,
    'content-generation': theme.palette.success.main,
    'seo-optimization': theme.palette.warning.main,
    'system': theme.palette.grey[500],
    'user': theme.palette.error.main
  };

  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  // Message type names
  const messageTypeNames: Record<string, string> = {
    [DynamicMessageType.GOAL_UPDATE]: 'Goal Update',
    [DynamicMessageType.REQUEST_INFORMATION]: 'Information Request',
    [DynamicMessageType.PROVIDE_INFORMATION]: 'Information Response',
    [DynamicMessageType.ARTIFACT_UPDATE]: 'Artifact Update',
    [DynamicMessageType.FEEDBACK_REQUEST]: 'Feedback Request',
    [DynamicMessageType.FEEDBACK_RESPONSE]: 'Feedback Response',
    [DynamicMessageType.SYSTEM_NOTIFICATION]: 'System Notification',
    [DynamicMessageType.USER_MESSAGE]: 'User Message',
    [DynamicMessageType.AGENT_MESSAGE]: 'Agent Message'
  };

  // Process messages from state
  useEffect(() => {
    if (!state || !state.dynamicMessages) {
      setMessages([]);
      return;
    }

    // Convert messages object to array and sort by timestamp
    const messagesArray = Object.values(state.dynamicMessages);

    // Sort by timestamp (oldest first)
    messagesArray.sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    setMessages(messagesArray);
  }, [state]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Filter messages
  const filteredMessages = messages.filter(message => {
    // Apply agent filter
    const matchesAgent =
      filterAgent === 'all' ||
      message.from === filterAgent ||
      (typeof message.to === 'string' && message.to === filterAgent) ||
      (Array.isArray(message.to) && message.to.includes(filterAgent));

    // Apply type filter
    const matchesType = filterType === 'all' || message.type === filterType;

    return matchesAgent && matchesType;
  });

  // Get unique agents for filter
  const uniqueAgents = ['all', ...new Set([
    ...messages.map(m => m.from),
    ...messages.flatMap(m => Array.isArray(m.to) ? m.to : [m.to])
  ])].filter(Boolean);

  // Get unique message types for filter
  const uniqueTypes = ['all', ...new Set(messages.map(m => m.type))];

  // Format time
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle filter menu open
  const handleFilterMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setFilterMenuAnchor(event.currentTarget);
  };

  // Handle filter menu close
  const handleFilterMenuClose = () => {
    setFilterMenuAnchor(null);
  };

  // Handle message menu open
  const handleMessageMenuOpen = (event: React.MouseEvent<HTMLButtonElement>, message: DynamicAgentMessage) => {
    setSelectedMessage(message);
    setMessageMenuAnchor(event.currentTarget);
  };

  // Handle message menu close
  const handleMessageMenuClose = () => {
    setMessageMenuAnchor(null);
  };

  // Handle send message
  const handleSendMessage = async () => {
    if (!userMessage.trim() || !sessionId) return;

    setSendingMessage(true);

    try {
      await dynamicCollaborationClient.sendMessage(
        sessionId,
        userMessage,
        DynamicMessageType.USER_MESSAGE
      );

      // Clear input
      setUserMessage('');

      // Refresh data
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSendingMessage(false);
    }
  };

  // Render message content
  const renderMessageContent = (message: DynamicAgentMessage) => {
    // Handle different message types
    switch (message.type) {
      case DynamicMessageType.GOAL_UPDATE:
        return (
          <Box>
            <Typography variant="body1" fontWeight="medium">
              {message.content.action}: {message.content.description}
            </Typography>
            {message.content.criteria && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Success Criteria:
                </Typography>
                <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                  {message.content.criteria.map((criterion: string, index: number) => (
                    <li key={index}>
                      <Typography variant="body2">{criterion}</Typography>
                    </li>
                  ))}
                </ul>
              </Box>
            )}
          </Box>
        );

      case DynamicMessageType.REQUEST_INFORMATION:
        return (
          <Box>
            <Typography variant="body1" fontWeight="medium">
              Information Request:
            </Typography>
            <Typography variant="body1">
              {message.content.query}
            </Typography>
            {message.content.context && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Context:
                </Typography>
                <Typography variant="body2">
                  {typeof message.content.context === 'string'
                    ? message.content.context
                    : JSON.stringify(message.content.context, null, 2)}
                </Typography>
              </Box>
            )}
          </Box>
        );

      case DynamicMessageType.ARTIFACT_UPDATE:
        return (
          <Box>
            <Typography variant="body1" fontWeight="medium">
              Artifact {message.content.action}: {message.content.name}
            </Typography>
            {message.content.description && (
              <Typography variant="body2">
                {message.content.description}
              </Typography>
            )}
          </Box>
        );

      case DynamicMessageType.FEEDBACK_REQUEST:
      case DynamicMessageType.FEEDBACK_RESPONSE:
        return (
          <Box>
            <Typography variant="body1" fontWeight="medium">
              {message.type === DynamicMessageType.FEEDBACK_REQUEST
                ? 'Feedback Request:'
                : 'Feedback Response:'}
            </Typography>
            <Typography variant="body1">
              {message.content.feedback}
            </Typography>
            {message.content.rating && (
              <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Rating:
                </Typography>
                <Chip
                  label={`${message.content.rating}/10`}
                  size="small"
                  color={
                    message.content.rating >= 8 ? 'success' :
                    message.content.rating >= 6 ? 'primary' :
                    message.content.rating >= 4 ? 'warning' : 'error'
                  }
                />
              </Box>
            )}
          </Box>
        );

      default:
        // Handle generic message content
        if (typeof message.content === 'string') {
          return (
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {message.content}
            </ReactMarkdown>
          );
        } else {
          return (
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {JSON.stringify(message.content, null, 2)}
            </ReactMarkdown>
          );
        }
    }
  };

  // Render message reasoning
  const renderMessageReasoning = (message: DynamicAgentMessage) => {
    if (!message.reasoning) return null;

    return (
      <Card variant="outlined" sx={{ mt: 1, bgcolor: 'grey.50' }}>
        <CardContent sx={{ py: 1, '&:last-child': { pb: 1 } }}>
          {message.reasoning.thoughts && message.reasoning.thoughts.length > 0 && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Thoughts:
              </Typography>
              <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                {message.reasoning.thoughts.map((thought, index) => (
                  <li key={index}>
                    <Typography variant="body2">{thought}</Typography>
                  </li>
                ))}
              </ul>
            </Box>
          )}

          {message.reasoning.considerations && message.reasoning.considerations.length > 0 && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Considerations:
              </Typography>
              <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                {message.reasoning.considerations.map((consideration, index) => (
                  <li key={index}>
                    <Typography variant="body2">{consideration}</Typography>
                  </li>
                ))}
              </ul>
            </Box>
          )}

          {message.reasoning.decision && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Decision:
              </Typography>
              <Typography variant="body2">
                {message.reasoning.decision}
              </Typography>
            </Box>
          )}

          {message.reasoning.confidence !== undefined && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                Confidence:
              </Typography>
              <Box
                sx={{
                  width: 100,
                  height: 6,
                  bgcolor: 'grey.200',
                  borderRadius: 3,
                  mr: 1
                }}
              >
                <Box
                  sx={{
                    width: `${message.reasoning.confidence * 100}%`,
                    height: '100%',
                    bgcolor:
                      message.reasoning.confidence >= 0.8 ? 'success.main' :
                      message.reasoning.confidence >= 0.6 ? 'primary.main' :
                      message.reasoning.confidence >= 0.4 ? 'warning.main' : 'error.main',
                    borderRadius: 3
                  }}
                />
              </Box>
              <Typography variant="caption">
                {Math.round(message.reasoning.confidence * 100)}%
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Agent Communication</Typography>
        <Box>
          <Tooltip title="Filter Messages">
            <IconButton onClick={handleFilterMenuOpen} size="small">
              <FilterListIcon />
            </IconButton>
          </Tooltip>

          {onRefresh && (
            <Tooltip title="Refresh">
              <IconButton onClick={onRefresh} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          )}

          <Menu
            anchorEl={filterMenuAnchor}
            open={Boolean(filterMenuAnchor)}
            onClose={handleFilterMenuClose}
          >
            <Box sx={{ px: 2, py: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Filter by Agent
              </Typography>
              <FormControl fullWidth size="small" sx={{ mb: 2, minWidth: 200 }}>
                <InputLabel>Agent</InputLabel>
                <Select
                  value={filterAgent}
                  onChange={(e) => setFilterAgent(e.target.value)}
                  label="Agent"
                >
                  {uniqueAgents.map(agent => (
                    <MenuItem key={agent} value={agent}>
                      {agent === 'all' ? 'All Agents' : agentNames[agent] || agent}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Typography variant="subtitle2" gutterBottom>
                Filter by Type
              </Typography>
              <FormControl fullWidth size="small">
                <InputLabel>Message Type</InputLabel>
                <Select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  label="Message Type"
                >
                  {uniqueTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {type === 'all' ? 'All Types' : messageTypeNames[type] || type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Menu>
        </Box>
      </Box>

      <Box sx={{ flexGrow: 1, overflow: 'auto', mb: 2, bgcolor: 'grey.50', borderRadius: 1, p: 1 }}>
        {loading && filteredMessages.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress />
          </Box>
        ) : filteredMessages.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Typography variant="body1" color="text.secondary">
              No messages available
            </Typography>
          </Box>
        ) : (
          <List>
            {filteredMessages.map((message, index) => (
              <React.Fragment key={message.id}>
                {index > 0 && <Divider variant="inset" component="li" />}
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    bgcolor: message.from === 'user' ? 'rgba(0, 0, 0, 0.03)' : 'transparent',
                    borderRadius: 1
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        bgcolor: agentColors[message.from] || theme.palette.grey[500]
                      }}
                    >
                      {message.from === 'user' ? <PersonIcon /> : <PsychologyIcon />}
                    </Avatar>
                  </ListItemAvatar>

                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle1" component="span">
                            {agentNames[message.from] || message.from}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ ml: 1 }}
                          >
                            {formatTime(message.timestamp)}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Chip
                            label={messageTypeNames[message.type] || message.type}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 1 }}
                          />

                          <IconButton
                            size="small"
                            onClick={(e) => handleMessageMenuOpen(e, message)}
                          >
                            <MoreVertIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Box>
                          {renderMessageContent(message)}
                        </Box>

                        {message.reasoning && renderMessageReasoning(message)}

                        {/* Show recipient */}
                        <Box sx={{ display: 'flex', mt: 1 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                            To:
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {Array.isArray(message.to) ? (
                              message.to.map(recipient => (
                                <Chip
                                  key={recipient}
                                  label={agentNames[recipient] || recipient}
                                  size="small"
                                  variant="outlined"
                                  sx={{ height: 20 }}
                                />
                              ))
                            ) : (
                              <Chip
                                label={agentNames[message.to] || message.to}
                                size="small"
                                variant="outlined"
                                sx={{ height: 20 }}
                              />
                            )}
                          </Box>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              </React.Fragment>
            ))}
            <div ref={messagesEndRef} />
          </List>
        )}
      </Box>

      <Box sx={{ display: 'flex', gap: 1 }}>
        <TextField
          fullWidth
          placeholder="Send a message to the agents..."
          value={userMessage}
          onChange={(e) => setUserMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
          disabled={sendingMessage || !sessionId}
          multiline
          maxRows={3}
        />
        <Button
          variant="contained"
          endIcon={sendingMessage ? <CircularProgress size={20} /> : <SendIcon />}
          onClick={handleSendMessage}
          disabled={!userMessage.trim() || sendingMessage || !sessionId}
        >
          Send
        </Button>
      </Box>

      {/* Message Menu */}
      <Menu
        anchorEl={messageMenuAnchor}
        open={Boolean(messageMenuAnchor)}
        onClose={handleMessageMenuClose}
      >
        <MenuItem onClick={handleMessageMenuClose}>
          View Details
        </MenuItem>
        <MenuItem onClick={handleMessageMenuClose}>
          Copy Content
        </MenuItem>
        <MenuItem onClick={handleMessageMenuClose}>
          Reply to Message
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default AgentCommunicationPanel;
