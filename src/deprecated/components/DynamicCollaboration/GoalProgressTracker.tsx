'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  Chip,
  LinearProgress,
  Grid,
  Divider,
  Card,
  CardContent,
  CircularProgress,
  Tooltip,
  IconButton,
  <PERSON>lapse,
  List,
  ListItem,
  ListItemText,
  useTheme
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import InfoIcon from '@mui/icons-material/Info';
import { DynamicCollaborationState } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

interface Goal {
  id: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  type: string;
  criteria: string[];
  assignedTo: string[];
  dependencies: string[];
  startTime?: string;
  endTime?: string;
  artifacts: string[];
}

interface GoalProgressTrackerProps {
  sessionId: string;
  state: DynamicCollaborationState | null;
  loading?: boolean;
  onRefresh?: () => void;
}

const GoalProgressTracker: React.FC<GoalProgressTrackerProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const theme = useTheme();
  const [expandedGoal, setExpandedGoal] = useState<string | null>(null);
  const [goals, setGoals] = useState<Goal[]>([]);
  const [activeGoalId, setActiveGoalId] = useState<string | null>(null);

  // Agent display names
  const agentNames: Record<string, string> = {
    'market-research': 'Market Research',
    'seo-keyword': 'SEO Keyword',
    'content-strategy': 'Content Strategy',
    'content-generation': 'Content Writer',
    'seo-optimization': 'SEO Optimizer',
    'system': 'System',
    'user': 'User'
  };

  // Format time
  const formatTime = (timestamp?: string) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'primary';
      case 'blocked':
        return 'error';
      default:
        return 'default';
    }
  };

  // Process goals from state
  useEffect(() => {
    if (!state || !state.goals) {
      setGoals([]);
      return;
    }

    // Convert goals object to array and sort by dependencies
    const goalsArray = Object.entries(state.goals).map(([id, goal]) => ({
      id,
      description: goal.description,
      status: goal.status as 'pending' | 'in-progress' | 'completed' | 'blocked',
      progress: goal.progress || 0,
      type: goal.type,
      criteria: goal.criteria || [],
      assignedTo: goal.assignedTo || [],
      dependencies: goal.dependencies || [],
      startTime: goal.startTime,
      endTime: goal.endTime,
      artifacts: goal.artifacts || []
    }));

    // Sort goals by dependencies (goals with no dependencies first)
    const sortedGoals = [...goalsArray].sort((a, b) => {
      // Completed goals come after in-progress goals
      if (a.status === 'completed' && b.status !== 'completed') return 1;
      if (a.status !== 'completed' && b.status === 'completed') return -1;

      // In-progress goals come before pending goals
      if (a.status === 'in-progress' && b.status === 'pending') return -1;
      if (a.status === 'pending' && b.status === 'in-progress') return 1;

      // Sort by dependencies
      if (a.dependencies.length === 0 && b.dependencies.length > 0) return -1;
      if (a.dependencies.length > 0 && b.dependencies.length === 0) return 1;

      return 0;
    });

    setGoals(sortedGoals);

    // Set active goal
    if (state.activeGoals && state.activeGoals.length > 0) {
      setActiveGoalId(state.activeGoals[0]);
    } else {
      setActiveGoalId(null);
    }
  }, [state]);

  // Toggle goal expansion
  const toggleGoalExpansion = (goalId: string) => {
    setExpandedGoal(expandedGoal === goalId ? null : goalId);
  };

  // Render detailed goal view
  const renderDetailedGoalView = (goal: Goal) => {
    // Get completion information
    const isCompleted = goal.status === 'completed';
    const completionTime = goal.endTime ? new Date(goal.endTime).getTime() : 0;

    // Get artifacts that contributed to this goal
    const contributingArtifacts = goal.artifacts
      .map(artifactId => state?.artifacts?.[artifactId])
      .filter(Boolean)
      .sort((a, b) => {
        // Sort by timestamp (newest first)
        const aTime = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const bTime = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        return bTime - aTime;
      });

    // Get the artifact that completed the goal (the last one created before goal completion)
    const completingArtifact = isCompleted && completionTime > 0
      ? contributingArtifacts.find(artifact => {
          const artifactTime = artifact.timestamp ? new Date(artifact.timestamp).getTime() : 0;
          return artifactTime <= completionTime;
        })
      : null;

    return (
      <Box sx={{ mt: 1, mb: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle2">Progress:</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={goal.progress}
                  color={getStatusColor(goal.status) as 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'}
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {Math.round(goal.progress)}%
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2">Status:</Typography>
            <Chip
              label={goal.status.charAt(0).toUpperCase() + goal.status.slice(1)}
              size="small"
              color={getStatusColor(goal.status) as 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'}
              icon={isCompleted ? <CheckCircleIcon /> : undefined}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle2">Type:</Typography>
            <Typography variant="body2">{goal.type}</Typography>
          </Grid>

          {goal.startTime && (
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">Started:</Typography>
              <Typography variant="body2">{formatTime(goal.startTime)}</Typography>
            </Grid>
          )}

          {goal.endTime && (
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2">Completed:</Typography>
              <Typography variant="body2">{formatTime(goal.endTime)}</Typography>
            </Grid>
          )}

          <Grid item xs={12}>
            <Typography variant="subtitle2">Assigned Agents:</Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
              {goal.assignedTo.length > 0 ? (
                goal.assignedTo.map(agentId => (
                  <Chip
                    key={agentId}
                    label={agentNames[agentId] || agentId}
                    size="small"
                    sx={{ mb: 0.5 }}
                  />
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No agents assigned
                </Typography>
              )}
            </Box>
          </Grid>

          {goal.dependencies.length > 0 && (
            <Grid item xs={12}>
              <Typography variant="subtitle2">Dependencies:</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                {goal.dependencies.map(depId => {
                  const depGoal = goals.find(g => g.id === depId);
                  return (
                    <Chip
                      key={depId}
                      label={depGoal?.description || `Goal ${depId.substring(0, 6)}`}
                      size="small"
                      color={
                        depGoal?.status === 'completed' ? 'success' :
                        depGoal?.status === 'in-progress' ? 'primary' :
                        depGoal?.status === 'blocked' ? 'error' : 'default'
                      }
                      icon={depGoal?.status === 'completed' ? <CheckCircleIcon /> : undefined}
                      sx={{ mb: 0.5 }}
                    />
                  );
                })}
              </Box>
            </Grid>
          )}

          {goal.criteria.length > 0 && (
            <Grid item xs={12}>
              <Typography variant="subtitle2">Success Criteria:</Typography>
              <List dense>
                {goal.criteria.map((criterion, index) => (
                  <ListItem key={index} sx={{ py: 0 }}>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {isCompleted && <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />}
                          {criterion}
                        </Box>
                      }
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
          )}

          {contributingArtifacts.length > 0 && (
            <Grid item xs={12}>
              <Typography variant="subtitle2">
                {isCompleted ? 'Artifacts that completed this goal:' : 'Related Artifacts:'}
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 0.5 }}>
                {contributingArtifacts.map(artifact => (
                  <Chip
                    key={artifact.id}
                    label={artifact.name || `Artifact ${artifact.id.substring(0, 6)}`}
                    size="small"
                    color={artifact === completingArtifact ? 'success' : 'default'}
                    variant={artifact === completingArtifact ? 'filled' : 'outlined'}
                    icon={artifact === completingArtifact ? <CheckCircleIcon /> : undefined}
                    sx={{ mb: 0.5 }}
                  />
                ))}
              </Box>

              {completingArtifact && (
                <Box sx={{ mt: 1, p: 1, bgcolor: 'success.light', borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ color: 'white' }}>
                    Goal completed by "{completingArtifact.name}" artifact
                    {completingArtifact.timestamp && ` at ${formatTime(completingArtifact.timestamp)}`}
                  </Typography>
                </Box>
              )}
            </Grid>
          )}

          {isCompleted && (
            <Grid item xs={12}>
              <Box sx={{ mt: 1, p: 1, bgcolor: 'success.light', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold' }}>
                  Goal successfully completed
                  {goal.endTime && ` at ${formatTime(goal.endTime)}`}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // Calculate overall progress
  const calculateOverallProgress = (): number => {
    if (goals.length === 0) return 0;

    const totalProgress = goals.reduce((sum, goal) => {
      return sum + goal.progress;
    }, 0);

    return Math.round(totalProgress / goals.length);
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Goal Progress</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
            Overall: {calculateOverallProgress()}%
          </Typography>
          <LinearProgress
            variant="determinate"
            value={calculateOverallProgress()}
            sx={{ width: 100 }}
          />
        </Box>
      </Box>

      {loading && goals.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress />
        </Box>
      ) : goals.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <Typography variant="body1" color="text.secondary">
            No goals available
          </Typography>
        </Box>
      ) : (
        <Stepper activeStep={goals.findIndex(goal => goal.id === activeGoalId)} orientation="vertical">
          {goals.map((goal, index) => (
            <Step key={goal.id} completed={goal.status === 'completed'}>
              <StepLabel
                StepIconComponent={() =>
                  goal.status === 'completed' ? (
                    <CheckCircleIcon color="success" />
                  ) : goal.status === 'in-progress' ? (
                    <PlayArrowIcon color="primary" />
                  ) : goal.status === 'blocked' ? (
                    <RadioButtonUncheckedIcon color="error" />
                  ) : (
                    <RadioButtonUncheckedIcon color="disabled" />
                  )
                }
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="subtitle1">
                    {goal.description}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={() => toggleGoalExpansion(goal.id)}
                    sx={{ ml: 1 }}
                  >
                    {expandedGoal === goal.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Box>
              </StepLabel>
              <StepContent>
                <Collapse in={expandedGoal === goal.id}>
                  {renderDetailedGoalView(goal)}
                </Collapse>

                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Box sx={{ width: '100%', mr: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={goal.progress}
                      color={getStatusColor(goal.status) as 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'}
                    />
                  </Box>
                  <Chip
                    label={`${Math.round(goal.progress)}%`}
                    size="small"
                    color={getStatusColor(goal.status) as 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'}
                  />
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      )}
    </Paper>
  );
};

export default GoalProgressTracker;
