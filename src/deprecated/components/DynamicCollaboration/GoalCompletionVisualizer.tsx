'use client';

import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Grid,
  LinearProgress,
  useTheme
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import DescriptionIcon from '@mui/icons-material/Description';
import { DynamicWorkflowPhase } from '../../app/(payload)/api/agents/dynamic-collaboration-v2/state';

interface GoalCompletionVisualizerProps {
  goals: Record<string, any>;
  artifacts: Record<string, any>;
  completedGoals: string[];
}

const GoalCompletionVisualizer: React.FC<GoalCompletionVisualizerProps> = ({
  goals,
  artifacts,
  completedGoals
}) => {
  const theme = useTheme();

  // Format time
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get artifacts related to a goal
  const getGoalArtifacts = (goalId: string) => {
    const goal = goals[goalId];
    if (!goal || !goal.artifacts || !artifacts) return [];

    return goal.artifacts
      .map((artifactId: string) => artifacts[artifactId])
      .filter(Boolean);
  };

  // Calculate overall completion percentage
  const overallCompletion = useMemo(() => {
    if (!goals || Object.keys(goals).length === 0) return 0;
    
    const totalGoals = Object.keys(goals).length;
    const completed = completedGoals.length;
    
    return Math.round((completed / totalGoals) * 100);
  }, [goals, completedGoals]);

  // Group completed goals by type
  const goalsByType = useMemo(() => {
    const result: Record<string, string[]> = {};
    
    completedGoals.forEach(goalId => {
      const goal = goals[goalId];
      if (!goal) return;
      
      const type = goal.type || 'unknown';
      if (!result[type]) {
        result[type] = [];
      }
      
      result[type].push(goalId);
    });
    
    return result;
  }, [goals, completedGoals]);

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>
        Goal Completion Summary
      </Typography>
      
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2">
            Overall Completion: {overallCompletion}%
          </Typography>
          <Typography variant="body2">
            {completedGoals.length} of {Object.keys(goals).length} goals completed
          </Typography>
        </Box>
        <LinearProgress 
          variant="determinate" 
          value={overallCompletion} 
          color={
            overallCompletion >= 80 ? 'success' :
            overallCompletion >= 50 ? 'primary' :
            overallCompletion >= 20 ? 'warning' : 'error'
          }
          sx={{ height: 10, borderRadius: 5 }}
        />
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      {Object.entries(goalsByType).length > 0 ? (
        Object.entries(goalsByType).map(([type, goalIds]) => (
          <Box key={type} sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              {type.charAt(0).toUpperCase() + type.slice(1)} Goals
            </Typography>
            
            <List dense>
              {goalIds.map(goalId => {
                const goal = goals[goalId];
                if (!goal) return null;
                
                const goalArtifacts = getGoalArtifacts(goalId);
                
                return (
                  <ListItem 
                    key={goalId} 
                    sx={{ 
                      py: 1, 
                      px: 2, 
                      mb: 1, 
                      bgcolor: 'background.paper', 
                      borderRadius: 1,
                      border: `1px solid ${theme.palette.divider}`
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CheckCircleIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1">
                          {goal.description}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Grid container spacing={1}>
                            {goal.endTime && (
                              <Grid item xs={12} sm={6}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                                  <Typography variant="caption" color="text.secondary">
                                    Completed at {formatTime(goal.endTime)}
                                  </Typography>
                                </Box>
                              </Grid>
                            )}
                            
                            {goalArtifacts.length > 0 && (
                              <Grid item xs={12}>
                                <Box sx={{ display: 'flex', alignItems: 'flex-start', mt: 0.5 }}>
                                  <DescriptionIcon fontSize="small" sx={{ mr: 0.5, mt: 0.5, color: 'text.secondary' }} />
                                  <Box>
                                    <Typography variant="caption" color="text.secondary">
                                      Artifacts that contributed to completion:
                                    </Typography>
                                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                                      {goalArtifacts.map(artifact => (
                                        <Chip
                                          key={artifact.id}
                                          label={artifact.name || `Artifact ${artifact.id.substring(0, 6)}`}
                                          size="small"
                                          variant="outlined"
                                          sx={{ mb: 0.5 }}
                                        />
                                      ))}
                                    </Box>
                                  </Box>
                                </Box>
                              </Grid>
                            )}
                          </Grid>
                        </Box>
                      }
                    />
                  </ListItem>
                );
              })}
            </List>
          </Box>
        ))
      ) : (
        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
          No goals have been completed yet.
        </Typography>
      )}
    </Paper>
  );
};

export default GoalCompletionVisualizer;
