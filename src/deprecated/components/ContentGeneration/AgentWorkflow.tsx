'use client';

import React, { useEffect, useState } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Card, 
  CardContent, 
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  Paper
} from '@mui/material';
import { AgentProgressDisplay, ArtifactViewer } from '@/components/AgentProgress';
import { useCollaborativeAgents, CollaborativeBrief } from '@/hooks/useCollaborativeAgents';

interface AgentWorkflowProps {
  brief: CollaborativeBrief;
  onComplete: (contentData: any) => void;
  onCancel: () => void;
}

export const AgentWorkflow: React.FC<AgentWorkflowProps> = ({ 
  brief, 
  onComplete, 
  onCancel 
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Initialize the collaborative agents system
  const agents = useCollaborativeAgents({
    autoRefresh: true,
    refreshInterval: 3000
  });
  
  // Workflow steps
  const steps = [
    'Planning & Research',
    'Content Strategy',
    'Content Creation',
    'Review & Refinement'
  ];
  
  // Initialize agent collaboration when brief is provided
  useEffect(() => {
    const initializeAgents = async () => {
      if (!brief || agents.sessionId) return;
      
      try {
        setIsGenerating(true);
        await agents.initializeSession(brief);
        setActiveStep(0);
      } catch (err) {
        setError(`Failed to initialize agents: ${err.message}`);
      } finally {
        setIsGenerating(false);
      }
    };
    
    initializeAgents();
  }, [brief, agents.sessionId]);
  
  // Update the step based on current phase
  useEffect(() => {
    if (!agents.sessionState) return;
    
    switch (agents.currentPhase) {
      case 'planning':
      case 'research':
        setActiveStep(0);
        break;
      case 'creation':
        setActiveStep(2);
        break;
      case 'review':
      case 'refinement':
        setActiveStep(3);
        break;
      case 'finalization':
        setActiveStep(4);
        break;
      default:
        setActiveStep(1);
    }
  }, [agents.currentPhase, agents.sessionState]);
  
  // Start planning and research
  const startPlanningAndResearch = async () => {
    try {
      setIsGenerating(true);
      await agents.requestInitialContentPlan();
      setActiveStep(1);
    } catch (err) {
      setError(`Failed to start planning: ${err.message}`);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Start content creation
  const startContentCreation = async () => {
    try {
      setIsGenerating(true);
      await agents.requestContentGeneration();
      setActiveStep(2);
    } catch (err) {
      setError(`Failed to start content creation: ${err.message}`);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Request content review
  const requestContentReview = async () => {
    try {
      setIsGenerating(true);
      // Find the content draft artifact
      const contentArtifacts = Object.values(agents.artifacts)
        .filter((artifact: any) => artifact.type === 'content-draft');
      
      if (contentArtifacts.length === 0) {
        throw new Error('No content draft found to review');
      }
      
      const contentArtifact = contentArtifacts[0];
      
      // Request SEO review
      await agents.sendMessage({
        from: 'orchestrator',
        to: 'seo-keyword',
        type: 'ARTIFACT_DELIVERY',
        content: {
          artifactId: contentArtifact.id,
          artifactType: 'content-draft'
        }
      });
      
      // Request content strategy review
      await agents.sendMessage({
        from: 'orchestrator',
        to: 'content-strategy',
        type: 'ARTIFACT_DELIVERY',
        content: {
          artifactId: contentArtifact.id,
          artifactType: 'content-draft'
        }
      });
      
      setActiveStep(3);
    } catch (err) {
      setError(`Failed to request review: ${err.message}`);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Finalize content
  const finalizeContent = () => {
    // Find the content draft artifact
    const contentArtifacts = Object.values(agents.artifacts)
      .filter((artifact: any) => artifact.type === 'content-draft');
    
    if (contentArtifacts.length === 0) {
      setError('No content draft found to finalize');
      return;
    }
    
    // Get the latest version of the content
    const contentArtifact = contentArtifacts[0];
    const latestVersion = contentArtifact.iterations[contentArtifact.iterations.length - 1];
    
    // Pass the content back to the parent component
    onComplete(latestVersion.content);
  };
  
  // Get relevant artifacts for the current step
  const getStepArtifacts = () => {
    const allArtifacts = Object.values(agents.artifacts);
    
    switch (activeStep) {
      case 0: // Planning & Research
        return allArtifacts.filter((a: any) => 
          a.type === 'market-research' || 
          a.type === 'seo-keyword-research'
        );
      case 1: // Content Strategy
        return allArtifacts.filter((a: any) => 
          a.type === 'content-strategy' || 
          a.type === 'content-plan' || 
          a.type === 'content-outline'
        );
      case 2: // Content Creation
        return allArtifacts.filter((a: any) => 
          a.type === 'content-draft' || 
          a.type === 'content-outline'
        );
      case 3: // Review & Refinement
        return allArtifacts;
      default:
        return allArtifacts;
    }
  };
  
  if (!agents.sessionId) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="300px">
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      
      <Box sx={{ mb: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" gutterBottom>
              Agent Progress
            </Typography>
            <AgentProgressDisplay state={agents.sessionState} />
          </Box>
          
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
            <Button 
              variant="outlined" 
              color="secondary" 
              onClick={onCancel}
              disabled={isGenerating}
            >
              Cancel
            </Button>
            
            {activeStep === 0 && (
              <Button 
                variant="contained" 
                color="primary" 
                onClick={startPlanningAndResearch}
                disabled={isGenerating}
              >
                {isGenerating ? <CircularProgress size={24} /> : 'Start Planning & Research'}
              </Button>
            )}
            
            {activeStep === 1 && (
              <Button 
                variant="contained" 
                color="primary" 
                onClick={startContentCreation}
                disabled={isGenerating}
              >
                {isGenerating ? <CircularProgress size={24} /> : 'Generate Content'}
              </Button>
            )}
            
            {activeStep === 2 && (
              <Button 
                variant="contained" 
                color="primary" 
                onClick={requestContentReview}
                disabled={isGenerating}
              >
                {isGenerating ? <CircularProgress size={24} /> : 'Request Review'}
              </Button>
            )}
            
            {activeStep === 3 && (
              <Button 
                variant="contained" 
                color="primary" 
                onClick={finalizeContent}
                disabled={isGenerating}
              >
                {isGenerating ? <CircularProgress size={24} /> : 'Finalize Content'}
              </Button>
            )}
          </Box>
        </Paper>
      </Box>
      
      <Box>
        <Typography variant="h6" gutterBottom>
          Artifacts
        </Typography>
        <ArtifactViewer artifacts={getStepArtifacts().reduce((acc, artifact: any) => {
          acc[artifact.id] = artifact;
          return acc;
        }, {})} />
      </Box>
    </Box>
  );
};
