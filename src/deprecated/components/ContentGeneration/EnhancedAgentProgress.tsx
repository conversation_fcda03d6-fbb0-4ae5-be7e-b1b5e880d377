'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Tab, Button, CircularProgress } from '@mui/material'
import VisibilityIcon from '@mui/icons-material/Visibility'
import RefreshIcon from '@mui/icons-material/Refresh'
import AgentCollaborationFlow from '../EnhancedCollaboration/AgentCollaborationFlow'
import AgentReasoningVisualizer from '../EnhancedCollaboration/AgentReasoningVisualizer'
import AgentInteractionVisualizer from '../EnhancedCollaboration/AgentInteractionVisualizer'
import ArtifactGallery from '../EnhancedCollaboration/ArtifactGallery'

// Define the CollaborationArtifact interface to match what's expected by ArtifactGallery
interface CollaborationArtifact {
  id: string;
  name: string;
  type: string;
  createdBy: string;
  currentVersion: number;
  iterations: Array<{
    version: number;
    content: any;
    timestamp: string;
    reasoning?: {
      process: string;
      steps: string[];
      thoughts?: string[];
      considerations?: string[];
      decision: string;
      confidence?: number;
    };
    seoData?: {
      score: number;
      recommendations: any[];
    };
  }>;
}

interface EnhancedAgentProgressProps {
  sessionId: string
  phase: string
  progress: number
  message: string
  agentMessages: string[]
  showAgentDiscussion: boolean
  onToggleAgentDiscussion: () => void
}

/**
 * Enhanced agent progress component that integrates the new visualization components
 */
const EnhancedAgentProgress: React.FC<EnhancedAgentProgressProps> = ({
  sessionId,
  phase,
  progress,
  message,
  agentMessages,
  showAgentDiscussion,
  onToggleAgentDiscussion
}) => {
  const [tabValue, setTabValue] = useState(0)
  const [collaborationState, setCollaborationState] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedArtifactId, setSelectedArtifactId] = useState<string | null>(null)

  // Fetch collaboration state
  const fetchCollaborationState = async () => {
    if (!sessionId) return

    setLoading(true)
    try {
      const response = await fetch(`/api/agents/collaborative-iteration/state?sessionId=${sessionId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch collaboration state')
      }
      const data = await response.json()
      setCollaborationState(data)
      setError(null)
    } catch (err) {
      console.error('Error fetching collaboration state:', err)
      setError('Failed to fetch collaboration state')
    } finally {
      setLoading(false)
    }
  }

  // Send feedback to an agent
  const handleSendFeedback = async (artifactId: string, feedback: string) => {
    if (!sessionId) return

    setLoading(true)
    try {
      const response = await fetch('/api/agents/collaborative-iteration/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          artifactId,
          feedback
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to send feedback')
      }
      
      // Refresh state after sending feedback
      await fetchCollaborationState()
    } catch (err) {
      console.error('Error sending feedback:', err)
      setError('Failed to send feedback')
    } finally {
      setLoading(false)
    }
  }

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  // View artifact details
  const handleViewArtifact = (artifactId: string) => {
    setSelectedArtifactId(artifactId)
    // Switch to artifacts tab
    setTabValue(2)
  }

  // Effect to fetch collaboration state on mount and when sessionId changes
  useEffect(() => {
    if (sessionId) {
      fetchCollaborationState()
    }
  }, [sessionId])

  // Extract iterations for agent reasoning visualization if state is available
  const allIterations = collaborationState?.artifacts 
    ? Object.values(collaborationState.artifacts).flatMap((artifact: any) => 
        artifact.iterations.map((iteration: any) => ({
          ...iteration,
          artifactId: artifact.id,
          artifactType: artifact.type,
          artifactName: artifact.name,
          agent: artifact.createdBy
        }))
      )
    : []

  // Extract artifacts if state is available
  const artifacts = collaborationState?.artifacts 
    ? Object.values(collaborationState.artifacts) as any[] 
    : []

  return (
    <div className="agent-progress-container">
      <div className="progress-header">
        <div className="progress-info">
          <h3>Content Generation in Progress</h3>
          <div className="progress-bar-container">
            <div className="progress-bar" style={{ width: `${progress}%` }}></div>
          </div>
          <div className="progress-details">
            <span className="phase">{phase.charAt(0).toUpperCase() + phase.slice(1)} Phase</span>
            <span className="percentage">{progress}%</span>
          </div>
          <p className="progress-message">{message}</p>
        </div>
        
        <div className="actions">
          <Button 
            variant="outlined" 
            startIcon={<VisibilityIcon />}
            onClick={onToggleAgentDiscussion}
            sx={{ mb: 2 }}
          >
            {showAgentDiscussion ? 'Hide Agent Details' : 'Show Agent Details'}
          </Button>
          
          {showAgentDiscussion && (
            <Button 
              variant="outlined" 
              startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={fetchCollaborationState}
              disabled={loading}
              sx={{ ml: 2 }}
            >
              Refresh
            </Button>
          )}
        </div>
      </div>

      {showAgentDiscussion && (
        <div className="agent-discussion">
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
          
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="agent visualization tabs">
              <Tab label="Collaboration Flow" />
              <Tab label="Agent Reasoning" />
              <Tab label="Artifacts" />
              <Tab label="Agent Interactions" />
            </Tabs>
          </Box>
          
          {/* Collaboration Flow Tab */}
          <Box hidden={tabValue !== 0}>
            {collaborationState ? (
              <AgentCollaborationFlow 
                sessionId={sessionId}
                state={collaborationState}
                onRefresh={fetchCollaborationState}
                onViewArtifact={handleViewArtifact}
              />
            ) : (
              <div className="loading-container">
                {loading ? (
                  <CircularProgress />
                ) : (
                  <Typography>No collaboration data available</Typography>
                )}
              </div>
            )}
          </Box>
          
          {/* Agent Reasoning Tab */}
          <Box hidden={tabValue !== 1}>
            {allIterations.length > 0 ? (
              <AgentReasoningVisualizer iterations={allIterations} />
            ) : (
              <div className="loading-container">
                {loading ? (
                  <CircularProgress />
                ) : (
                  <Typography>No agent reasoning data available</Typography>
                )}
              </div>
            )}
          </Box>
          
          {/* Artifacts Tab */}
          <Box hidden={tabValue !== 2}>
            {artifacts.length > 0 ? (
              <ArtifactGallery 
                artifacts={artifacts} 
                onSendFeedback={handleSendFeedback} 
              />
            ) : (
              <div className="loading-container">
                {loading ? (
                  <CircularProgress />
                ) : (
                  <Typography>No artifacts available</Typography>
                )}
              </div>
            )}
          </Box>
          
          {/* Agent Interactions Tab */}
          <Box hidden={tabValue !== 3} sx={{ height: 500 }}>
            {collaborationState?.messages ? (
              <AgentInteractionVisualizer 
                messages={collaborationState.messages}
                artifacts={collaborationState.artifacts}
                loading={loading}
                onRefresh={fetchCollaborationState}
                onViewArtifact={handleViewArtifact}
              />
            ) : (
              <div className="loading-container">
                {loading ? (
                  <CircularProgress />
                ) : (
                  <Typography>No agent interaction data available</Typography>
                )}
              </div>
            )}
          </Box>
        </div>
      )}

      <style jsx>{`
        .agent-progress-container {
          background-color: #f5f7f9;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 30px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 20px;
        }
        
        .progress-info {
          flex: 1;
        }
        
        .progress-bar-container {
          height: 8px;
          background-color: #e0e0e0;
          border-radius: 4px;
          overflow: hidden;
          margin: 10px 0;
        }
        
        .progress-bar {
          height: 100%;
          background-color: #4caf50;
          transition: width 0.5s ease;
        }
        
        .progress-details {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }
        
        .progress-message {
          font-size: 14px;
          color: #333;
          margin: 0;
        }
        
        .agent-discussion {
          margin-top: 20px;
          padding-top: 20px;
          border-top: 1px solid #e0e0e0;
        }
        
        .error-message {
          background-color: #ffebee;
          color: #d32f2f;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 20px;
        }
        
        .loading-container {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 300px;
        }
      `}</style>
    </div>
  )
}

export default EnhancedAgentProgress
