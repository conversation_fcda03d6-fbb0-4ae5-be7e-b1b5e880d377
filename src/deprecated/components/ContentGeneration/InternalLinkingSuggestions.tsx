// src/components/ContentGeneration/InternalLinkingSuggestions.tsx
'use client'

import React, { useState } from 'react'

interface InternalLinkingSuggestionsProps {
  internalLinks: Array<{
    anchorText: string
    targetTitle: string
    targetURL: string
    context: string
    relevance: number
  }>
}

const InternalLinkingSuggestions: React.FC<InternalLinkingSuggestionsProps> = ({
  internalLinks
}) => {
  const [expandedLink, setExpandedLink] = useState<number | null>(null)
  
  // Toggle expanded state for a link
  const toggleExpand = (index: number) => {
    setExpandedLink(expandedLink === index ? null : index)
  }
  
  // Get relevance color based on score
  const getRelevanceColor = (relevance: number): string => {
    if (relevance >= 80) return '#4caf50' // Green
    if (relevance >= 60) return '#ff9800' // Orange
    return '#f44336' // Red
  }
  
  return (
    <div className="internal-linking-suggestions">
      <h3>Internal Linking Opportunities</h3>
      
      {internalLinks.length > 0 ? (
        <div className="links-list">
          {internalLinks.map((link, index) => (
            <div 
              key={index} 
              className={`link-item ${expandedLink === index ? 'expanded' : ''}`}
              onClick={() => toggleExpand(index)}
            >
              <div className="link-header">
                <div className="link-title">{link.targetTitle}</div>
                <div 
                  className="link-relevance" 
                  style={{ color: getRelevanceColor(link.relevance) }}
                >
                  {link.relevance}% Relevant
                </div>
                <div className="link-expand-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    style={{ transform: expandedLink === index ? 'rotate(180deg)' : 'rotate(0deg)' }}
                  >
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                </div>
              </div>
              
              {expandedLink === index && (
                <div className="link-details">
                  <div className="link-detail-item">
                    <div className="link-detail-label">Anchor Text</div>
                    <div className="link-detail-value">{link.anchorText}</div>
                  </div>
                  
                  <div className="link-detail-item">
                    <div className="link-detail-label">Target URL</div>
                    <div className="link-detail-value link-url">{link.targetURL}</div>
                  </div>
                  
                  <div className="link-detail-item">
                    <div className="link-detail-label">Context</div>
                    <div className="link-detail-value link-context">
                      {link.context.split(link.anchorText).map((part, i, arr) => (
                        <React.Fragment key={i}>
                          {part}
                          {i < arr.length - 1 && (
                            <span className="highlight-anchor">{link.anchorText}</span>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="no-links">
          No internal linking opportunities found.
        </div>
      )}
      
      <style jsx>{`
        .internal-linking-suggestions {
          background-color: white;
          border-radius: 8px;
          border: 1px solid var(--theme-elevation-100);
          padding: 1.5rem;
        }
        
        h3 {
          font-size: 1.2rem;
          font-weight: 600;
          margin-top: 0;
          margin-bottom: 1.5rem;
          color: var(--theme-elevation-800);
        }
        
        .links-list {
          max-height: 400px;
          overflow-y: auto;
        }
        
        .link-item {
          border: 1px solid var(--theme-elevation-100);
          border-radius: 4px;
          margin-bottom: 0.75rem;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .link-item:last-child {
          margin-bottom: 0;
        }
        
        .link-item:hover {
          border-color: var(--theme-elevation-200);
          background-color: var(--theme-elevation-50);
        }
        
        .link-item.expanded {
          border-color: var(--theme-elevation-200);
          background-color: var(--theme-elevation-50);
        }
        
        .link-header {
          display: flex;
          align-items: center;
          padding: 1rem;
        }
        
        .link-title {
          flex-grow: 1;
          font-weight: 600;
          color: var(--theme-elevation-800);
        }
        
        .link-relevance {
          margin-right: 1rem;
          font-size: 0.9rem;
          font-weight: 600;
        }
        
        .link-expand-icon {
          transition: transform 0.2s ease;
        }
        
        .link-details {
          padding: 0 1rem 1rem;
          border-top: 1px solid var(--theme-elevation-100);
        }
        
        .link-detail-item {
          margin-top: 1rem;
        }
        
        .link-detail-label {
          font-size: 0.8rem;
          color: var(--theme-elevation-500);
          margin-bottom: 0.25rem;
        }
        
        .link-detail-value {
          font-size: 0.9rem;
          color: var(--theme-elevation-800);
        }
        
        .link-url {
          color: var(--theme-primary-500);
          word-break: break-all;
        }
        
        .link-context {
          line-height: 1.5;
        }
        
        .highlight-anchor {
          background-color: rgba(255, 213, 79, 0.3);
          padding: 0 2px;
          border-radius: 2px;
          font-weight: 600;
        }
        
        .no-links {
          padding: 1rem;
          text-align: center;
          color: var(--theme-elevation-500);
          font-size: 0.9rem;
          background-color: var(--theme-elevation-50);
          border-radius: 4px;
        }
      `}</style>
    </div>
  )
}

export default InternalLinkingSuggestions