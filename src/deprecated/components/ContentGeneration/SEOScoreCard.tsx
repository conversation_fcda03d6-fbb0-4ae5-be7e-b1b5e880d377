// src/components/ContentGeneration/SEOScoreCard.tsx
'use client'

import React, { useState } from 'react'

interface SEOScoreCardProps {
  seoScore: {
    overall: number
    [key: string]: number
  }
  improvementSuggestions: Array<{
    area: string
    suggestion: string
    priority: 'low' | 'medium' | 'high'
  }>
}

const SEOScoreCard: React.FC<SEOScoreCardProps> = ({
  seoScore,
  improvementSuggestions
}) => {
  const [showAllSuggestions, setShowAllSuggestions] = useState(false)
  
  // Get score color based on value
  const getScoreColor = (score: number): string => {
    if (score >= 80) return '#4caf50' // Green
    if (score >= 60) return '#ff9800' // Orange
    return '#f44336' // Red
  }
  
  // Get priority label and color
  const getPriorityInfo = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high':
        return { label: 'High Priority', color: '#f44336' }
      case 'medium':
        return { label: 'Medium Priority', color: '#ff9800' }
      case 'low':
        return { label: 'Low Priority', color: '#4caf50' }
      default:
        return { label: 'Priority', color: '#757575' }
    }
  }
  
  // Filter suggestions to show only high priority ones when not showing all
  const filteredSuggestions = showAllSuggestions 
    ? improvementSuggestions 
    : improvementSuggestions.filter(suggestion => suggestion.priority === 'high')
  
  return (
    <div className="seo-score-card">
      <h3>SEO Score</h3>
      
      <div className="score-overview">
        <div className="overall-score">
          <div 
            className="score-circle" 
            style={{ 
              background: `conic-gradient(
                ${getScoreColor(seoScore.overall)} ${seoScore.overall}%, 
                #e0e0e0 ${seoScore.overall}%
              )` 
            }}
          >
            <div className="score-value">{seoScore.overall}</div>
          </div>
          <div className="score-label">Overall</div>
        </div>
        
        <div className="score-breakdown">
          {Object.entries(seoScore)
            .filter(([key]) => key !== 'overall')
            .map(([key, value]) => (
              <div key={key} className="score-item">
                <div className="score-item-label">
                  {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                </div>
                <div className="score-item-bar-container">
                  <div 
                    className="score-item-bar" 
                    style={{ 
                      width: `${value}%`,
                      backgroundColor: getScoreColor(value)
                    }}
                  ></div>
                </div>
                <div className="score-item-value">{value}</div>
              </div>
            ))}
        </div>
      </div>
      
      <div className="improvement-suggestions">
        <div className="suggestions-header">
          <h4>Improvement Suggestions</h4>
          <button 
            className="toggle-button"
            onClick={() => setShowAllSuggestions(!showAllSuggestions)}
          >
            {showAllSuggestions ? 'Show High Priority Only' : 'Show All Suggestions'}
          </button>
        </div>
        
        {filteredSuggestions.length > 0 ? (
          <div className="suggestions-list">
            {filteredSuggestions.map((suggestion, index) => {
              const { label, color } = getPriorityInfo(suggestion.priority)
              
              return (
                <div key={index} className="suggestion-item">
                  <div className="suggestion-priority" style={{ color }}>
                    {label}
                  </div>
                  <div className="suggestion-area">{suggestion.area}</div>
                  <div className="suggestion-text">{suggestion.suggestion}</div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="no-suggestions">
            No {showAllSuggestions ? '' : 'high priority '}suggestions available.
          </div>
        )}
      </div>
      
      <style jsx>{`
        .seo-score-card {
          background-color: white;
          border-radius: 8px;
          border: 1px solid var(--theme-elevation-100);
          padding: 1.5rem;
          margin-bottom: 1.5rem;
        }
        
        h3 {
          font-size: 1.2rem;
          font-weight: 600;
          margin-top: 0;
          margin-bottom: 1.5rem;
          color: var(--theme-elevation-800);
        }
        
        .score-overview {
          display: flex;
          margin-bottom: 1.5rem;
        }
        
        .overall-score {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 2rem;
        }
        
        .score-circle {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
        
        .score-circle::before {
          content: '';
          position: absolute;
          width: 70px;
          height: 70px;
          border-radius: 50%;
          background-color: white;
        }
        
        .score-value {
          position: relative;
          font-size: 1.8rem;
          font-weight: 700;
          color: var(--theme-elevation-800);
        }
        
        .score-label {
          margin-top: 0.5rem;
          font-size: 0.9rem;
          color: var(--theme-elevation-500);
        }
        
        .score-breakdown {
          flex-grow: 1;
        }
        
        .score-item {
          display: flex;
          align-items: center;
          margin-bottom: 0.75rem;
        }
        
        .score-item:last-child {
          margin-bottom: 0;
        }
        
        .score-item-label {
          width: 120px;
          font-size: 0.9rem;
          color: var(--theme-elevation-600);
        }
        
        .score-item-bar-container {
          flex-grow: 1;
          height: 8px;
          background-color: var(--theme-elevation-100);
          border-radius: 4px;
          overflow: hidden;
          margin: 0 1rem;
        }
        
        .score-item-bar {
          height: 100%;
          border-radius: 4px;
          transition: width 0.3s ease;
        }
        
        .score-item-value {
          width: 30px;
          font-size: 0.9rem;
          font-weight: 600;
          text-align: right;
          color: var(--theme-elevation-800);
        }
        
        .improvement-suggestions {
          border-top: 1px solid var(--theme-elevation-100);
          padding-top: 1.5rem;
        }
        
        .suggestions-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }
        
        .suggestions-header h4 {
          font-size: 1rem;
          font-weight: 600;
          margin: 0;
          color: var(--theme-elevation-800);
        }
        
        .toggle-button {
          background: none;
          border: none;
          color: var(--theme-primary-500);
          font-size: 0.8rem;
          cursor: pointer;
          text-decoration: underline;
        }
        
        .suggestions-list {
          max-height: 300px;
          overflow-y: auto;
        }
        
        .suggestion-item {
          padding: 1rem;
          margin-bottom: 0.75rem;
          background-color: var(--theme-elevation-50);
          border-radius: 4px;
        }
        
        .suggestion-item:last-child {
          margin-bottom: 0;
        }
        
        .suggestion-priority {
          font-size: 0.8rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
        
        .suggestion-area {
          font-size: 0.9rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          color: var(--theme-elevation-800);
        }
        
        .suggestion-text {
          font-size: 0.9rem;
          line-height: 1.4;
          color: var(--theme-elevation-700);
        }
        
        .no-suggestions {
          padding: 1rem;
          text-align: center;
          color: var(--theme-elevation-500);
          font-size: 0.9rem;
          background-color: var(--theme-elevation-50);
          border-radius: 4px;
        }
      `}</style>
    </div>
  )
}

export default SEOScoreCard