// src/components/ContentGeneration/ContentPreview.tsx
// Old Code
// 'use client'

// import React, { useState } from 'react'
// import { ContentGenerationResult } from './Dashboard'
// import SEOScoreCard from './SEOScoreCard'
// import InternalLinkingSuggestions from './InternalLinkingSuggestions'

// interface ContentPreviewProps {
//   content: ContentGenerationResult
//   onPublish: (content: ContentGenerationResult, status: 'draft' | 'published') => void
//   onRegenerate: (feedback: string) => void
//   onBack: () => void
//   isLoading: boolean
// }

// const ContentPreview: React.FC<ContentPreviewProps> = ({
//   content,
//   onPublish,
//   onRegenerate,
//   onBack,
//   isLoading
// }) => {
//   // State for editing content
//   const [editedContent, setEditedContent] = useState<ContentGenerationResult>(content)
//   const [isEditing, setIsEditing] = useState(false)
//   const [regenerateFeedback, setRegenerateFeedback] = useState('')
//   const [showRegenerateForm, setShowRegenerateForm] = useState(false)
  
//   // Handle section content change
//   const handleSectionChange = (sectionId: string, newContent: any) => {
//     setEditedContent(prev => ({
//       ...prev,
//       sections: prev.sections.map(section => 
//         section.id === sectionId 
//           ? { ...section, content: newContent } 
//           : section
//       )
//     }))
//   }
  
//   // Handle title change
//   const handleTitleChange = (newTitle: string) => {
//     setEditedContent(prev => ({
//       ...prev,
//       title: newTitle
//     }))
//   }
  
//   // Handle meta description change
//   const handleMetaDescriptionChange = (newDescription: string) => {
//     setEditedContent(prev => ({
//       ...prev,
//       metaDescription: newDescription
//     }))
//   }
  
//   // Toggle edit mode
//   const toggleEditMode = () => {
//     setIsEditing(!isEditing)
//   }
  
//   // Handle regenerate form submission
//   const handleRegenerateSubmit = (e: React.FormEvent) => {
//     e.preventDefault()
//     if (regenerateFeedback.trim()) {
//       onRegenerate(regenerateFeedback)
//       setShowRegenerateForm(false)
//     }
//   }
  
//   // Render content section based on type
//   const renderSection = (section: any) => {
//     switch (section.type) {
//       case 'heading':
//         return isEditing ? (
//           <input
//             type="text"
//             value={section.content as string}
//             onChange={(e) => handleSectionChange(section.id, e.target.value)}
//             className="section-heading-input"
//           />
//         ) : (
//           <h3>{section.content}</h3>
//         )
        
//       case 'text':
//         return isEditing ? (
//           <textarea
//             value={section.content as string}
//             onChange={(e) => handleSectionChange(section.id, e.target.value)}
//             rows={5}
//             className="section-text-input"
//           />
//         ) : (
//           <div className="section-text">{section.content}</div>
//         )
        
//       case 'list':
//         return isEditing ? (
//           <textarea
//             value={(section.content as string[]).join('\n')}
//             onChange={(e) => handleSectionChange(section.id, e.target.value.split('\n'))}
//             rows={section.content.length + 1}
//             className="section-list-input"
//           />
//         ) : (
//           <ul className="section-list">
//             {(section.content as string[]).map((item, index) => (
//               <li key={index}>{item}</li>
//             ))}
//           </ul>
//         )
        
//       case 'faq':
//         return (
//           <div className="section-faq">
//             {(section.content as Array<{question: string, answer: string}>).map((faq, index) => (
//               <div key={index} className="faq-item">
//                 <h4 className="faq-question">
//                   {isEditing ? (
//                     <input
//                       type="text"
//                       value={faq.question}
//                       onChange={(e) => {
//                         const newContent = [...section.content]
//                         newContent[index].question = e.target.value
//                         handleSectionChange(section.id, newContent)
//                       }}
//                       className="faq-question-input"
//                     />
//                   ) : (
//                     faq.question
//                   )}
//                 </h4>
//                 <div className="faq-answer">
//                   {isEditing ? (
//                     <textarea
//                       value={faq.answer}
//                       onChange={(e) => {
//                         const newContent = [...section.content]
//                         newContent[index].answer = e.target.value
//                         handleSectionChange(section.id, newContent)
//                       }}
//                       rows={3}
//                       className="faq-answer-input"
//                     />
//                   ) : (
//                     faq.answer
//                   )}
//                 </div>
//               </div>
//             ))}
//           </div>
//         )
        
//       default:
//         return <div>Unsupported section type: {section.type}</div>
//     }
//   }
  
//   return (
//     <div className="content-preview">
//       <div className="preview-header">
//         <button className="back-button" onClick={onBack} disabled={isLoading}>
//           <svg
//             xmlns="http://www.w3.org/2000/svg"
//             width="16"
//             height="16"
//             viewBox="0 0 24 24"
//             fill="none"
//             stroke="currentColor"
//             strokeWidth="2"
//             strokeLinecap="round"
//             strokeLinejoin="round"
//           >
//             <line x1="19" y1="12" x2="5" y2="12"></line>
//             <polyline points="12 19 5 12 12 5"></polyline>
//           </svg>
//           Back
//         </button>
//         <h2>Content Preview</h2>
//         <div className="preview-actions">
//           <button 
//             className="edit-button" 
//             onClick={toggleEditMode}
//             disabled={isLoading}
//           >
//             {isEditing ? 'Done Editing' : 'Edit Content'}
//           </button>
//         </div>
//       </div>
      
//       <div className="content-container">
//         <div className="content-meta">
//           <div className="meta-item">
//             <div className="meta-label">Title</div>
//             {isEditing ? (
//               <input
//                 type="text"
//                 value={editedContent.title}
//                 onChange={(e) => handleTitleChange(e.target.value)}
//                 className="meta-title-input"
//               />
//             ) : (
//               <div className="meta-title">{editedContent.title}</div>
//             )}
//           </div>
          
//           <div className="meta-item">
//             <div className="meta-label">Meta Description</div>
//             {isEditing ? (
//               <textarea
//                 value={editedContent.metaDescription}
//                 onChange={(e) => handleMetaDescriptionChange(e.target.value)}
//                 rows={2}
//                 className="meta-description-input"
//               />
//             ) : (
//               <div className="meta-description">{editedContent.metaDescription}</div>
//             )}
//           </div>
//         </div>
        
//         <div className="content-body">
//           {editedContent.sections.map((section) => (
//             <div key={section.id} className={`content-section content-section-${section.type}`}>
//               <div className="section-title">
//                 {isEditing ? (
//                   <input
//                     type="text"
//                     value={section.title}
//                     onChange={(e) => {
//                       setEditedContent(prev => ({
//                         ...prev,
//                         sections: prev.sections.map(s => 
//                           s.id === section.id 
//                             ? { ...s, title: e.target.value } 
//                             : s
//                         )
//                       }))
//                     }}
//                     className="section-title-input"
//                   />
//                 ) : (
//                   <h3>{section.title}</h3>
//                 )}
//               </div>
//               <div className="section-content">
//                 {renderSection(section)}
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
      
//       <div className="preview-extras">
//         <SEOScoreCard 
//           seoScore={editedContent.seoScore} 
//           improvementSuggestions={editedContent.improvementSuggestions}
//         />
        
//         <InternalLinkingSuggestions 
//           internalLinks={editedContent.internalLinks}
//         />
//       </div>
      
//       <div className="preview-actions-footer">
//         <div className="left-actions">
//           <button 
//             className="regenerate-button" 
//             onClick={() => setShowRegenerateForm(!showRegenerateForm)}
//             disabled={isLoading}
//           >
//             Retry Draft
//           </button>
//         </div>
        
//         <div className="right-actions">
//           <button 
//             className="publish-draft-button" 
//             onClick={() => onPublish(editedContent, 'draft')}
//             disabled={isLoading}
//           >
//             Save as Draft
//           </button>
//           <button 
//             className="publish-button" 
//             onClick={() => onPublish(editedContent, 'published')}
//             disabled={isLoading}
//           >
//             {isLoading ? 'Publishing...' : 'Publish Content'}
//           </button>
//         </div>
//       </div>
      
//       {showRegenerateForm && (
//         <div className="regenerate-form">
//           <h3>Provide Feedback for Regeneration</h3>
//           <form onSubmit={handleRegenerateSubmit}>
//             <textarea
//               value={regenerateFeedback}
//               onChange={(e) => setRegenerateFeedback(e.target.value)}
//               placeholder="What would you like to improve in this draft? Be specific about what aspects need to be changed."
//               rows={4}
//               required
//             />
//             <div className="regenerate-form-actions">
//               <button 
//                 type="button" 
//                 className="cancel-button"
//                 onClick={() => setShowRegenerateForm(false)}
//               >
//                 Cancel
//               </button>
//               <button 
//                 type="submit" 
//                 className="submit-button"
//                 disabled={!regenerateFeedback.trim() || isLoading}
//               >
//                 {isLoading ? 'Regenerating...' : 'Regenerate Content'}
//               </button>
//             </div>
//           </form>
//         </div>
//       )}
//           <style jsx>{`
//         .content-preview {
//           width: 100%;
//           margin-bottom: 2rem;
//         }
        
//         .preview-header {
//           display: flex;
//           align-items: center;
//           margin-bottom: 1.5rem;
//         }
        
//         .back-button {
//           display: flex;
//           align-items: center;
//           background: none;
//           border: none;
//           color: var(--theme-elevation-500);
//           padding: 0.5rem;
//           margin-right: 1rem;
//           cursor: pointer;
//           font-size: 0.9rem;
//         }
        
//         .back-button svg {
//           margin-right: 0.25rem;
//         }
        
//         .back-button:hover {
//           color: var(--theme-elevation-800);
//         }
        
//         .back-button:disabled {
//           opacity: 0.5;
//           cursor: not-allowed;
//         }
        
//         h2 {
//           font-size: 1.5rem;
//           font-weight: 600;
//           margin: 0;
//           flex-grow: 1;
//         }
        
//         .preview-actions {
//           display: flex;
//           gap: 0.5rem;
//         }
        
//         .edit-button {
//           padding: 0.5rem 1rem;
//           background-color: var(--theme-elevation-100);
//           border: 1px solid var(--theme-elevation-200);
//           border-radius: 4px;
//           cursor: pointer;
//           font-size: 0.9rem;
//         }
        
//         .edit-button:hover {
//           background-color: var(--theme-elevation-200);
//         }
        
//         .content-container {
//           background-color: white;
//           border: 1px solid var(--theme-elevation-100);
//           border-radius: 6px;
//           padding: 1.5rem;
//           margin-bottom: 1.5rem;
//           box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
//         }
        
//         .content-meta {
//           margin-bottom: 2rem;
//           padding-bottom: 1rem;
//           border-bottom: 1px solid var(--theme-elevation-100);
//         }
        
//         .meta-item {
//           margin-bottom: 1rem;
//         }
        
//         .meta-label {
//           font-size: 0.9rem;
//           font-weight: 600;
//           color: var(--theme-elevation-600);
//           margin-bottom: 0.25rem;
//         }
        
//         .meta-title {
//           font-size: 1.4rem;
//           font-weight: 600;
//           color: var(--theme-elevation-900);
//         }
        
//         .meta-description {
//           font-size: 1rem;
//           color: var(--theme-elevation-700);
//           line-height: 1.5;
//         }
        
//         .meta-title-input, .meta-description-input {
//           width: 100%;
//           padding: 0.5rem;
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           font-size: 1rem;
//         }
        
//         .meta-title-input {
//           font-size: 1.2rem;
//           font-weight: 600;
//         }
        
//         .content-body {
//           display: flex;
//           flex-direction: column;
//           gap: 1.5rem;
//         }
        
//         .content-section {
//           margin-bottom: 1.5rem;
//         }
        
//         .section-title h3 {
//           font-size: 1.2rem;
//           font-weight: 600;
//           margin-top: 0;
//           margin-bottom: 0.75rem;
//           color: var(--theme-elevation-800);
//         }
        
//         .section-title-input {
//           width: 100%;
//           padding: 0.5rem;
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           font-size: 1.1rem;
//           font-weight: 600;
//           margin-bottom: 0.75rem;
//         }
        
//         .section-content {
//           font-size: 1rem;
//           line-height: 1.6;
//           color: var(--theme-elevation-700);
//         }
        
//         .section-heading-input, .section-text-input, .section-list-input, 
//         .faq-question-input, .faq-answer-input {
//           width: 100%;
//           padding: 0.5rem;
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           font-size: 1rem;
//           margin-bottom: 0.5rem;
//         }
        
//         .section-text {
//           white-space: pre-line;
//         }
        
//         .section-list {
//           padding-left: 1.5rem;
//         }
        
//         .section-list li {
//           margin-bottom: 0.5rem;
//         }
        
//         .section-faq {
//           display: flex;
//           flex-direction: column;
//           gap: 1rem;
//         }
        
//         .faq-item {
//           border-bottom: 1px solid var(--theme-elevation-100);
//           padding-bottom: 1rem;
//         }
        
//         .faq-item:last-child {
//           border-bottom: none;
//         }
        
//         .faq-question {
//           font-weight: 600;
//           margin-bottom: 0.5rem;
//           color: var(--theme-elevation-800);
//         }
        
//         .faq-answer {
//           color: var(--theme-elevation-700);
//         }
        
//         .preview-extras {
//           display: grid;
//           grid-template-columns: 1fr 1fr;
//           gap: 1.5rem;
//           margin-bottom: 2rem;
//         }
        
//         @media (max-width: 768px) {
//           .preview-extras {
//             grid-template-columns: 1fr;
//           }
//         }
        
//         .preview-actions-footer {
//           display: flex;
//           justify-content: space-between;
//           margin-top: 2rem;
//           padding-top: 1rem;
//           border-top: 1px solid var(--theme-elevation-100);
//         }
        
//         .left-actions, .right-actions {
//           display: flex;
//           gap: 0.75rem;
//         }
        
//         .regenerate-button {
//           padding: 0.6rem 1rem;
//           background-color: white;
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           cursor: pointer;
//           font-size: 0.9rem;
//           color: var(--theme-elevation-800);
//         }
        
//         .regenerate-button:hover {
//           background-color: var(--theme-elevation-100);
//         }
        
//         .publish-draft-button {
//           padding: 0.6rem 1rem;
//           background-color: var(--theme-elevation-100);
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           cursor: pointer;
//           font-size: 0.9rem;
//         }
        
//         .publish-draft-button:hover {
//           background-color: var(--theme-elevation-200);
//         }
        
//         .publish-button {
//           padding: 0.6rem 1.5rem;
//           background-color: var(--theme-success-500, #10b981);
//           color: white;
//           border: none;
//           border-radius: 4px;
//           cursor: pointer;
//           font-size: 0.9rem;
//           font-weight: 500;
//         }
        
//         .publish-button:hover {
//           background-color: var(--theme-success-600, #059669);
//         }
        
//         button:disabled {
//           opacity: 0.6;
//           cursor: not-allowed;
//         }
        
//         .regenerate-form {
//           position: fixed;
//           top: 0;
//           left: 0;
//           right: 0;
//           bottom: 0;
//           background-color: rgba(0, 0, 0, 0.5);
//           display: flex;
//           align-items: center;
//           justify-content: center;
//           z-index: 1000;
//         }
        
//         .regenerate-form > form {
//           background-color: white;
//           padding: 2rem;
//           border-radius: 6px;
//           width: 90%;
//           max-width: 600px;
//           box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
//         }
        
//         .regenerate-form h3 {
//           margin-top: 0;
//           margin-bottom: 1rem;
//           font-size: 1.3rem;
//         }
        
//         .regenerate-form textarea {
//           width: 100%;
//           padding: 0.75rem;
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           margin-bottom: 1.5rem;
//           font-size: 1rem;
//         }
        
//         .regenerate-form-actions {
//           display: flex;
//           justify-content: flex-end;
//           gap: 1rem;
//         }
        
//         .cancel-button {
//           padding: 0.6rem 1rem;
//           background-color: white;
//           border: 1px solid var(--theme-elevation-300);
//           border-radius: 4px;
//           cursor: pointer;
//           font-size: 0.9rem;
//         }
        
//         .cancel-button:hover {
//           background-color: var(--theme-elevation-100);
//         }
        
//         .submit-button {
//           padding: 0.6rem 1.5rem;
//           background-color: var(--theme-primary-500, #3b82f6);
//           color: white;
//           border: none;
//           border-radius: 4px;
//           cursor: pointer;
//           font-size: 0.9rem;
//           font-weight: 500;
//         }
        
//         .submit-button:hover {
//           background-color: var(--theme-primary-600, #2563eb);
//         }
//       `}</style>
//     </div>
//   )
// }

// export default ContentPreview
// src/components/ContentGeneration/ContentPreview.tsx
'use client'

import React, { useState, useEffect } from 'react'
import { ContentGenerationResult } from './Dashboard'
import SEOScoreCard from './SEOScoreCard'
import InternalLinkingSuggestions from './InternalLinkingSuggestions'

interface ContentPreviewProps {
  content: ContentGenerationResult
  onPublish: (content: ContentGenerationResult, status: 'draft' | 'published') => void
  onRegenerate: (feedback: string) => void
  onBack: () => void
  isLoading: boolean
}

// Helper function to extract content from LangChain message format if needed
function extractContent(content: any): any {
  // If it's null or undefined, return as is
  if (content === null || content === undefined) {
    return content;
  }
  
  // If it's a string, check if it's a LangChain message string
  if (typeof content === 'string') {
    try {
      // Check if it looks like a JSON string
      if (content.trim().startsWith('{') && content.includes('kwargs')) {
        const parsed = JSON.parse(content);
        // Check for LangChain message structure
        if (parsed.lc === 1 && 
            parsed.type === 'constructor' && 
            parsed.kwargs && 
            parsed.kwargs.content) {
          return parsed.kwargs.content;
        }
      }
      return content;
    } catch (e) {
      // If parsing fails, return the original string
      return content;
    }
  }
  
  // If it's an object, check if it's a LangChain message object
  if (typeof content === 'object' && content !== null) {
    // Check for LangChain message structure
    if (content.lc === 1 && 
        content.type === 'constructor' && 
        content.kwargs && 
        content.kwargs.content) {
      return content.kwargs.content;
    }
    
    // If it's an array, process each item
    if (Array.isArray(content)) {
      return content.map(item => extractContent(item));
    }
    
    // If it's an object, process each property
    const processed: any = {};
    for (const key in content) {
      processed[key] = extractContent(content[key]);
    }
    return processed;
  }
  
  // Default case - return unchanged
  return content;
}

const ContentPreview: React.FC<ContentPreviewProps> = ({
  content,
  onPublish,
  onRegenerate,
  onBack,
  isLoading
}) => {
  // Extract and process content to handle LangChain message formats
  const processedContent = extractContent(content);
  
  // State for editing content
  const [editedContent, setEditedContent] = useState<ContentGenerationResult>(processedContent);
  const [isEditing, setIsEditing] = useState(false);
  const [regenerateFeedback, setRegenerateFeedback] = useState('');
  const [showRegenerateForm, setShowRegenerateForm] = useState(false);
  const [activeTab, setActiveTab] = useState<'content' | 'discussion'>('content');
  const [discussionTab, setDiscussionTab] = useState<'planning' | 'discussion' | 'execution' | 'review'>('planning');
  
  // Update edited content when the source content changes
  useEffect(() => {
    setEditedContent(extractContent(content));
  }, [content]);
  
  // Handle section content change
  const handleSectionChange = (sectionId: string, newContent: any) => {
    setEditedContent(prev => ({
      ...prev,
      sections: prev.sections.map(section => 
        section.id === sectionId 
          ? { ...section, content: newContent } 
          : section
      )
    }));
  };
  
  // Handle title change
  const handleTitleChange = (newTitle: string) => {
    setEditedContent(prev => ({
      ...prev,
      title: newTitle
    }));
  };
  
  // Handle meta description change
  const handleMetaDescriptionChange = (newDescription: string) => {
    setEditedContent(prev => ({
      ...prev,
      metaDescription: newDescription
    }));
  };
  
  // Toggle edit mode
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };
  
  // Handle regenerate form submission
  const handleRegenerateSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (regenerateFeedback.trim()) {
      onRegenerate(regenerateFeedback);
      setShowRegenerateForm(false);
    }
  };
  
  // Add new section
  const handleAddSection = () => {
    const newId = `section-${Date.now()}`;
    
    setEditedContent(prev => ({
      ...prev,
      sections: [
        ...prev.sections,
        {
          id: newId,
          type: 'heading',
          title: 'New Section',
          content: 'Enter content here...'
        }
      ]
    }));
  };
  
  // Delete section
  const handleDeleteSection = (sectionId: string) => {
    if (window.confirm('Are you sure you want to delete this section?')) {
      setEditedContent(prev => ({
        ...prev,
        sections: prev.sections.filter(section => section.id !== sectionId)
      }));
    }
  };
  
  // Move section up
  const handleMoveSectionUp = (sectionId: string) => {
    setEditedContent(prev => {
      const sections = [...prev.sections];
      const index = sections.findIndex(section => section.id === sectionId);
      if (index > 0) {
        const temp = sections[index];
        sections[index] = sections[index - 1];
        sections[index - 1] = temp;
      }
      return {
        ...prev,
        sections
      };
    });
  };
  
  // Move section down
  const handleMoveSectionDown = (sectionId: string) => {
    setEditedContent(prev => {
      const sections = [...prev.sections];
      const index = sections.findIndex(section => section.id === sectionId);
      if (index < sections.length - 1) {
        const temp = sections[index];
        sections[index] = sections[index + 1];
        sections[index + 1] = temp;
      }
      return {
        ...prev,
        sections
      };
    });
  };
  
  // Change section type
  const handleChangeSectionType = (sectionId: string, newType: string) => {
    setEditedContent(prev => {
      const sections = [...prev.sections];
      const index = sections.findIndex(section => section.id === sectionId);
      
      if (index !== -1) {
        let updatedContent;
        const currentSection = sections[index];
        
        // Convert content based on type change
        if (newType === 'list' && typeof currentSection.content === 'string') {
          // Convert text to list
          updatedContent = currentSection.content.split('\n').filter(item => item.trim() !== '');
        } else if (newType === 'text' && Array.isArray(currentSection.content)) {
          // Convert list to text
          updatedContent = (currentSection.content as string[]).join('\n');
        } else if (newType === 'faq') {
          // Initialize FAQ structure
          updatedContent = typeof currentSection.content === 'string' 
            ? [{ question: 'New Question', answer: currentSection.content }]
            : [{ question: 'New Question', answer: 'New Answer' }];
        } else {
          // Keep existing content or initialize appropriate default
          updatedContent = currentSection.content;
        }
        
        sections[index] = {
          ...currentSection,
          type: newType,
          content: updatedContent
        };
      }
      
      return {
        ...prev,
        sections
      };
    });
  };
  
  // Add FAQ item to a section
  const handleAddFaqItem = (sectionId: string) => {
    setEditedContent(prev => {
      const sections = [...prev.sections];
      const index = sections.findIndex(section => section.id === sectionId);
      
      if (index !== -1 && sections[index].type === 'faq') {
        const currentFaqs = Array.isArray(sections[index].content) 
          ? [...sections[index].content] 
          : [];
          
        sections[index] = {
          ...sections[index],
          content: [...currentFaqs, { question: 'New Question', answer: 'New Answer' }]
        };
      }
      
      return {
        ...prev,
        sections
      };
    });
  };
  
  // Delete FAQ item
  const handleDeleteFaqItem = (sectionId: string, faqIndex: number) => {
    setEditedContent(prev => {
      const sections = [...prev.sections];
      const index = sections.findIndex(section => section.id === sectionId);
      
      if (index !== -1 && sections[index].type === 'faq' && Array.isArray(sections[index].content)) {
        const currentFaqs = [...sections[index].content];
        currentFaqs.splice(faqIndex, 1);
        
        sections[index] = {
          ...sections[index],
          content: currentFaqs
        };
      }
      
      return {
        ...prev,
        sections
      };
    });
  };
  
  // Render content section based on type
  const renderSection = (section: any, index: number) => {
    // Ensure content is properly processed to handle LangChain message format
    const processedSectionContent = extractContent(section.content);
    
    switch (section.type) {
      case 'heading':
        return (
          <div className="section-content-wrapper">
            {isEditing && (
              <div className="section-edit-controls">
                <select
                  value={section.type}
                  onChange={(e) => handleChangeSectionType(section.id, e.target.value)}
                  className="section-type-selector"
                >
                  <option value="heading">Heading & Text</option>
                  <option value="text">Text Only</option>
                  <option value="list">List</option>
                  <option value="faq">FAQ</option>
                </select>
                
                <div className="section-buttons">
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionUp(section.id)}
                    className="section-button"
                    disabled={index === 0}
                    title="Move Up"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionDown(section.id)}
                    className="section-button"
                    disabled={index === editedContent.sections.length - 1}
                    title="Move Down"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleDeleteSection(section.id)}
                    className="section-button section-delete-button"
                    title="Delete Section"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            {isEditing ? (
              <textarea
                value={typeof processedSectionContent === 'string' ? processedSectionContent : ''}
                onChange={(e) => handleSectionChange(section.id, e.target.value)}
                rows={Math.max(5, (processedSectionContent?.toString().split('\n').length || 0) + 1)}
                className="section-text-input"
                placeholder="Enter content here..."
              />
            ) : (
              <div className="section-text">
                {typeof processedSectionContent === 'string' 
                  ? processedSectionContent
                  : JSON.stringify(processedSectionContent)
                }
              </div>
            )}
          </div>
        );
        
      case 'text':
        return (
          <div className="section-content-wrapper">
            {isEditing && (
              <div className="section-edit-controls">
                <select
                  value={section.type}
                  onChange={(e) => handleChangeSectionType(section.id, e.target.value)}
                  className="section-type-selector"
                >
                  <option value="heading">Heading & Text</option>
                  <option value="text">Text Only</option>
                  <option value="list">List</option>
                  <option value="faq">FAQ</option>
                </select>
                
                <div className="section-buttons">
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionUp(section.id)}
                    className="section-button"
                    disabled={index === 0}
                    title="Move Up"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionDown(section.id)}
                    className="section-button"
                    disabled={index === editedContent.sections.length - 1}
                    title="Move Down"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleDeleteSection(section.id)}
                    className="section-button section-delete-button"
                    title="Delete Section"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            {isEditing ? (
              <textarea
                value={typeof processedSectionContent === 'string' ? processedSectionContent : ''}
                onChange={(e) => handleSectionChange(section.id, e.target.value)}
                rows={Math.max(5, (processedSectionContent?.toString().split('\n').length || 0) + 1)}
                className="section-text-input"
                placeholder="Enter content here..."
              />
            ) : (
              <div className="section-text">
                {typeof processedSectionContent === 'string' 
                  ? processedSectionContent
                  : JSON.stringify(processedSectionContent)
                }
              </div>
            )}
          </div>
        );
        
      case 'list':
        const listItems = Array.isArray(processedSectionContent) 
          ? processedSectionContent 
          : typeof processedSectionContent === 'string' 
            ? [processedSectionContent] 
            : [];
            
        return (
          <div className="section-content-wrapper">
            {isEditing && (
              <div className="section-edit-controls">
                <select
                  value={section.type}
                  onChange={(e) => handleChangeSectionType(section.id, e.target.value)}
                  className="section-type-selector"
                >
                  <option value="heading">Heading & Text</option>
                  <option value="text">Text Only</option>
                  <option value="list">List</option>
                  <option value="faq">FAQ</option>
                </select>
                
                <div className="section-buttons">
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionUp(section.id)}
                    className="section-button"
                    disabled={index === 0}
                    title="Move Up"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionDown(section.id)}
                    className="section-button"
                    disabled={index === editedContent.sections.length - 1}
                    title="Move Down"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleDeleteSection(section.id)}
                    className="section-button section-delete-button"
                    title="Delete Section"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            {isEditing ? (
              <div className="list-editor">
                <p className="list-instruction">Enter one item per line:</p>
                <textarea
                  value={listItems.join('\n')}
                  onChange={(e) => handleSectionChange(section.id, e.target.value.split('\n').filter(item => item.trim() !== ''))}
                  rows={Math.max(5, listItems.length + 2)}
                  className="section-list-input"
                  placeholder="Enter list items, one per line"
                />
              </div>
            ) : (
              <ul className="section-list">
                {listItems.map((item, i) => (
                  <li key={i}>{extractContent(item)}</li>
                ))}
              </ul>
            )}
          </div>
        );
        
      case 'faq':
        const faqItems = Array.isArray(processedSectionContent) 
          ? processedSectionContent.map(item => ({
              question: extractContent(item.question) || '',
              answer: extractContent(item.answer) || ''
            }))
          : [];
          
        return (
          <div className="section-content-wrapper">
            {isEditing && (
              <div className="section-edit-controls">
                <select
                  value={section.type}
                  onChange={(e) => handleChangeSectionType(section.id, e.target.value)}
                  className="section-type-selector"
                >
                  <option value="heading">Heading & Text</option>
                  <option value="text">Text Only</option>
                  <option value="list">List</option>
                  <option value="faq">FAQ</option>
                </select>
                
                <div className="section-buttons">
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionUp(section.id)}
                    className="section-button"
                    disabled={index === 0}
                    title="Move Up"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleMoveSectionDown(section.id)}
                    className="section-button"
                    disabled={index === editedContent.sections.length - 1}
                    title="Move Down"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>
                  
                  <button 
                    type="button"
                    onClick={() => handleDeleteSection(section.id)}
                    className="section-button section-delete-button"
                    title="Delete Section"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            <div className="section-faq">
              {faqItems.map((faq, faqIndex) => (
                <div key={faqIndex} className="faq-item">
                  <div className="faq-header">
                    <h4 className="faq-question">
                      {isEditing ? (
                        <input
                          type="text"
                          value={faq.question}
                          onChange={(e) => {
                            const newContent = [...faqItems];
                            newContent[faqIndex].question = e.target.value;
                            handleSectionChange(section.id, newContent);
                          }}
                          className="faq-question-input"
                          placeholder="Enter question here"
                        />
                      ) : (
                        faq.question
                      )}
                    </h4>
                    
                    {isEditing && (
                      <button 
                        type="button" 
                        className="faq-delete-button"
                        onClick={() => handleDeleteFaqItem(section.id, faqIndex)}
                        title="Delete Question"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </button>
                    )}
                  </div>
                  
                  <div className="faq-answer">
                    {isEditing ? (
                      <textarea
                        value={faq.answer}
                        onChange={(e) => {
                          const newContent = [...faqItems];
                          newContent[faqIndex].answer = e.target.value;
                          handleSectionChange(section.id, newContent);
                        }}
                        rows={3}
                        className="faq-answer-input"
                        placeholder="Enter answer here"
                      />
                    ) : (
                      faq.answer
                    )}
                  </div>
                </div>
              ))}
              
              {isEditing && (
                <button 
                  type="button" 
                  className="add-faq-button"
                  onClick={() => handleAddFaqItem(section.id)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Add Question
                </button>
              )}
            </div>
          </div>
        );
        
      default:
        return <div>Unsupported section type: {section.type}</div>;
    }
  };
  
  // Render the agent discussion content
  const renderAgentDiscussion = () => {
    if (!editedContent.agentDiscussion) {
      return <div className="no-discussion">No agent discussion data available.</div>;
    }
    
    const discussionData = editedContent.agentDiscussion[discussionTab] || [];
    
    return (
      <div className="agent-discussion-content">
        <div className="discussion-tabs">
          <button 
            className={`discussion-tab ${discussionTab === 'planning' ? 'active' : ''}`}
            onClick={() => setDiscussionTab('planning')}
          >
            Planning
          </button>
          <button 
            className={`discussion-tab ${discussionTab === 'discussion' ? 'active' : ''}`}
            onClick={() => setDiscussionTab('discussion')}
          >
            Discussion
          </button>
          <button 
            className={`discussion-tab ${discussionTab === 'execution' ? 'active' : ''}`}
            onClick={() => setDiscussionTab('execution')}
          >
            Execution
          </button>
          <button 
            className={`discussion-tab ${discussionTab === 'review' ? 'active' : ''}`}
            onClick={() => setDiscussionTab('review')}
          >
            Review
          </button>
        </div>
        
        <div className="discussion-messages">
          {Array.isArray(discussionData) && discussionData.length > 0 ? (
            discussionData.map((message, index) => (
              <div key={index} className="discussion-message">
                {extractContent(message) || 'No content available'}
              </div>
            ))
          ) : (
            <div className="no-discussion">No discussion data available for this phase.</div>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className="content-preview">
      <div className="preview-header">
        <button className="back-button" onClick={onBack} disabled={isLoading}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Back
        </button>
        <h2>Content Preview</h2>
        <div className="preview-actions">
          <button 
            className="edit-button" 
            onClick={toggleEditMode}
            disabled={isLoading}
          >
            {isEditing ? 'Done Editing' : 'Edit Content'}
          </button>
        </div>
      </div>
      
      <div className="preview-tabs">
        <button 
          className={`preview-tab ${activeTab === 'content' ? 'active' : ''}`}
          onClick={() => setActiveTab('content')}
        >
          Content
        </button>
        <button 
          className={`preview-tab ${activeTab === 'discussion' ? 'active' : ''}`}
          onClick={() => setActiveTab('discussion')}
        >
          Agent Discussion
        </button>
      </div>
      
      {activeTab === 'content' ? (
        <div className="content-container">
          <div className="content-meta">
            <div className="meta-item">
              <div className="meta-label">Title</div>
              {isEditing ? (
                <input
                  type="text"
                  value={extractContent(editedContent.title) || ''}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  className="meta-title-input"
                  placeholder="Enter title"
                />
              ) : (
                <div className="meta-title">{extractContent(editedContent.title) || ''}</div>
              )}
            </div>
            
            <div className="meta-item">
              <div className="meta-label">Meta Description</div>
              {isEditing ? (
                <textarea
                  value={extractContent(editedContent.metaDescription) || ''}
                  onChange={(e) => handleMetaDescriptionChange(e.target.value)}
                  rows={2}
                  className="meta-description-input"
                  placeholder="Enter meta description"
                />
              ) : (
                <div className="meta-description">{extractContent(editedContent.metaDescription) || ''}</div>
              )}
            </div>
          </div>
          
          <div className="content-body">
            {editedContent.sections.map((section, index) => (
              <div key={section.id} className={`content-section content-section-${section.type}`}>
                <div className="section-title">
                  {isEditing ? (
                    <input
                      type="text"
                      value={extractContent(section.title) || ''}
                      onChange={(e) => {
                        setEditedContent(prev => ({
                          ...prev,
                          sections: prev.sections.map(s => 
                            s.id === section.id 
                              ? { ...s, title: e.target.value } 
                              : s
                          )
                        }));
                      }}
                      className="section-title-input"
                      placeholder="Enter section title"
                    />
                  ) : (
                    <h3>{extractContent(section.title) || ''}</h3>
                  )}
                </div>
                <div className="section-content">
                  {renderSection(section, index)}
                </div>
              </div>
            ))}
            
            {isEditing && (
              <div className="add-section-container">
                <button 
                  type="button" 
                  className="add-section-button"
                  onClick={handleAddSection}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Add New Section
                </button>
              </div>
            )}
          </div>
          
          <div className="preview-extras">
            <SEOScoreCard 
              seoScore={extractContent(editedContent.seoScore) || { overall: 0 }} 
              improvementSuggestions={extractContent(editedContent.improvementSuggestions) || []}
            />
            
            <InternalLinkingSuggestions 
              internalLinks={extractContent(editedContent.internalLinks) || []}
            />
          </div>
        </div>
      ) : (
        <div className="discussion-container">
          {renderAgentDiscussion()}
        </div>
      )}
      
      <div className="preview-actions-footer">
        <div className="left-actions">
          <button 
            className="regenerate-button" 
            onClick={() => setShowRegenerateForm(!showRegenerateForm)}
            disabled={isLoading}
          >
            Retry Draft
          </button>
        </div>
        
        <div className="right-actions">
          <button 
            className="publish-draft-button" 
            onClick={() => onPublish(editedContent, 'draft')}
            disabled={isLoading}
          >
            Save as Draft
          </button>
          <button 
            className="publish-button" 
            onClick={() => onPublish(editedContent, 'published')}
            disabled={isLoading}
          >
            {isLoading ? 'Publishing...' : 'Publish Content'}
          </button>
        </div>
      </div>
      
      {showRegenerateForm && (
        <div className="regenerate-form">
          <h3>Provide Feedback for Regeneration</h3>
          <form onSubmit={handleRegenerateSubmit}>
            <textarea
              value={regenerateFeedback}
              onChange={(e) => setRegenerateFeedback(e.target.value)}
              placeholder="What would you like to improve in this draft? Be specific about what aspects need to be changed."
              rows={4}
              required
            />
            <div className="regenerate-form-actions">
              <button 
                type="button" 
                className="cancel-button"
                onClick={() => setShowRegenerateForm(false)}
              >
                Cancel
              </button>
              <button 
                type="submit" 
                className="submit-button"
                disabled={!regenerateFeedback.trim() || isLoading}
              >
                {isLoading ? 'Regenerating...' : 'Regenerate Content'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      <style jsx>{`
        .content-preview {
          width: 100%;
          margin-bottom: 2rem;
        }
        
        .preview-header {
          display: flex;
          align-items: center;
          margin-bottom: 1rem;
        }
        
        .back-button {
          display: flex;
          align-items: center;
          background: none;
          border: none;
          color: var(--theme-elevation-500);
          padding: 0.5rem;
          margin-right: 1rem;
          cursor: pointer;
          font-size: 0.9rem;
        }
        
        .back-button svg {
          margin-right: 0.25rem;
        }
        
        .back-button:hover {
          color: var(--theme-elevation-800);
        }
        
        .back-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin: 0;
          flex-grow: 1;
        }
        
        .preview-actions {
          display: flex;
          gap: 0.5rem;
        }
        
        .edit-button {
          padding: 0.5rem 1rem;
          background-color: var(--theme-elevation-100);
          border: 1px solid var(--theme-elevation-200);
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
        }
        
        .edit-button:hover {
          background-color: var(--theme-elevation-200);
        }
        
        .preview-tabs {
          display: flex;
          border-bottom: 1px solid var(--theme-elevation-200);
          margin-bottom: 1rem;
        }
        
        .preview-tab {
          padding: 0.75rem 1.5rem;
          background: none;
          border: none;
          border-bottom: 2px solid transparent;
          cursor: pointer;
          font-size: 1rem;
          color: var(--theme-elevation-600);
        }
        
        .preview-tab:hover {
          color: var(--theme-primary-500);
        }
        
        .preview-tab.active {
          border-bottom: 2px solid var(--theme-primary-500);
          color: var(--theme-primary-500);
          font-weight: 500;
        }
        
        .content-container {
          background-color: white;
          border: 1px solid var(--theme-elevation-100);
          border-radius: 6px;
          padding: 1.5rem;
          margin-bottom: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .discussion-container {
          background-color: white;
          border: 1px solid var(--theme-elevation-100);
          border-radius: 6px;
          padding: 1.5rem;
          margin-bottom: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .content-meta {
          margin-bottom: 2rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid var(--theme-elevation-100);
        }
        
        .meta-item {
          margin-bottom: 1rem;
        }
        
        .meta-label {
          font-size: 0.9rem;
          font-weight: 600;
          color: var(--theme-elevation-600);
          margin-bottom: 0.25rem;
        }
        
        .meta-title {
          font-size: 1.4rem;
          font-weight: 600;
          color: var(--theme-elevation-900);
        }
        
        .meta-description {
          font-size: 1rem;
          color: var(--theme-elevation-700);
          line-height: 1.5;
        }
        
        .meta-title-input, .meta-description-input {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          font-size: 1rem;
        }
        
        .meta-title-input {
          font-size: 1.2rem;
          font-weight: 600;
        }
        
        .content-body {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }
        
        .content-section {
          margin-bottom: 1.5rem;
          border: 1px solid var(--theme-elevation-100);
          border-radius: 6px;
          padding: 1rem;
          background-color: var(--theme-elevation-50);
        }
        
        .section-title h3 {
          font-size: 1.2rem;
          font-weight: 600;
          margin-top: 0;
          margin-bottom: 0.75rem;
          color: var(--theme-elevation-800);
        }
        
        .section-title-input {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 0.75rem;
        }
        
        .section-content {
          font-size: 1rem;
          line-height: 1.6;
          color: var(--theme-elevation-700);
        }
        
        .section-content-wrapper {
          position: relative;
        }
        
        .section-edit-controls {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.75rem;
          align-items: center;
        }
        
        .section-type-selector {
          padding: 0.3rem 0.5rem;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          font-size: 0.85rem;
        }
        
        .section-buttons {
          display: flex;
          gap: 0.25rem;
        }
        
        .section-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          background-color: var(--theme-elevation-100);
          border: 1px solid var(--theme-elevation-200);
          border-radius: 4px;
          cursor: pointer;
        }
        
        .section-button:hover {
          background-color: var(--theme-elevation-200);
        }
        
        .section-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
        
        .section-delete-button {
          color: #cc0000;
        }
        
        .section-delete-button:hover {
          background-color: #ffeeee;
        }
        
        .section-heading-input, .section-text-input, .section-list-input, 
        .faq-question-input, .faq-answer-input {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          font-size: 1rem;
          margin-bottom: 0.5rem;
        }
        
        .section-text {
          white-space: pre-line;
          padding: 0.5rem;
          background-color: white;
          border-radius: 4px;
          min-height: 50px;
        }
        
        .list-instruction {
          font-size: 0.85rem;
          margin-bottom: 0.5rem;
          color: var(--theme-elevation-600);
        }
        
        .section-list {
          padding-left: 1.5rem;
          margin: 0.5rem 0;
        }
        
        .section-list li {
          margin-bottom: 0.5rem;
        }
        
        .section-faq {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }
        
        .faq-item {
          border-bottom: 1px solid var(--theme-elevation-100);
          padding-bottom: 1rem;
          background-color: white;
          border-radius: 4px;
          padding: 1rem;
        }
        
        .faq-item:last-child {
          border-bottom: none;
        }
        
        .faq-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .faq-question {
          font-weight: 600;
          margin-bottom: 0.5rem;
          color: var(--theme-elevation-800);
          margin-right: 0.5rem;
          flex-grow: 1;
        }
        
        .faq-delete-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 22px;
          height: 22px;
          background-color: var(--theme-elevation-100);
          border: 1px solid var(--theme-elevation-200);
          border-radius: 50%;
          cursor: pointer;
          color: #cc0000;
        }
        
        .faq-delete-button:hover {
          background-color: #ffeeee;
        }
        
        .faq-answer {
          color: var(--theme-elevation-700);
          padding-top: 0.5rem;
        }
        
        .add-faq-button, .add-section-button {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          background-color: var(--theme-elevation-100);
          border: 1px dashed var(--theme-elevation-300);
          border-radius: 4px;
          padding: 0.75rem 1rem;
          width: 100%;
          cursor: pointer;
          color: var(--theme-elevation-700);
          font-size: 0.9rem;
          margin-top: 0.5rem;
        }
        
        .add-faq-button:hover, .add-section-button:hover {
          background-color: var(--theme-elevation-200);
          color: var(--theme-elevation-900);
        }
        
        .add-section-container {
          margin-top: 1rem;
          padding-top: 1rem;
          border-top: 1px dashed var(--theme-elevation-200);
        }
        
        .preview-extras {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.5rem;
          margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
          .preview-extras {
            grid-template-columns: 1fr;
          }
        }
        
        .discussion-tabs {
          display: flex;
          border-bottom: 1px solid var(--theme-elevation-100);
          margin-bottom: 1rem;
          overflow-x: auto;
        }
        
        .discussion-tab {
          padding: 0.75rem 1rem;
          background: none;
          border: none;
          border-bottom: 2px solid transparent;
          cursor: pointer;
          font-size: 0.9rem;
          color: var(--theme-elevation-600);
          white-space: nowrap;
        }
        
        .discussion-tab:hover {
          color: var(--theme-primary-500);
        }
        
        .discussion-tab.active {
          border-bottom: 2px solid var(--theme-primary-500);
          color: var(--theme-primary-500);
          font-weight: 500;
        }
        
        .discussion-messages {
          max-height: 600px;
          overflow-y: auto;
          padding: 1rem;
        }
        
        .discussion-message {
          padding: 1rem;
          margin-bottom: 1rem;
          background-color: var(--theme-elevation-50);
          border-radius: 6px;
          border-left: 3px solid var(--theme-primary-300);
          font-size: 0.95rem;
          line-height: 1.5;
          white-space: pre-line;
        }
        
        .no-discussion {
          text-align: center;
          padding: 2rem;
          color: var(--theme-elevation-500);
          font-size: 0.95rem;
          background-color: var(--theme-elevation-50);
          border-radius: 6px;
        }
        
        .preview-actions-footer {
          display: flex;
          justify-content: space-between;
          margin-top: 2rem;
          padding-top: 1rem;
          border-top: 1px solid var(--theme-elevation-100);
        }
        
        .left-actions, .right-actions {
          display: flex;
          gap: 0.75rem;
        }
        
        .regenerate-button {
          padding: 0.6rem 1rem;
          background-color: white;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
          color: var(--theme-elevation-800);
        }
        
        .regenerate-button:hover {
          background-color: var(--theme-elevation-100);
        }
        
        .publish-draft-button {
          padding: 0.6rem 1rem;
          background-color: var(--theme-elevation-100);
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
        }
        
        .publish-draft-button:hover {
          background-color: var(--theme-elevation-200);
        }
        
        .publish-button {
          padding: 0.6rem 1.5rem;
          background-color: var(--theme-success-500, #10b981);
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
          font-weight: 500;
        }
        
        .publish-button:hover {
          background-color: var(--theme-success-600, #059669);
        }
        
        button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        
        .regenerate-form {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }
        
        .regenerate-form > form {
          background-color: white;
          padding: 2rem;
          border-radius: 6px;
          width: 90%;
          max-width: 600px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .regenerate-form h3 {
          margin-top: 0;
          margin-bottom: 1rem;
          font-size: 1.3rem;
        }
        
        .regenerate-form textarea {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          margin-bottom: 1.5rem;
          font-size: 1rem;
        }
        
        .regenerate-form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 1rem;
        }
        
        .cancel-button {
          padding: 0.6rem 1rem;
          background-color: white;
          border: 1px solid var(--theme-elevation-300);
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
        }
        
        .cancel-button:hover {
          background-color: var(--theme-elevation-100);
        }
        
        .submit-button {
          padding: 0.6rem 1.5rem;
          background-color: var(--theme-primary-500, #3b82f6);
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.9rem;
          font-weight: 500;
        }
        
        .submit-button:hover {
          background-color: var(--theme-primary-600, #2563eb);
        }
      `}</style>
    </div>
    )
  }
  
  export default ContentPreview