import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Divider, 
  List, 
  ListItem, 
  ListItemText, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  <PERSON>,
  Stack,
  Avatar,
  Tooltip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ChatIcon from '@mui/icons-material/Chat';
import AssignmentIcon from '@mui/icons-material/Assignment';

// Agent colors for visual identification
const agentColors = {
  'market-research': '#8884d8',
  'seo-keyword': '#82ca9d',
  'content-strategy': '#ffc658',
  'content-generation': '#ff8042',
  'seo-optimization': '#0088fe',
  'orchestrator': '#7e57c2',
  'system': '#9e9e9e'
};

// Agent display names
const agentNames = {
  'market-research': 'Market Research',
  'seo-keyword': 'SEO Keyword',
  'content-strategy': 'Content Strategy',
  'content-generation': 'Content Writer',
  'seo-optimization': 'SEO Optimizer',
  'orchestrator': 'Orchestrator',
  'system': 'System'
};

interface DiscussionsPanelProps {
  discussions: Record<string, any>;
}

export const DiscussionsPanel: React.FC<DiscussionsPanelProps> = ({ discussions }) => {
  if (!discussions || Object.keys(discussions).length === 0) {
    return (
      <Paper elevation={0} sx={{ p: 2, bgcolor: 'background.paper', border: '1px dashed grey' }}>
        <Box display="flex" alignItems="center" justifyContent="center" flexDirection="column" py={3}>
          <ChatIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
          <Typography variant="body1" color="text.secondary">
            No discussions have been started yet.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Box>
      {Object.entries(discussions).map(([discussionId, discussion]: [string, any]) => (
        <Accordion key={discussionId} defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            sx={{ 
              bgcolor: 'rgba(0, 0, 0, 0.03)',
              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.05)' }
            }}
          >
            <Box display="flex" alignItems="center" width="100%">
              <ChatIcon sx={{ mr: 1.5, color: 'primary.main' }} />
              <Box flexGrow={1}>
                <Typography variant="subtitle1" fontWeight="medium">
                  {discussion.topic}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Lead: {agentNames[discussion.leadAgent as keyof typeof agentNames] || discussion.leadAgent} • 
                  {discussion.status === 'active' ? 
                    ' Active' : discussion.status === 'resolved' ? ' Resolved' : ' Pending'}
                </Typography>
              </Box>
              <Chip 
                size="small" 
                label={`${discussion.messages?.length || 0} messages`}
                sx={{ ml: 1 }}
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 0 }}>
            <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>
              {discussion.messages?.map((message: any, index: number) => (
                <React.Fragment key={message.id || index}>
                  <ListItem 
                    alignItems="flex-start"
                    sx={{ 
                      pl: 2, 
                      pr: 2,
                      bgcolor: message.from === discussion.leadAgent ? 'rgba(0, 0, 0, 0.01)' : 'inherit'
                    }}
                  >
                    <Box display="flex" width="100%">
                      <Avatar 
                        sx={{ 
                          width: 36, 
                          height: 36, 
                          mr: 2, 
                          bgcolor: agentColors[message.from as keyof typeof agentColors] || 'grey',
                          fontSize: '0.875rem'
                        }}
                      >
                        {message.from.substring(0, 2).toUpperCase()}
                      </Avatar>
                      <Box flexGrow={1}>
                        <Box display="flex" justifyContent="space-between" mb={0.5}>
                          <Typography variant="subtitle2" component="span">
                            {agentNames[message.from as keyof typeof agentNames] || message.from}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </Box>
                        <Typography variant="body2" gutterBottom>
                          {message.content.message || message.content.contribution || 
                           message.content.perspective || message.content.synthesis || 
                           JSON.stringify(message.content)}
                        </Typography>
                        {message.reasoning && (
                          <Tooltip title="Reasoning">
                            <Chip
                              icon={<AssignmentIcon fontSize="small" />}
                              label={message.reasoning.decision || "View reasoning"}
                              variant="outlined"
                              size="small"
                              sx={{ mt: 1, fontSize: '0.7rem' }}
                            />
                          </Tooltip>
                        )}
                      </Box>
                    </Box>
                  </ListItem>
                  {index < discussion.messages.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </React.Fragment>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
};
