'use client';

import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Chip, 
  List, 
  ListItem, 
  ListItemText,
  Divider,
  LinearProgress,
  Stack
} from '@mui/material';
import Grid from '@mui/material/Grid';

interface AgentProgressDisplayProps {
  state: any;
}

export const AgentProgressDisplay: React.FC<AgentProgressDisplayProps> = ({ state }) => {
  const getAgentStatus = (agentId: string) => {
    const agentState = state.agentStates?.[agentId];
    
    if (!agentState) {
      return 'inactive';
    }
    
    const processedRequests = agentState.processedRequests?.length || 0;
    const generatedArtifacts = agentState.generatedArtifacts?.length || 0;
    
    if (generatedArtifacts > 0) {
      return 'generated content';
    }
    
    if (processedRequests > 0) {
      return 'active';
    }
    
    return 'waiting';
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated content':
        return 'success';
      case 'active':
        return 'primary';
      case 'waiting':
        return 'warning';
      default:
        return 'default';
    }
  };
  
  const getPhaseProgress = () => {
    const phases = ['planning', 'research', 'creation', 'review', 'refinement', 'finalization'];
    const currentPhaseIndex = phases.indexOf(state.currentPhase);
    
    if (currentPhaseIndex === -1) {
      return 0;
    }
    
    return ((currentPhaseIndex + 1) / phases.length) * 100;
  };
  
  const renderMessages = () => {
    if (!state.messages || state.messages.length === 0) {
      return (
        <Typography variant="body2" color="textSecondary">
          No messages yet
        </Typography>
      );
    }
    
    // Sort messages by timestamp
    const sortedMessages = [...state.messages].sort(
      (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    return (
      <List dense>
        {sortedMessages.slice(0, 10).map((message: any) => (
          <ListItem key={`agent-progress-message-${message.id}-${Math.random().toString(36).substring(2, 9)}`} alignItems="flex-start">
            <ListItemText
              primary={
                <Typography variant="body2" fontWeight="medium">
                  {message.from} → {typeof message.to === 'string' ? message.to : message.to.join(', ')}
                </Typography>
              }
              secondaryTypographyProps={{ component: 'div' }}
              secondary={
                <>
                  <Typography variant="caption" color="textSecondary" component="span">
                    {new Date(message.timestamp).toLocaleString()} | {message.type}
                  </Typography>
                  <Box sx={{ mt: 0.5 }}>
                    <Typography variant="body2">
                      {message.type === 'FEEDBACK' && (
                        message.content.feedback.substring(0, 100) + 
                        (message.content.feedback.length > 100 ? '...' : '')
                      )}
                      {message.type === 'CONSULTATION_REQUEST' && (
                        message.content.question
                      )}
                      {message.type === 'ARTIFACT_REQUEST' && (
                        `Request for ${message.content.artifactType}`
                      )}
                      {message.type === 'ARTIFACT_DELIVERY' && (
                        `Delivered artifact: ${message.content.artifactId}`
                      )}
                      {message.type === 'ACKNOWLEDGMENT' && (
                        message.content.message
                      )}
                    </Typography>
                  </Box>
                </>
              }
            />
          </ListItem>
        ))}
      </List>
    );
  };
  
  return (
    <Box>
      <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
        <Typography variant="subtitle1" gutterBottom>
          Session Progress
        </Typography>
        <Stack spacing={1}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" component="span">Current Phase:</Typography>
            <Chip size="small" label={state.currentPhase} color="primary" />
          </Box>
          <Box>
            <LinearProgress variant="determinate" value={getPhaseProgress()} sx={{ height: 10, borderRadius: 5 }} />
          </Box>
          <Typography variant="caption" color="textSecondary">
            Iterations: {state.iterations} / {state.maxIterations}
          </Typography>
        </Stack>
      </Paper>
      
      <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
        <Typography variant="subtitle1" gutterBottom>
          Agent Status
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ width: { xs: '45%', sm: '22%' } }}>
            <Chip 
              label="Content Generation" 
              size="small"
              color={getStatusColor(getAgentStatus('content-generation'))}
              sx={{ width: '100%' }}
            />
          </Box>
          <Box sx={{ width: { xs: '45%', sm: '22%' } }}>
            <Chip 
              label="SEO Keyword" 
              size="small"
              color={getStatusColor(getAgentStatus('seo-keyword'))}
              sx={{ width: '100%' }}
            />
          </Box>
          <Box sx={{ width: { xs: '45%', sm: '22%' } }}>
            <Chip 
              label="Market Research" 
              size="small"
              color={getStatusColor(getAgentStatus('market-research'))}
              sx={{ width: '100%' }}
            />
          </Box>
          <Box sx={{ width: { xs: '45%', sm: '22%' } }}>
            <Chip 
              label="Content Strategy" 
              size="small"
              color={getStatusColor(getAgentStatus('content-strategy'))}
              sx={{ width: '100%' }}
            />
          </Box>
        </Box>
      </Paper>
      
      <Paper elevation={0} sx={{ p: 2, bgcolor: 'background.default' }}>
        <Typography variant="subtitle1" gutterBottom>
          Recent Messages
        </Typography>
        {renderMessages()}
      </Paper>
    </Box>
  );
};
