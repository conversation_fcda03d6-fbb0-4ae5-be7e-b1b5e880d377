import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Divider, 
  Card,
  CardHeader,
  CardContent,
  Chip,
  Avatar,
  Stack,
  Tooltip,
  IconButton
} from '@mui/material';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Agent colors for visual identification
const agentColors = {
  'market-research': '#8884d8',
  'seo-keyword': '#82ca9d',
  'content-strategy': '#ffc658',
  'content-generation': '#ff8042',
  'seo-optimization': '#0088fe',
  'orchestrator': '#7e57c2',
  'system': '#9e9e9e'
};

// Agent display names
const agentNames = {
  'market-research': 'Market Research',
  'seo-keyword': 'SEO Keyword',
  'content-strategy': 'Content Strategy',
  'content-generation': 'Content Writer',
  'seo-optimization': 'SEO Optimizer',
  'orchestrator': 'Orchestrator',
  'system': 'System'
};

interface ConsultationsPanelProps {
  consultations: Record<string, any>;
}

export const ConsultationsPanel: React.FC<ConsultationsPanelProps> = ({ consultations }) => {
  if (!consultations || Object.keys(consultations).length === 0) {
    return (
      <Paper elevation={0} sx={{ p: 2, bgcolor: 'background.paper', border: '1px dashed grey' }}>
        <Box display="flex" alignItems="center" justifyContent="center" flexDirection="column" py={3}>
          <QuestionAnswerIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
          <Typography variant="body1" color="text.secondary">
            No consultations have occurred yet.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Stack spacing={2}>
      {Object.entries(consultations).map(([consultationId, consultation]: [string, any]) => (
        <Card key={consultationId} sx={{ position: 'relative' }}>
          <CardHeader
            avatar={
              <Avatar sx={{ bgcolor: agentColors[consultation.fromAgent as keyof typeof agentColors] || 'grey' }}>
                {consultation.fromAgent.substring(0, 2).toUpperCase()}
              </Avatar>
            }
            title={
              <Box display="flex" alignItems="center">
                <Typography variant="subtitle1">
                  {agentNames[consultation.fromAgent as keyof typeof agentNames] || consultation.fromAgent}
                  <Typography component="span" variant="body2" color="text.secondary" sx={{ mx: 0.5 }}>
                    asked
                  </Typography>
                  {agentNames[consultation.toAgent as keyof typeof agentNames] || consultation.toAgent}
                </Typography>
                {consultation.status === 'resolved' ? (
                  <Tooltip title="Resolved">
                    <CheckCircleIcon color="success" fontSize="small" sx={{ ml: 1 }} />
                  </Tooltip>
                ) : (
                  <Tooltip title="Pending">
                    <PendingIcon color="action" fontSize="small" sx={{ ml: 1 }} />
                  </Tooltip>
                )}
              </Box>
            }
            subheader={
              <Typography variant="caption" color="text.secondary">
                {new Date(consultation.createdAt).toLocaleString()}
              </Typography>
            }
            action={
              <Tooltip title="Consultation">
                <IconButton aria-label="consultation icon" size="small">
                  <HelpOutlineIcon />
                </IconButton>
              </Tooltip>
            }
            sx={{ pb: 0 }}
          />
          <CardContent>
            <Box sx={{ mb: 1.5 }}>
              <Typography variant="body1" fontWeight="medium" gutterBottom>
                Question:
              </Typography>
              <Typography variant="body2" paragraph sx={{ pl: 1 }}>
                {consultation.question}
              </Typography>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Box>
              <Typography variant="body1" fontWeight="medium" gutterBottom>
                Response:
              </Typography>
              {consultation.response ? (
                <Typography variant="body2" paragraph sx={{ pl: 1 }}>
                  {consultation.response}
                </Typography>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ pl: 1, fontStyle: 'italic' }}>
                  Awaiting response...
                </Typography>
              )}
            </Box>
            
            {consultation.context && Object.keys(consultation.context).length > 0 && (
              <Box mt={2}>
                <Typography variant="caption" color="text.secondary">
                  Context:
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" mt={0.5}>
                  {Object.entries(consultation.context).map(([key, value]: [string, any]) => (
                    <Chip 
                      key={key} 
                      label={`${key}: ${typeof value === 'string' ? value : JSON.stringify(value)}`}
                      size="small" 
                      variant="outlined"
                    />
                  ))}
                </Stack>
              </Box>
            )}
            
            {consultation.reasoning && (
              <Tooltip title={consultation.reasoning.process || "Reasoning process"}>
                <Chip
                  icon={<AssignmentIcon fontSize="small" />}
                  label={consultation.reasoning.decision || "View reasoning"}
                  variant="outlined"
                  size="small"
                  sx={{ mt: 2, fontSize: '0.7rem' }}
                />
              </Tooltip>
            )}
          </CardContent>
        </Card>
      ))}
    </Stack>
  );
};
