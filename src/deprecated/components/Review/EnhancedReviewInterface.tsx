/**
 * Enhanced Review Interface
 * Multi-reviewer support with improved UI
 */

'use client';

import { useState, useEffect } from 'react';
import { ReviewData, ReviewAssignment, Reviewer, ReviewDecision } from '../../core/review/types';

interface EnhancedReviewInterfaceProps {
  reviewId: string;
  onReviewComplete?: (reviewId: string, decision: string) => void;
}

interface EnhancedReviewData extends ReviewData {
  assignments?: ReviewAssignment[];
  reviewers?: Reviewer[];
  timeRemaining?: number;
  escalationHistory?: EscalationEvent[];
}

interface EscalationEvent {
  id: string;
  timestamp: string;
  reason: string;
  escalatedBy: string;
  escalatedTo: string[];
}

export default function EnhancedReviewInterface({ 
  reviewId, 
  onReviewComplete 
}: EnhancedReviewInterfaceProps) {
  const [reviewData, setReviewData] = useState<EnhancedReviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [decision, setDecision] = useState<'approve' | 'reject' | ''>('');
  const [feedback, setFeedback] = useState('');
  const [activeTab, setActiveTab] = useState<'content' | 'assignments' | 'history'>('content');

  useEffect(() => {
    loadReviewData();
  }, [reviewId]);

  const loadReviewData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/review/${reviewId}/enhanced`);
      const result = await response.json();

      if (result.success) {
        setReviewData(result.data);
      } else {
        setError(result.error || 'Failed to load review');
      }
    } catch (err) {
      setError('Failed to load review');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const submitReview = async () => {
    if (!decision) return;

    setSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/review/${reviewId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          decision,
          feedback: feedback || undefined,
          reviewer: 'current-user' // In production, get from auth
        })
      });

      const result = await response.json();

      if (result.success) {
        setReviewData(prev => prev ? { ...prev, status: 'completed' } : null);
        onReviewComplete?.(reviewId, decision);
      } else {
        setError(result.error || 'Failed to submit review');
      }
    } catch (err) {
      setError('Failed to submit review');
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimeRemaining = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-center text-gray-600 mt-4">Loading review...</p>
        </div>
      </div>
    );
  }

  if (error && !reviewData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
          <div className="text-red-600 text-center">
            <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h2 className="text-xl font-semibold mb-2">Error Loading Review</h2>
            <p className="text-gray-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!reviewData) return null;

  const isCompleted = reviewData.status === 'completed';
  const isExpired = reviewData.status === 'expired';
  const canSubmit = !isCompleted && !isExpired && decision;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Content Review</h1>
              <p className="text-gray-600 mt-1">Review ID: {reviewId}</p>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Status Badge */}
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(reviewData.status)}`}>
                {reviewData.status.replace('_', ' ').toUpperCase()}
              </span>
              
              {/* Time Remaining */}
              {reviewData.timeRemaining && reviewData.timeRemaining > 0 && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Time remaining:</span> {formatTimeRemaining(reviewData.timeRemaining)}
                </div>
              )}
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="mt-6">
            <nav className="flex space-x-8">
              {['content', 'assignments', 'history'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'content' && (
              <div className="bg-white rounded-lg shadow-lg">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">{reviewData.content.title}</h2>
                  <p className="text-gray-600 mt-1">{reviewData.content.type}</p>
                </div>
                
                <div className="p-6">
                  <div className="prose max-w-none">
                    {typeof reviewData.content.data === 'string' ? (
                      <div className="whitespace-pre-wrap">{reviewData.content.data}</div>
                    ) : (
                      <pre className="bg-gray-50 p-4 rounded-lg overflow-auto">
                        {JSON.stringify(reviewData.content.data, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'assignments' && (
              <div className="bg-white rounded-lg shadow-lg">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Review Assignments</h2>
                </div>
                
                <div className="p-6">
                  {reviewData.assignments && reviewData.assignments.length > 0 ? (
                    <div className="space-y-4">
                      {reviewData.assignments.map((assignment) => (
                        <div key={assignment.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium text-gray-900">
                              Reviewer: {assignment.reviewerId}
                            </div>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(assignment.status)}`}>
                              {assignment.status}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>Role: {assignment.role}</div>
                            <div>Priority: {assignment.priority}</div>
                            <div>Assigned: {new Date(assignment.assignedAt).toLocaleDateString()}</div>
                            <div>Deadline: {new Date(assignment.deadline).toLocaleDateString()}</div>
                          </div>
                          
                          {assignment.feedback && (
                            <div className="mt-3 p-3 bg-gray-50 rounded">
                              <div className="text-sm font-medium text-gray-900">Feedback:</div>
                              <div className="text-sm text-gray-600 mt-1">{assignment.feedback}</div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No assignments found.</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div className="bg-white rounded-lg shadow-lg">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Review History</h2>
                </div>
                
                <div className="p-6">
                  {reviewData.escalationHistory && reviewData.escalationHistory.length > 0 ? (
                    <div className="space-y-4">
                      {reviewData.escalationHistory.map((event) => (
                        <div key={event.id} className="border-l-4 border-orange-400 pl-4">
                          <div className="font-medium text-gray-900">Escalation Event</div>
                          <div className="text-sm text-gray-600 mt-1">
                            <div>Reason: {event.reason}</div>
                            <div>Escalated by: {event.escalatedBy}</div>
                            <div>Date: {new Date(event.timestamp).toLocaleString()}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No escalation history.</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Instructions */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Instructions</h3>
              <p className="text-gray-600 text-sm">{reviewData.instructions}</p>
            </div>

            {/* Review Actions */}
            {!isCompleted && !isExpired && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Review</h3>
                
                <div className="space-y-4">
                  {/* Decision */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Decision</label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="decision"
                          value="approve"
                          checked={decision === 'approve'}
                          onChange={(e) => setDecision(e.target.value as 'approve')}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-900">Approve</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="decision"
                          value="reject"
                          checked={decision === 'reject'}
                          onChange={(e) => setDecision(e.target.value as 'reject')}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-900">Reject</span>
                      </label>
                    </div>
                  </div>

                  {/* Feedback */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Feedback {decision === 'reject' && <span className="text-red-500">*</span>}
                    </label>
                    <textarea
                      value={feedback}
                      onChange={(e) => setFeedback(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder={decision === 'reject' ? 'Please explain why you are rejecting this content...' : 'Optional feedback or suggestions...'}
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    onClick={submitReview}
                    disabled={!canSubmit || submitting}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                  >
                    {submitting ? 'Submitting...' : `Submit ${decision === 'approve' ? 'Approval' : decision === 'reject' ? 'Rejection' : 'Review'}`}
                  </button>
                </div>
              </div>
            )}

            {/* Status Messages */}
            {isCompleted && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex">
                  <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">Review Completed</h3>
                    <p className="text-sm text-green-700 mt-1">This review has been completed.</p>
                  </div>
                </div>
              </div>
            )}

            {isExpired && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Review Expired</h3>
                    <p className="text-sm text-red-700 mt-1">This review has expired and can no longer be completed.</p>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
