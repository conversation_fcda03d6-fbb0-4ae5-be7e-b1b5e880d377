'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  AppBar,
  <PERSON>lbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  Button,
  useTheme,
  useMediaQuery
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArticleIcon from '@mui/icons-material/Article';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import GroupIcon from '@mui/icons-material/Group';
import FolderIcon from '@mui/icons-material/Folder';
import ChatIcon from '@mui/icons-material/Chat';
import PsychologyIcon from '@mui/icons-material/Psychology';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';

// Components
import ArticleInitiationFormV3 from './ArticleInitiationFormV3';
import AgentCollaborationGraphV3 from './AgentCollaborationGraphV3';
import GoalProgressTrackerV3 from './GoalProgressTrackerV3';
import ArtifactGalleryV3 from './ArtifactGalleryV3';
import PhaseTransitionVisualizerV3 from './PhaseTransitionVisualizerV3';
import ArticlePreviewV3 from './ArticlePreviewV3';

// Client
import { dynamicCollaborationClientV3 } from '../../lib/dynamic-collaboration-client-v3';

// Types
import { WorkflowPhase, SessionStatus } from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface DynamicCollaborationDashboardV3Props {
  initialSessionId?: string;
}

const DynamicCollaborationDashboardV3: React.FC<DynamicCollaborationDashboardV3Props> = ({
  initialSessionId
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [activeTab, setActiveTab] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(initialSessionId || null);
  const [collaborationState, setCollaborationState] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Toggle drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Fetch session data
  const fetchSessionData = async (sid: string) => {
    setLoading(true);
    setError(null);

    try {
      const state = await dynamicCollaborationClientV3.getState(sid);
      setCollaborationState(state);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Start a new collaboration session
  const startNewSession = async (formData: any) => {
    setLoading(true);
    setError(null);

    try {
      const result = await dynamicCollaborationClientV3.initiate(formData);
      setSessionId(result.sessionId);
      setActiveTab(1); // Switch to the overview tab

      setSnackbar({
        open: true,
        message: 'New collaboration session started successfully',
        severity: 'success'
      });

      // Fetch initial state
      await fetchSessionData(result.sessionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Progress the workflow
  const progressSession = async () => {
    if (!sessionId) return;

    setLoading(true);
    setError(null);

    try {
      // Make multiple progress attempts to ensure we move through phases
      let madeProgress = false;
      let attempts = 0;
      const maxAttempts = 5;

      // Keep trying until we make progress or reach max attempts
      while (attempts < maxAttempts) {
        attempts++;

        // Try to progress with increasing steps for more reliable progression
        const steps = attempts <= 2 ? 1 : 3;

        // Call the progress endpoint
        const result = await dynamicCollaborationClientV3.progressSession(sessionId, steps);

        // If we made progress or changed phases, consider it successful
        if (result.progressMade || result.initialPhase !== result.currentPhase) {
          madeProgress = true;

          // Fetch updated state after each successful step
          await fetchSessionData(sessionId);

          // If we've reached the final phase, stop
          if (result.currentPhase === WorkflowPhase.FINALIZATION ||
              collaborationState?.currentPhase === WorkflowPhase.FINALIZATION) {
            break;
          }
        } else if (attempts >= maxAttempts - 1) {
          // If we're on the last attempt, try one more time with a larger step
          const lastResult = await dynamicCollaborationClientV3.progressSession(sessionId, 5);

          if (lastResult.progressMade || lastResult.initialPhase !== lastResult.currentPhase) {
            madeProgress = true;
          }

          // Fetch updated state
          await fetchSessionData(sessionId);
          break;
        }
      }

      if (madeProgress) {
        setSnackbar({
          open: true,
          message: `Workflow progressed to ${collaborationState?.currentPhase}`,
          severity: 'success'
        });
      } else {
        setSnackbar({
          open: true,
          message: 'No progress was made after multiple attempts. The workflow may be waiting for dependencies or already complete.',
          severity: 'info'
        });
      }

      // Final fetch to ensure we have the latest state
      await fetchSessionData(sessionId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Calculate progress percentage
  const calculateProgress = (): number => {
    if (!collaborationState) {
      return 0;
    }

    // Calculate based on completed goals
    const totalGoals = Object.keys(collaborationState.goals).length;
    if (totalGoals === 0) {
      return 0;
    }

    const completedGoals = collaborationState.completedGoalIds.length;
    return Math.round((completedGoals / totalGoals) * 100);
  };

  // Load initial session data if sessionId is provided
  useEffect(() => {
    if (sessionId) {
      fetchSessionData(sessionId);

      // Set up polling for updates
      const intervalId = setInterval(() => {
        fetchSessionData(sessionId);
      }, 10000); // Poll every 10 seconds

      return () => clearInterval(intervalId);
    }
  }, [sessionId]);

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 0: // New Session
        return (
          <ArticleInitiationFormV3
            onSubmit={startNewSession}
            loading={loading}
          />
        );
      case 1: // Overview
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <AgentCollaborationGraphV3
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
                onRefresh={() => fetchSessionData(sessionId!)}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <GoalProgressTrackerV3
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
                onRefresh={() => fetchSessionData(sessionId!)}
              />
            </Grid>

            {/* Phase Transition Visualizer */}
            <Grid item xs={12}>
              <PhaseTransitionVisualizerV3
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
              />
            </Grid>
          </Grid>
        );
      case 2: // Artifacts
        return (
          <ArtifactGalleryV3
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
            onRefresh={() => fetchSessionData(sessionId!)}
          />
        );
      case 3: // Article Preview
        return (
          <ArticlePreviewV3
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={toggleDrawer}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Dynamic Agent Collaboration V3
          </Typography>
          {sessionId && (
            <>
              <Chip
                label={`Session: ${sessionId.substring(0, 8)}...`}
                color="secondary"
                sx={{ mr: 1 }}
              />
              <Chip
                label={`Progress: ${calculateProgress()}%`}
                color="primary"
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                color="secondary"
                size="small"
                onClick={() => progressSession()}
                disabled={loading || (collaborationState?.currentPhase === WorkflowPhase.FINALIZATION)}
                startIcon={<PlayArrowIcon />}
                sx={{ mr: 1, textTransform: 'none', fontWeight: 'bold' }}
              >
                Progress Workflow
              </Button>
              <IconButton color="inherit" onClick={() => fetchSessionData(sessionId)}>
                <RefreshIcon />
              </IconButton>
            </>
          )}
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Drawer
        variant={isMobile ? 'temporary' : 'persistent'}
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{
          width: 240,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            <ListItem button onClick={() => setActiveTab(0)}>
              <ListItemIcon>
                <ArticleIcon />
              </ListItemIcon>
              <ListItemText primary="New Session" />
            </ListItem>
            <Divider />
            <ListItem button onClick={() => setActiveTab(1)} disabled={!sessionId}>
              <ListItemIcon>
                <TrackChangesIcon />
              </ListItemIcon>
              <ListItemText primary="Overview" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(2)} disabled={!sessionId}>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText primary="Artifacts" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(3)} disabled={!sessionId}>
              <ListItemIcon>
                <VisibilityIcon />
              </ListItemIcon>
              <ListItemText primary="Article Preview" />
            </ListItem>
          </List>

          {collaborationState && (
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Current Phase
              </Typography>
              <Chip
                label={collaborationState.currentPhase}
                color="primary"
                size="small"
                icon={
                  collaborationState.currentPhase === WorkflowPhase.FINALIZATION ?
                  <CheckCircleIcon /> : undefined
                }
                sx={{ width: '100%', justifyContent: 'left' }}
              />

              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Session Status
              </Typography>
              <Chip
                label={collaborationState.status}
                color={
                  collaborationState.status === 'completed' ? 'success' :
                  collaborationState.status === 'failed' ? 'error' :
                  collaborationState.status === 'paused' ? 'warning' : 'info'
                }
                size="small"
                sx={{ width: '100%', justifyContent: 'left' }}
              />
            </Box>
          )}
        </Box>
      </Drawer>

      {/* Main content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3, mt: 8 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Mobile tabs */}
        {isMobile && (
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ mb: 2 }}
          >
            <Tab icon={<ArticleIcon />} label="New" />
            <Tab icon={<TrackChangesIcon />} label="Overview" disabled={!sessionId} />
            <Tab icon={<FolderIcon />} label="Artifacts" disabled={!sessionId} />
            <Tab icon={<VisibilityIcon />} label="Preview" disabled={!sessionId} />
          </Tabs>
        )}

        {renderContent()}
      </Box>
    </Box>
  );
};

export default DynamicCollaborationDashboardV3;
