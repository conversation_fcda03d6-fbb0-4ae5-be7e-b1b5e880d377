// src/components/GoalOrchestrator/ArtifactEvaluation.tsx

import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Chip, Divider,
  Button, Dialog, DialogTitle, DialogContent,
  DialogActions, Rating, TextField, CircularProgress,
  Accordion, AccordionSummary, AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ArtifactStatus } from '../../app/(payload)/api/agents/dynamic-collaboration-v3';
import { goalOrchestratorClient } from '../../lib/goal-based-collaboration-client';

interface ArtifactEvaluationProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const ArtifactEvaluation: React.FC<ArtifactEvaluationProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const [artifacts, setArtifacts] = useState<any[]>([]);
  const [selectedArtifact, setSelectedArtifact] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [feedbackRating, setFeedbackRating] = useState<number>(3);
  const [feedbackText, setFeedbackText] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (state && state.artifacts) {
      // Convert artifacts object to array
      const artifactsArray = Object.values(state.artifacts);

      // Sort by creation date (newest first)
      artifactsArray.sort((a: any, b: any) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setArtifacts(artifactsArray);
    }
  }, [state]);

  const handleArtifactClick = (artifact: any) => {
    setSelectedArtifact(artifact);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setFeedbackRating(3);
    setFeedbackText('');
  };

  const handleSubmitFeedback = async () => {
    if (!selectedArtifact) return;

    setSubmitting(true);

    try {
      await goalOrchestratorClient.sendFeedback(sessionId, selectedArtifact.id, {
        overallRating: feedbackRating * 20, // Convert 0-5 to 0-100
        strengths: feedbackText.split('\n').filter(line => line.trim().startsWith('+')),
        areasForImprovement: feedbackText.split('\n').filter(line => line.trim().startsWith('-')),
        specificFeedback: [],
        summary: feedbackText
      });

      if (onRefresh) {
        onRefresh();
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Error submitting feedback:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderArtifactContent = (artifact: any) => {
    if (typeof artifact.content === 'string') {
      return <Typography variant="body2">{artifact.content}</Typography>;
    }

    if (typeof artifact.content === 'object') {
      return (
        <Box>
          {Object.entries(artifact.content).map(([key, value]) => (
            <Accordion key={key} defaultExpanded={key === 'title'}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>{key.charAt(0).toUpperCase() + key.slice(1)}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                {typeof value === 'string' ? (
                  <Typography variant="body2">{value}</Typography>
                ) : Array.isArray(value) ? (
                  <ul>
                    {(value as any[]).map((item, index) => (
                      <li key={index}>
                        <Typography variant="body2">
                          {typeof item === 'string' ? item : JSON.stringify(item)}
                        </Typography>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <Typography variant="body2">{JSON.stringify(value, null, 2)}</Typography>
                )}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      );
    }

    return <Typography variant="body2">{JSON.stringify(artifact.content, null, 2)}</Typography>;
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>Artifact Evaluation</Typography>

      {loading ? (
        <CircularProgress size={24} sx={{ display: 'block', mx: 'auto', my: 2 }} />
      ) : artifacts.length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No artifacts available for evaluation
        </Typography>
      ) : (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {artifacts.map(artifact => (
            <Paper
              key={artifact.id}
              variant="outlined"
              sx={{
                p: 2,
                cursor: 'pointer',
                '&:hover': { bgcolor: 'action.hover' }
              }}
              onClick={() => handleArtifactClick(artifact)}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1">{artifact.title}</Typography>
                <Chip
                  label={artifact.status}
                  color={
                    artifact.status === ArtifactStatus.APPROVED ? 'success' :
                    artifact.status === ArtifactStatus.REVIEW ? 'warning' :
                    artifact.status === ArtifactStatus.REJECTED ? 'error' :
                    'default'
                  }
                  size="small"
                />
              </Box>

              <Typography variant="body2" color="text.secondary" noWrap>
                {typeof artifact.content === 'string'
                  ? artifact.content.substring(0, 100) + '...'
                  : `${artifact.type} artifact created by ${artifact.createdBy}`
                }
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  Created: {new Date(artifact.createdAt).toLocaleString()}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Type: {artifact.type}
                </Typography>
              </Box>
            </Paper>
          ))}
        </Box>
      )}

      {/* Artifact Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedArtifact && (
          <>
            <DialogTitle>
              {selectedArtifact.title}
              <Chip
                label={selectedArtifact.status}
                color={
                  selectedArtifact.status === ArtifactStatus.APPROVED ? 'success' :
                  selectedArtifact.status === ArtifactStatus.REVIEW ? 'warning' :
                  selectedArtifact.status === ArtifactStatus.REJECTED ? 'error' :
                  'default'
                }
                size="small"
                sx={{ ml: 1 }}
              />
            </DialogTitle>
            <DialogContent dividers>
              <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                Created by {selectedArtifact.createdBy} on {new Date(selectedArtifact.createdAt).toLocaleString()}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 3 }}>
                {renderArtifactContent(selectedArtifact)}
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                Provide Feedback
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="body2" sx={{ mr: 2 }}>
                  Rating:
                </Typography>
                <Rating
                  value={feedbackRating}
                  onChange={(_, newValue) => {
                    setFeedbackRating(newValue || 3);
                  }}
                />
              </Box>

              <TextField
                fullWidth
                multiline
                rows={4}
                label="Feedback"
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
                placeholder="Provide your feedback here. Use + for strengths and - for areas of improvement."
                variant="outlined"
                disabled={submitting}
              />

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                Tip: Start lines with + for strengths and - for areas that need improvement.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog} disabled={submitting}>
                Cancel
              </Button>
              <Button
                onClick={handleSubmitFeedback}
                variant="contained"
                color="primary"
                disabled={submitting || !feedbackText.trim()}
              >
                {submitting ? <CircularProgress size={24} /> : 'Submit Feedback'}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Paper>
  );
};

export default ArtifactEvaluation;