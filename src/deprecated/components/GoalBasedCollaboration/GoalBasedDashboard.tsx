'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Drawer,
  App<PERSON><PERSON>,
  <PERSON>l<PERSON>,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  Alert,
  Snackbar,
  Button,
  useTheme,
  useMediaQuery,
  CircularProgress
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArticleIcon from '@mui/icons-material/Article';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';
import GroupIcon from '@mui/icons-material/Group';
import FolderIcon from '@mui/icons-material/Folder';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

// Components
import ArticleInitiationForm from '../DynamicCollaborationV3/ArticleInitiationFormV3';
import WorkflowProgressVisualization from './WorkflowProgressVisualization';
import ArtifactEvaluationDisplay from './ArtifactEvaluationDisplay';
import GoalVisualization from './GoalVisualization';
import ArticlePreview from './ArticlePreview';
import StatusMessage from './StatusMessage';
import AgentCollaborationGraphForGoals from './AgentCollaborationGraphForGoals';

// Client
import { goalOrchestratorClient } from '../../lib/goal-orchestrator-client';

interface GoalBasedDashboardProps {
  initialSessionId?: string;
}

const GoalBasedDashboard: React.FC<GoalBasedDashboardProps> = ({
  initialSessionId
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [activeTab, setActiveTab] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(initialSessionId || null);
  const [collaborationState, setCollaborationState] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Process state to ensure consistent structure
  const processState = (state: any): any => {
    if (!state) {
      // Return a default state structure if state is null
      return {
        goals: { byId: {}, activeIds: [], completedIds: [] },
        workflowProgress: {
          currentPhase: 'PLANNING',
          phaseProgress: {
            PLANNING: 0,
            RESEARCH: 0,
            CREATION: 0,
            REVIEW: 0,
            FINALIZATION: 0
          },
          overallProgress: 0,
          milestones: []
        },
        artifacts: {}
      };
    }

    // Create a deep copy to avoid modifying the original state
    const processedState = JSON.parse(JSON.stringify(state));

    // Ensure goals structure
    if (!processedState.goals) {
      processedState.goals = { byId: {}, activeIds: [], completedIds: [] };
    } else if (!processedState.goals.byId) {
      // Convert old format to new format if needed
      const byId: Record<string, any> = {};
      const activeIds: string[] = [];
      const completedIds: string[] = [];

      Object.entries(processedState.goals).forEach(([id, goal]: [string, any]) => {
        byId[id] = goal;
        if (goal.status === 'ACTIVE' || goal.status === 'IN_PROGRESS') {
          activeIds.push(id);
        } else if (goal.status === 'COMPLETED') {
          completedIds.push(id);
        }
      });

      processedState.goals = { byId, activeIds, completedIds };
    }

    // Ensure workflowProgress structure
    if (!processedState.workflowProgress) {
      processedState.workflowProgress = {
        currentPhase: 'PLANNING',
        phaseProgress: {
          PLANNING: 0,
          RESEARCH: 0,
          CREATION: 0,
          REVIEW: 0,
          FINALIZATION: 0
        },
        overallProgress: 0,
        milestones: []
      };
    }

    // Ensure artifacts structure
    if (!processedState.artifacts) {
      processedState.artifacts = {};
    }

    // Log the processed state for debugging
    console.log('Processed state:', processedState);

    return processedState;
  };

  // Fetch session data
  const fetchSessionData = useCallback(async (sid: string) => {
    setLoading(true);
    setError(null);

    try {
      let rawState;
      try {
        rawState = await goalOrchestratorClient.getState(sid);
        console.log('Raw state from API:', rawState);
      } catch (stateError) {
        console.error('Error fetching state:', stateError);
        // If we can't get the state, use a default state
        rawState = null;
      }

      // Process state to ensure consistent structure
      const state = processState(rawState);
      setCollaborationState(state);

      // Check if goals are defined
      const hasGoals = state.goals &&
                      state.goals.byId &&
                      Object.keys(state.goals.byId).length > 0;

      // If no goals, try to fix them
      if (!hasGoals) {
        console.log('No goals found, attempting to fix goals');
        await fixGoals(sid);
        // Fetch state again after fixing goals
        try {
          const updatedRawState = await goalOrchestratorClient.getState(sid);
          const updatedState = processState(updatedRawState);
          setCollaborationState(updatedState);
        } catch (updateError) {
          console.error('Error fetching updated state:', updateError);
          // Keep using the current state
        }
      }

      // Check if we need to auto-progress the session
      const shouldAutoProgress = state.goals &&
                               state.goals.activeIds &&
                               state.goals.activeIds.length > 0 &&
                               state.workflowProgress &&
                               state.workflowProgress.overallProgress < 100;

      if (shouldAutoProgress && !refreshInterval) {
        // Only auto-progress if this is not a regular refresh
        // This prevents multiple auto-progress calls during regular polling
        console.log('Auto-progressing session due to active goals');
        // Don't await to avoid blocking the UI
        setTimeout(() => {
          progressSession();
        }, 2000); // Wait 2 seconds before auto-progressing
      }

      // Only show success message on manual refresh
      if (!refreshInterval) {
        setSnackbar({
          open: true,
          message: 'Session data refreshed',
          severity: 'success'
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error fetching session data:', errorMessage);
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Fix goals for a session
  const fixGoals = async (sid: string) => {
    try {
      setLoading(true);

      const result = await goalOrchestratorClient.fixGoals(sid);

      setSnackbar({
        open: true,
        message: 'Goals fixed successfully',
        severity: 'success'
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error fixing goals:', errorMessage);

      setSnackbar({
        open: true,
        message: `Error fixing goals: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Progress the session
  const progressSession = useCallback(async () => {
    if (!sessionId) return;

    setLoading(true);
    setError(null);

    try {
      console.log('Progressing session:', sessionId);

      // Progress the session
      const progressResult = await goalOrchestratorClient.progressSession(sessionId);
      console.log('Progress result:', progressResult);

      // Fetch updated state immediately
      const updatedRawState = await goalOrchestratorClient.getState(sessionId);
      const updatedState = processState(updatedRawState);
      setCollaborationState(updatedState);

      // Show success message
      setSnackbar({
        open: true,
        message: 'Session progressed successfully',
        severity: 'success'
      });

      // Check if we need to continue progressing
      const shouldContinue = updatedState.goals &&
                           updatedState.goals.activeIds &&
                           updatedState.goals.activeIds.length > 0 &&
                           updatedState.workflowProgress &&
                           updatedState.workflowProgress.overallProgress < 100;

      if (shouldContinue) {
        console.log('Session has more active goals, will auto-progress again in 5 seconds');
        // Schedule another progress call after a delay
        setTimeout(() => {
          progressSession(); // Call progressSession again to continue the workflow
        }, 5000);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error progressing session:', errorMessage);
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error progressing session: ${errorMessage}`,
        severity: 'error'
      });

      // Try to fetch the latest state anyway
      setTimeout(() => {
        fetchSessionData(sessionId);
      }, 1000);
    } finally {
      setLoading(false);
    }
  }, [sessionId, fetchSessionData, processState]);

  // Process goals directly using the fixed processor - only used as a fallback
  const processGoalsFixed = useCallback(async () => {
    if (!sessionId) return;

    setLoading(true);
    setError(null);

    try {
      // Check if goals exist first
      const rawState = await goalOrchestratorClient.getState(sessionId);
      const state = processState(rawState);
      const hasGoals = state.goals &&
                      state.goals.byId &&
                      Object.keys(state.goals.byId).length > 0;

      // If no goals, fix them first
      if (!hasGoals) {
        console.log('No goals found, fixing goals before processing');
        await fixGoals(sessionId);
      }

      // Process goals with fixed processor
      await goalOrchestratorClient.processGoalsFixed(sessionId);
      await fetchSessionData(sessionId);

      setSnackbar({
        open: true,
        message: 'Goals processed successfully using fallback processor',
        severity: 'success'
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, [sessionId, fetchSessionData, processState]);

  // Force progress using the debug endpoint
  const forceProgress = async (sid: string) => {
    if (!sid) return;

    setLoading(true);
    setError(null);

    try {
      console.log('Forcing progress for session:', sid);

      // Use the client method to force progress
      const result = await goalOrchestratorClient.forceProgress(sid);
      console.log('Force progress result:', result);

      // Fetch updated state immediately
      await fetchSessionData(sid);

      // Try to activate the next goal if needed
      if (!result.activeGoals || result.activeGoals.length === 0) {
        console.log('No active goals after forcing progress, trying to activate next goal');
        await goalOrchestratorClient.activateNextGoal(sid);
        await fetchSessionData(sid);
      }

      setSnackbar({
        open: true,
        message: 'Session progress forced successfully',
        severity: 'success'
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error forcing progress:', errorMessage);
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error forcing progress: ${errorMessage}`,
        severity: 'error'
      });

      // Try to fetch the latest state anyway
      setTimeout(() => {
        fetchSessionData(sid);
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  // Load initial session data
  useEffect(() => {
    if (sessionId) {
      fetchSessionData(sessionId);

      // Set up polling for updates (decreased frequency from 5s to 20s)
      const intervalId = setInterval(() => {
        fetchSessionData(sessionId);
      }, 20000); // Poll every 20 seconds

      setRefreshInterval(intervalId);

      return () => {
        if (refreshInterval) {
          clearInterval(refreshInterval);
        }
      };
    }
  }, [sessionId, fetchSessionData]);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);

  // Start a new collaboration session
  const startNewSession = async (formData: any) => {
    setLoading(true);
    setError(null);

    try {
      const result = await goalOrchestratorClient.initiate(formData);
      setSessionId(result.sessionId);
      setActiveTab(1); // Switch to the overview tab

      setSnackbar({
        open: true,
        message: 'New goal-based collaboration session started successfully',
        severity: 'success'
      });

      // Fetch initial state
      await fetchSessionData(result.sessionId);

      // Set up polling for updates (decreased frequency from 5s to 20s)
      const intervalId = setInterval(() => {
        if (result.sessionId) {
          fetchSessionData(result.sessionId);
        }
      }, 20000); // Poll every 20 seconds

      if (refreshInterval) {
        clearInterval(refreshInterval);
      }

      setRefreshInterval(intervalId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Toggle drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Calculate progress
  const calculateProgress = (): number => {
    if (!collaborationState) return 0;

    // Check if we have the new state structure
    if (collaborationState.goals?.byId) {
      const totalGoals = Object.keys(collaborationState.goals.byId).length;
      if (totalGoals === 0) return 0;

      const completedGoals = collaborationState.goals.completedIds?.length || 0;
      return Math.round((completedGoals / totalGoals) * 100);
    }

    // Fall back to old state structure
    const totalGoals = Object.keys(collaborationState.goals || {}).length;
    if (totalGoals === 0) return 0;

    const completedGoals = collaborationState.completedGoalIds?.length || 0;
    return Math.round((completedGoals / totalGoals) * 100);
  };

  // Render content based on active tab
  const renderContent = () => {
    if (!sessionId && activeTab !== 0) {
      return (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No Active Session
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            Please start a new session to view this content.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setActiveTab(0)}
          >
            Start New Session
          </Button>
        </Paper>
      );
    }

    switch (activeTab) {
      case 0: // New Session
        return (
          <ArticleInitiationForm onSubmit={startNewSession} loading={loading} />
        );
      case 1: // Overview
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <GoalVisualization
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
                onRefresh={() => fetchSessionData(sessionId!)}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <WorkflowProgressVisualization
                sessionId={sessionId!}
                state={collaborationState}
                loading={loading}
                onRefresh={() => fetchSessionData(sessionId!)}
              />
              {/* Only show progress button if there are active goals or if workflow is not complete */}
              {collaborationState && (
                (collaborationState.goals?.activeIds?.length > 0 ||
                 (collaborationState.workflowProgress?.overallProgress < 100)) && (
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayArrowIcon />}
                      onClick={progressSession}
                      disabled={loading}
                    >
                      Progress Session
                    </Button>
                    <Button
                      variant="outlined"
                      color="secondary"
                      onClick={() => forceProgress(sessionId!)}
                      disabled={loading}
                    >
                      Force Progress
                    </Button>
                    {/* Only show the fixed processor button if there are issues with the regular progress */}
                    {collaborationState.errors && collaborationState.errors.length > 0 && (
                      <Button
                        variant="outlined"
                        color="warning"
                        onClick={processGoalsFixed}
                        disabled={loading}
                        size="small"
                      >
                        Use Fallback Processor
                      </Button>
                    )}
                  </Box>
                )
              )}
            </Grid>
          </Grid>
        );
      case 2: // Artifacts
        return (
          <ArtifactEvaluationDisplay
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
            onRefresh={() => fetchSessionData(sessionId!)}
          />
        );
      case 3: // Article Preview
        return (
          <ArticlePreview
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
          />
        );
      case 4: // Agent Collaboration Graph
        return (
          <AgentCollaborationGraphForGoals
            sessionId={sessionId!}
            state={collaborationState}
            loading={loading}
            onRefresh={() => fetchSessionData(sessionId!)}
          />
        );
      default:
        return (
          <Typography variant="body1">
            Unknown tab: {activeTab}
          </Typography>
        );
    }
  };

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={toggleDrawer}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Goal-Based Agent Collaboration
          </Typography>
          {sessionId && (
            <>
              <Chip
                label={`Session: ${sessionId.substring(0, 8)}...`}
                color="secondary"
                sx={{ mr: 1 }}
              />
              <Chip
                label={`Progress: ${calculateProgress()}%`}
                color="primary"
              />
              <IconButton color="inherit" onClick={() => fetchSessionData(sessionId)}>
                <RefreshIcon />
              </IconButton>
            </>
          )}
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Drawer
        variant={isMobile ? 'temporary' : 'persistent'}
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            <ListItem>
              <ListItemIcon>
                <ArticleIcon />
              </ListItemIcon>
              <ListItemText
                primary="New Session"
                onClick={() => setActiveTab(0)}
                sx={{ cursor: 'pointer' }}
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <TrackChangesIcon />
              </ListItemIcon>
              <ListItemText
                primary="Overview"
                onClick={sessionId ? () => setActiveTab(1) : undefined}
                sx={{
                  cursor: sessionId ? 'pointer' : 'default',
                  opacity: sessionId ? 1 : 0.5
                }}
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText
                primary="Artifacts"
                onClick={sessionId ? () => setActiveTab(2) : undefined}
                sx={{
                  cursor: sessionId ? 'pointer' : 'default',
                  opacity: sessionId ? 1 : 0.5
                }}
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <VisibilityIcon />
              </ListItemIcon>
              <ListItemText
                primary="Article Preview"
                onClick={sessionId ? () => setActiveTab(3) : undefined}
                sx={{
                  cursor: sessionId ? 'pointer' : 'default',
                  opacity: sessionId ? 1 : 0.5
                }}
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <GroupIcon />
              </ListItemIcon>
              <ListItemText
                primary="Collaboration Graph"
                onClick={sessionId ? () => setActiveTab(4) : undefined}
                sx={{
                  cursor: sessionId ? 'pointer' : 'default',
                  opacity: sessionId ? 1 : 0.5
                }}
              />
            </ListItem>
          </List>
        </Box>
      </Drawer>

      {/* Main content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3, mt: 8 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Status message */}
        {sessionId && (
          <StatusMessage
            state={collaborationState}
            loading={loading}
          />
        )}

        {/* Mobile tabs */}
        {isMobile && (
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ mb: 2 }}
          >
            <Tab icon={<ArticleIcon />} label="New" />
            <Tab icon={<TrackChangesIcon />} label="Overview" disabled={!sessionId} />
            <Tab icon={<FolderIcon />} label="Artifacts" disabled={!sessionId} />
            <Tab icon={<VisibilityIcon />} label="Preview" disabled={!sessionId} />
            <Tab icon={<GroupIcon />} label="Graph" disabled={!sessionId} />
          </Tabs>
        )}

        {renderContent()}
      </Box>
    </Box>
  );
};

export default GoalBasedDashboard;
