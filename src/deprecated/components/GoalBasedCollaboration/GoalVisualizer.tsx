// src/components/GoalOrchestrator/GoalVisualization.tsx

import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Chip, LinearProgress, Grid, Tooltip } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import BlockIcon from '@mui/icons-material/Block';
import { GoalStatus, GoalType } from '../../app/(payload)/api/agents/dynamic-collaboration-v3';

interface GoalVisualizationProps {
  sessionId: string;
  state: any;
  loading?: boolean;
  onRefresh?: () => void;
}

const GoalVisualization: React.FC<GoalVisualizationProps> = ({
  sessionId,
  state,
  loading = false,
  onRefresh
}) => {
  const [goals, setGoals] = useState<any[]>([]);
  const [hierarchicalGoals, setHierarchicalGoals] = useState<any>({});

  useEffect(() => {
    if (state && state.goals) {
      // Convert goals object to array
      const goalsArray = Object.values(state.goals);

      // Sort by creation date
      goalsArray.sort((a: any, b: any) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      setGoals(goalsArray);

      // Create hierarchical structure
      const highLevelGoals = goalsArray.filter((g: any) =>
        g.type === GoalType.RESEARCH ||
        g.type === GoalType.CONTENT ||
        g.type === GoalType.QUALITY
      );

      const hierarchy: any = {};

      highLevelGoals.forEach((highLevelGoal: any) => {
        hierarchy[highLevelGoal.id] = {
          goal: highLevelGoal,
          children: goalsArray.filter((g: any) =>
            g.dependencies.includes(highLevelGoal.id)
          )
        };
      });

      setHierarchicalGoals(hierarchy);
    }
  }, [state]);

  const getStatusColor = (status: GoalStatus) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return 'success';
      case GoalStatus.ACTIVE:
      case GoalStatus.IN_PROGRESS:
        return 'primary';
      case GoalStatus.PENDING:
        return 'default';
      case GoalStatus.BLOCKED:
      case GoalStatus.FAILED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: GoalStatus) => {
    switch (status) {
      case GoalStatus.COMPLETED:
        return <CheckCircleIcon fontSize="small" />;
      case GoalStatus.ACTIVE:
      case GoalStatus.IN_PROGRESS:
        return <PlayArrowIcon fontSize="small" />;
      case GoalStatus.PENDING:
        return <PendingIcon fontSize="small" />;
      case GoalStatus.BLOCKED:
      case GoalStatus.FAILED:
        return <BlockIcon fontSize="small" />;
      default:
        return null;
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>Goal Hierarchy</Typography>

      {loading ? (
        <LinearProgress variant="indeterminate" />
      ) : Object.keys(hierarchicalGoals).length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No goals defined yet
        </Typography>
      ) : (
        <Box>
          {Object.values(hierarchicalGoals).map((highLevelGoalData: any) => {
            const highLevelGoal = highLevelGoalData.goal;
            return (
              <Box key={highLevelGoal.id} sx={{ mb: 3 }}>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    mb: 1,
                    borderColor: getStatusColor(highLevelGoal.status) === 'default'
                      ? 'divider'
                      : `${getStatusColor(highLevelGoal.status)}.main`
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="subtitle1">
                      {highLevelGoal.description}
                    </Typography>
                    <Chip
                      label={highLevelGoal.status}
                      color={getStatusColor(highLevelGoal.status) as any}
                      size="small"
                      icon={getStatusIcon(highLevelGoal.status)}
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={highLevelGoal.progress}
                    sx={{ mt: 1 }}
                  />
                </Paper>

                <Box sx={{ pl: 4 }}>
                  <Grid container spacing={2}>
                    {highLevelGoalData.children.map((childGoal: any) => (
                      <Grid item xs={12} md={6} key={childGoal.id}>
                        <Paper
                          variant="outlined"
                          sx={{
                            p: 2,
                            borderColor: getStatusColor(childGoal.status) === 'default'
                              ? 'divider'
                              : `${getStatusColor(childGoal.status)}.main`
                          }}
                        >
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body1">
                              {childGoal.description}
                            </Typography>
                            <Chip
                              label={childGoal.status}
                              color={getStatusColor(childGoal.status) as any}
                              size="small"
                              icon={getStatusIcon(childGoal.status)}
                            />
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={childGoal.progress}
                            sx={{ mt: 1 }}
                          />

                          {childGoal.criteria && childGoal.criteria.length > 0 && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="caption" color="text.secondary">
                                Criteria:
                              </Typography>
                              <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                                {childGoal.criteria.map((criterion: string, index: number) => (
                                  <li key={index}>
                                    <Typography variant="caption">
                                      {criterion}
                                    </Typography>
                                  </li>
                                ))}
                              </ul>
                            </Box>
                          )}
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box>
            );
          })}
        </Box>
      )}
    </Paper>
  );
};

export default GoalVisualization;