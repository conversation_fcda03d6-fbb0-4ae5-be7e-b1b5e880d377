'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Tabs,
  Tab,
  Drawer,
  AppBar,
  Toolbar,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  Alert,
  Snackbar,
  Button,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { useRouter } from 'next/navigation';
import MenuIcon from '@mui/icons-material/Menu';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArticleIcon from '@mui/icons-material/Article';
import TrackChangesIcon from '@mui/icons-material/TrackChanges';
import GroupIcon from '@mui/icons-material/Group';
import FolderIcon from '@mui/icons-material/Folder';
import ChatIcon from '@mui/icons-material/Chat';
import PsychologyIcon from '@mui/icons-material/Psychology';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

// Components
import WorkflowProgressVisualization from './WorkflowProgressVisualization';
import ArtifactEvaluationDisplay from './ArtifactEvaluationDisplay';
import GoalVisualization from './GoalVisualization';
import ArticlePreview from './ArticlePreview';
import StatusMessage from './StatusMessage';
import AgentCollaborationGraphForGoals from './AgentCollaborationGraphForGoals';

// Client
import { goalOrchestratorClient } from '../../lib/goal-orchestrator-client';

interface GoalBasedDashboardSessionProps {
  initialSessionId: string;
}

const GoalBasedDashboardSession: React.FC<GoalBasedDashboardSessionProps> = ({
  initialSessionId
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();

  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [activeTab, setActiveTab] = useState(0); // Start with overview tab
  const [sessionId, setSessionId] = useState<string>(initialSessionId);
  const [collaborationState, setCollaborationState] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // Debug logging function
  const debugLog = (message: string, data?: any) => {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS
    console.log(`[${timestamp}] ${message}`, data || '');
  };

  // Process state to ensure consistent structure
  const processState = (state: any): any => {
    if (!state) {
      debugLog('State is null or undefined, returning default state structure');
      // Return a default state structure if state is null
      return {
        goals: { byId: {}, activeIds: [], completedIds: [] },
        workflowProgress: {
          currentPhase: 'PLANNING',
          phaseProgress: {
            PLANNING: 0,
            RESEARCH: 0,
            CREATION: 0,
            REVIEW: 0,
            FINALIZATION: 0
          },
          overallProgress: 0,
          milestones: []
        },
        artifacts: {}
      };
    }

    // Create a deep copy to avoid modifying the original state
    const processedState = JSON.parse(JSON.stringify(state));
    debugLog('Processing raw state:', processedState);

    // Ensure goals structure
    if (!processedState.goals) {
      debugLog('No goals found in state, creating empty goals structure');
      processedState.goals = { byId: {}, activeIds: [], completedIds: [] };
    } else if (!processedState.goals.byId) {
      debugLog('Converting goals to byId format');
      // Convert old format to new format if needed
      const byId: Record<string, any> = {};
      const activeIds: string[] = [];
      const completedIds: string[] = [];

      Object.entries(processedState.goals).forEach(([id, goal]: [string, any]) => {
        byId[id] = goal;
        if (goal.status === 'ACTIVE' || goal.status === 'IN_PROGRESS') {
          activeIds.push(id);
        } else if (goal.status === 'COMPLETED') {
          completedIds.push(id);
        }
      });

      processedState.goals = { byId, activeIds, completedIds };
    }

    // Ensure workflowProgress structure
    if (!processedState.workflowProgress) {
      debugLog('No workflowProgress found, creating default structure');
      processedState.workflowProgress = {
        currentPhase: 'PLANNING',
        phaseProgress: {
          PLANNING: 0,
          RESEARCH: 0,
          CREATION: 0,
          REVIEW: 0,
          FINALIZATION: 0
        },
        overallProgress: 0,
        milestones: []
      };
    }

    // Ensure artifacts structure
    if (!processedState.artifacts) {
      debugLog('No artifacts found, creating empty object');
      processedState.artifacts = {};
    }

    return processedState;
  };

  // Fetch session data
  const fetchSessionData = useCallback(async (sid: string) => {
    if (!sid) {
      debugLog('No session ID provided to fetchSessionData');
      return;
    }

    // Don't set loading to true here to avoid UI flicker during polling
    // Only set loading if this is a manual refresh
    const isManualRefresh = !refreshInterval;
    if (isManualRefresh) {
      setLoading(true);
    }
    setError(null);

    try {
      debugLog(`=== FETCHING SESSION DATA FOR ${sid} ===`);
      let rawState;

      // Try direct API call first for more reliable results
      try {
        debugLog('Making direct API call to get state');
        const response = await fetch(`/api/agents/dynamic-collaboration-v3/goal-based?sessionId=${sid}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
          // Add cache busting to prevent stale data
          cache: 'no-store'
        });

        if (!response.ok) {
          const errorText = await response.text();
          debugLog(`HTTP error ${response.status}: ${response.statusText}`, errorText);
          throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
        }

        const stateResponse = await response.json();
        debugLog('Direct API call response received');

        // Handle both response formats: { state: ... } or the state object directly
        rawState = stateResponse.state || stateResponse;
      } catch (directApiError) {
        debugLog('Direct API call failed, trying fallback methods', directApiError);

        // Try debug endpoint next
        try {
          debugLog('Attempting to get state from debug endpoint');
          const debugResult = await goalOrchestratorClient.getDebugState(sid);

          if (debugResult && debugResult.rawState) {
            debugLog('Successfully retrieved state from debug endpoint');
            rawState = debugResult.rawState;
          } else {
            // Fall back to regular state endpoint
            debugLog('Debug endpoint did not return state, falling back to regular endpoint');
            rawState = await goalOrchestratorClient.getState(sid);
          }
        } catch (stateError) {
          console.error('Error fetching state from all sources:', stateError);
          // If we can't get the state, use a default state
          rawState = null;
        }
      }

      // Process state to ensure consistent structure
      const state = processState(rawState);

      // Only update the state if it's different from the current state
      // This prevents unnecessary re-renders
      if (JSON.stringify(state) !== JSON.stringify(collaborationState)) {
        debugLog('State has changed, updating collaborationState');
        setCollaborationState(state);
      } else {
        debugLog('State has not changed, skipping update');
      }

      // Only show success message on manual refresh
      if (isManualRefresh) {
        setSnackbar({
          open: true,
          message: 'Session data refreshed',
          severity: 'success'
        });
      }

      debugLog('=== FETCH SESSION DATA COMPLETED ===');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error fetching session data:', errorMessage);
      setError(errorMessage);

      // Only show error message on manual refresh to avoid spamming the user
      if (isManualRefresh) {
        setSnackbar({
          open: true,
          message: `Error: ${errorMessage}`,
          severity: 'error'
        });
      }
    } finally {
      // Only update loading state if this was a manual refresh
      if (isManualRefresh) {
        setLoading(false);
      }
    }
  }, [refreshInterval, collaborationState]);

  // Progress the session
  const progressSession = useCallback(async () => {
    if (!sessionId) return;

    setLoading(true);
    setError(null);

    try {
      debugLog('Manually progressing session:', sessionId);

      // Make a direct API call to the progress endpoint
      const response = await fetch('/api/agents/dynamic-collaboration-v3/goal-based/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          steps: 1
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const progressResult = await response.json();
      debugLog('Progress result:', progressResult);

      // Update state if available in the response
      if (progressResult.state) {
        const updatedState = processState(progressResult.state);
        setCollaborationState(updatedState);
        debugLog('Updated state from progress response');
      } else {
        // Fetch updated state if not included in the response
        const updatedRawState = await goalOrchestratorClient.getState(sessionId);
        const updatedState = processState(updatedRawState);
        setCollaborationState(updatedState);
        debugLog('Fetched updated state after progress');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: 'Session progressed successfully',
        severity: 'success'
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error progressing session:', errorMessage);
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error progressing session: ${errorMessage}`,
        severity: 'error'
      });

      // Try to fetch the latest state anyway
      setTimeout(() => {
        fetchSessionData(sessionId);
      }, 1000);
    } finally {
      setLoading(false);
    }
  }, [sessionId, fetchSessionData]);

  // Fix goals for a session
  const fixGoals = async (sid: string) => {
    try {
      setLoading(true);

      const result = await goalOrchestratorClient.fixGoals(sid);

      setSnackbar({
        open: true,
        message: 'Goals fixed successfully',
        severity: 'success'
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error fixing goals:', errorMessage);

      setSnackbar({
        open: true,
        message: `Error fixing goals: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Force progress using the debug endpoint
  const forceProgress = async (sid: string) => {
    if (!sid) return;

    setLoading(true);
    setError(null);

    try {
      console.log('Forcing progress for session:', sid);

      // Use the client method to force progress
      const result = await goalOrchestratorClient.forceProgress(sid);
      console.log('Force progress result:', result);

      // Fetch updated state immediately
      await fetchSessionData(sid);

      setSnackbar({
        open: true,
        message: 'Session progress forced successfully',
        severity: 'success'
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('Error forcing progress:', errorMessage);
      setError(errorMessage);

      setSnackbar({
        open: true,
        message: `Error forcing progress: ${errorMessage}`,
        severity: 'error'
      });

      // Try to fetch the latest state anyway
      setTimeout(() => {
        fetchSessionData(sid);
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  // Initial setup
  useEffect(() => {
    if (initialSessionId) {
      debugLog(`Using initial session ID: ${initialSessionId}`);
      // Ensure the session ID is set in state
      setSessionId(initialSessionId);
    }
  }, [initialSessionId]);

  // Load initial session data and set up auto-progression
  useEffect(() => {
    debugLog(`useEffect triggered for sessionId: ${sessionId || 'none'}`);

    // Clear any existing interval first to avoid multiple intervals
    if (refreshInterval) {
      debugLog('Clearing existing refresh interval');
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }

    if (sessionId) {
      debugLog(`Setting up data fetching for session: ${sessionId}`);

      // Fetch initial data immediately
      fetchSessionData(sessionId);

      // Set up polling for updates
      debugLog(`Setting up polling interval for session: ${sessionId}`);
      const intervalId = setInterval(() => {
        debugLog(`Polling interval triggered for session: ${sessionId}`);
        fetchSessionData(sessionId);
      }, 20000); // Poll every 20 seconds

      setRefreshInterval(intervalId);

      // Clean up on unmount or when sessionId changes
      return () => {
        debugLog(`Cleaning up for session: ${sessionId}`);
        clearInterval(intervalId);
      };
    } else {
      debugLog('No sessionId, skipping setup');
    }
  }, [sessionId, fetchSessionData]); // Only depend on sessionId to avoid re-creating the interval too often

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Toggle drawer
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Render content based on active tab
  const renderContent = () => {
    debugLog(`Rendering content for tab ${activeTab}, sessionId: ${sessionId || 'none'}, loading: ${loading}`);

    // If we have a sessionId but the state is still loading or empty
    if (!collaborationState || Object.keys(collaborationState).length === 0) {
      return (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Loading Session Data
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Please wait while we load the session data...
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', alignItems: 'center', mt: 3 }}>
            <CircularProgress size={40} sx={{ mb: 2 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Session ID: {sessionId.substring(0, 8)}...
              </Typography>
              <Button
                variant="text"
                size="small"
                onClick={() => {
                  navigator.clipboard.writeText(sessionId);
                  debugLog(`Copied session ID to clipboard: ${sessionId}`);
                  setSnackbar({
                    open: true,
                    message: 'Session ID copied to clipboard',
                    severity: 'success'
                  });
                }}
              >
                Copy ID
              </Button>
            </Box>
            <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => fetchSessionData(sessionId)}
              >
                Refresh Status
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => forceProgress(sessionId)}
              >
                Force Progress
              </Button>
            </Box>
          </Box>
        </Paper>
      );
    }

    switch (activeTab) {
      case 0: // Overview
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <GoalVisualization
                sessionId={sessionId}
                state={collaborationState}
                loading={loading}
                onRefresh={() => fetchSessionData(sessionId)}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <WorkflowProgressVisualization
                sessionId={sessionId}
                state={collaborationState}
                loading={loading}
                onRefresh={() => fetchSessionData(sessionId)}
              />
              {/* Only show progress button if there are active goals or if workflow is not complete */}
              {collaborationState && (
                (collaborationState.goals?.activeIds?.length > 0 ||
                 (collaborationState.workflowProgress?.overallProgress < 100)) && (
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayArrowIcon />}
                      onClick={progressSession}
                      disabled={loading}
                    >
                      Progress Session
                    </Button>
                    <Button
                      variant="outlined"
                      color="secondary"
                      onClick={() => forceProgress(sessionId)}
                      disabled={loading}
                    >
                      Force Progress
                    </Button>
                  </Box>
                )
              )}
            </Grid>
          </Grid>
        );
      case 1: // Artifacts
        return (
          <ArtifactEvaluationDisplay
            sessionId={sessionId}
            state={collaborationState}
            loading={loading}
            onRefresh={() => fetchSessionData(sessionId)}
          />
        );
      case 2: // Article Preview
        return (
          <ArticlePreview
            sessionId={sessionId}
            state={collaborationState}
            loading={loading}
          />
        );
      case 3: // Agent Collaboration Graph
        return (
          <AgentCollaborationGraphForGoals
            sessionId={sessionId}
            state={collaborationState}
            loading={loading}
            onRefresh={() => fetchSessionData(sessionId)}
          />
        );
      default:
        return (
          <Typography variant="body1">
            Unknown tab: {activeTab}
          </Typography>
        );
    }
  };

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={toggleDrawer}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Goal-Based Agent Collaboration
          </Typography>
          {sessionId && (
            <>
              <Chip
                label={`Session: ${sessionId.substring(0, 8)}...`}
                color="secondary"
                size="small"
                sx={{ mr: 1 }}
              />
              <IconButton
                color="inherit"
                onClick={() => fetchSessionData(sessionId)}
                disabled={loading}
              >
                <RefreshIcon />
              </IconButton>
            </>
          )}
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Drawer
        variant={isMobile ? 'temporary' : 'permanent'}
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          [`& .MuiDrawer-paper`]: { width: drawerWidth, boxSizing: 'border-box' },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            <ListItem button onClick={() => setActiveTab(0)} selected={activeTab === 0}>
              <ListItemIcon>
                <TrackChangesIcon />
              </ListItemIcon>
              <ListItemText primary="Overview" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(1)} selected={activeTab === 1}>
              <ListItemIcon>
                <FolderIcon />
              </ListItemIcon>
              <ListItemText primary="Artifacts" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(2)} selected={activeTab === 2}>
              <ListItemIcon>
                <ArticleIcon />
              </ListItemIcon>
              <ListItemText primary="Article Preview" />
            </ListItem>
            <ListItem button onClick={() => setActiveTab(3)} selected={activeTab === 3}>
              <ListItemIcon>
                <GroupIcon />
              </ListItemIcon>
              <ListItemText primary="Agent Collaboration" />
            </ListItem>
          </List>
          <Divider />
          <List>
            <ListItem>
              <ListItemIcon>
                <InfoIcon />
              </ListItemIcon>
              <ListItemText 
                primary="Session Status" 
                secondary={collaborationState?.workflowProgress?.currentPhase || 'Loading...'}
              />
            </ListItem>
          </List>
        </Box>
      </Drawer>

      {/* Main content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3, mt: 8 }}>
        {/* Tabs for mobile view */}
        {isMobile && (
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ mb: 2 }}
          >
            <Tab label="Overview" icon={<TrackChangesIcon />} />
            <Tab label="Artifacts" icon={<FolderIcon />} />
            <Tab label="Article" icon={<ArticleIcon />} />
            <Tab label="Agents" icon={<GroupIcon />} />
          </Tabs>
        )}

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Loading indicator */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <CircularProgress size={24} />
          </Box>
        )}

        {/* Main content based on active tab */}
        {renderContent()}
      </Box>
    </Box>
  );
};

export default GoalBasedDashboardSession;
