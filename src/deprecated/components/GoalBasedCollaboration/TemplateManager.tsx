'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import VisibilityIcon from '@mui/icons-material/Visibility';
import StarIcon from '@mui/icons-material/Star';
import StarBorderIcon from '@mui/icons-material/StarBorder';

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'Easy' | 'Medium' | 'Advanced';
  estimatedTime: string;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount: number;
}

interface TemplateManagerProps {
  templates?: Template[];
  onTemplateCreate?: (template: Omit<Template, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>) => void;
  onTemplateUpdate?: (id: string, template: Partial<Template>) => void;
  onTemplateDelete?: (id: string) => void;
}

const SAMPLE_TEMPLATES: Template[] = [
  {
    id: 'blog-article',
    name: 'SEO Blog Article',
    description: 'Comprehensive SEO-optimized blog posts with keyword research and optimization',
    category: 'Blog',
    difficulty: 'Medium',
    estimatedTime: '20-30 minutes',
    featured: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    usageCount: 45
  },
  {
    id: 'how-to-guide',
    name: 'How-To Guide',
    description: 'Step-by-step instructional content with clear actionable steps and troubleshooting',
    category: 'Educational',
    difficulty: 'Medium',
    estimatedTime: '25-35 minutes',
    featured: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    usageCount: 32
  },
  {
    id: 'case-study',
    name: 'Case Study',
    description: 'In-depth case studies showcasing real-world results and success stories',
    category: 'Business',
    difficulty: 'Advanced',
    estimatedTime: '30-45 minutes',
    featured: true,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    usageCount: 18
  }
];

const TemplateManager: React.FC<TemplateManagerProps> = ({
  templates = SAMPLE_TEMPLATES,
  onTemplateCreate,
  onTemplateUpdate,
  onTemplateDelete
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const [editForm, setEditForm] = useState({
    name: '',
    description: '',
    category: '',
    difficulty: 'Medium' as Template['difficulty'],
    estimatedTime: '',
    featured: false
  });

  const categories = Array.from(new Set(templates.map(t => t.category)));
  const featuredTemplates = templates.filter(t => t.featured);
  const allTemplates = templates;

  const handleEdit = (template: Template) => {
    setSelectedTemplate(template);
    setEditForm({
      name: template.name,
      description: template.description,
      category: template.category,
      difficulty: template.difficulty,
      estimatedTime: template.estimatedTime,
      featured: template.featured
    });
    setIsCreating(false);
    setEditDialogOpen(true);
  };

  const handleCreate = () => {
    setSelectedTemplate(null);
    setEditForm({
      name: '',
      description: '',
      category: '',
      difficulty: 'Medium',
      estimatedTime: '',
      featured: false
    });
    setIsCreating(true);
    setEditDialogOpen(true);
  };

  const handleSave = () => {
    if (isCreating && onTemplateCreate) {
      onTemplateCreate(editForm);
    } else if (selectedTemplate && onTemplateUpdate) {
      onTemplateUpdate(selectedTemplate.id, editForm);
    }
    setEditDialogOpen(false);
  };

  const handleDelete = () => {
    if (selectedTemplate && onTemplateDelete) {
      onTemplateDelete(selectedTemplate.id);
    }
    setDeleteDialogOpen(false);
  };

  const handleToggleFeatured = (template: Template) => {
    if (onTemplateUpdate) {
      onTemplateUpdate(template.id, { featured: !template.featured });
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'success';
      case 'Medium': return 'warning';
      case 'Advanced': return 'error';
      default: return 'default';
    }
  };

  const renderTemplateCard = (template: Template) => (
    <Card key={template.id} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Typography variant="h6" component="h3">
            {template.name}
          </Typography>
          <IconButton
            size="small"
            onClick={() => handleToggleFeatured(template)}
            color={template.featured ? 'primary' : 'default'}
          >
            {template.featured ? <StarIcon /> : <StarBorderIcon />}
          </IconButton>
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {template.description}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          <Chip label={template.category} size="small" variant="outlined" />
          <Chip 
            label={template.difficulty} 
            size="small" 
            color={getDifficultyColor(template.difficulty) as any}
          />
          <Chip label={template.estimatedTime} size="small" variant="outlined" />
        </Box>

        <Typography variant="caption" color="text.secondary">
          Used {template.usageCount} times
        </Typography>
      </CardContent>
      
      <CardActions>
        <Tooltip title="View Details">
          <IconButton 
            size="small" 
            onClick={() => {
              setSelectedTemplate(template);
              setViewDialogOpen(true);
            }}
          >
            <VisibilityIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Edit Template">
          <IconButton size="small" onClick={() => handleEdit(template)}>
            <EditIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Delete Template">
          <IconButton 
            size="small" 
            onClick={() => {
              setSelectedTemplate(template);
              setDeleteDialogOpen(true);
            }}
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      </CardActions>
    </Card>
  );

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Template Manager
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreate}
        >
          Create Template
        </Button>
      </Box>

      <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label={`All Templates (${allTemplates.length})`} />
        <Tab label={`Featured (${featuredTemplates.length})`} />
      </Tabs>

      <Grid container spacing={2}>
        {(activeTab === 0 ? allTemplates : featuredTemplates).map(template => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            {renderTemplateCard(template)}
          </Grid>
        ))}
      </Grid>

      {/* Edit/Create Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {isCreating ? 'Create New Template' : 'Edit Template'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Template Name"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={editForm.description}
                onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Category"
                value={editForm.category}
                onChange={(e) => setEditForm({ ...editForm, category: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Difficulty</InputLabel>
                <Select
                  value={editForm.difficulty}
                  label="Difficulty"
                  onChange={(e) => setEditForm({ ...editForm, difficulty: e.target.value as Template['difficulty'] })}
                >
                  <MenuItem value="Easy">Easy</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="Advanced">Advanced</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Estimated Time"
                value={editForm.estimatedTime}
                onChange={(e) => setEditForm({ ...editForm, estimatedTime: e.target.value })}
                placeholder="e.g., 20-30 minutes"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleSave}>
            {isCreating ? 'Create' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Template Details</DialogTitle>
        <DialogContent>
          {selectedTemplate && (
            <Box>
              <Typography variant="h6" gutterBottom>{selectedTemplate.name}</Typography>
              <Typography variant="body2" paragraph>{selectedTemplate.description}</Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Category</Typography>
                  <Typography variant="body2">{selectedTemplate.category}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Difficulty</Typography>
                  <Typography variant="body2">{selectedTemplate.difficulty}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Estimated Time</Typography>
                  <Typography variant="body2">{selectedTemplate.estimatedTime}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Usage Count</Typography>
                  <Typography variant="body2">{selectedTemplate.usageCount} times</Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Template</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            This action cannot be undone.
          </Alert>
          <Typography>
            Are you sure you want to delete the template "{selectedTemplate?.name}"?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" color="error" onClick={handleDelete}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default TemplateManager;
