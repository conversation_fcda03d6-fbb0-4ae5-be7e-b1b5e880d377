'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionD<PERSON>ils,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CodeIcon from '@mui/icons-material/Code';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import ReactMarkdown from 'react-markdown';

interface ArtifactContentDisplayProps {
  artifact: any;
}

/**
 * Component for displaying artifact content in various formats
 */
const ArtifactContentDisplay: React.FC<ArtifactContentDisplayProps> = ({
  artifact
}) => {
  const [expanded, setExpanded] = useState<boolean>(true);
  const [showRaw, setShowRaw] = useState<boolean>(false);

  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  // Toggle raw view
  const toggleRawView = () => {
    setShowRaw(!showRaw);
  };

  // Copy content to clipboard
  const copyToClipboard = () => {
    let contentToCopy = '';
    
    if (typeof artifact.content === 'string') {
      contentToCopy = artifact.content;
    } else if (typeof artifact.content === 'object') {
      contentToCopy = JSON.stringify(artifact.content, null, 2);
    } else {
      contentToCopy = String(artifact.content);
    }
    
    navigator.clipboard.writeText(contentToCopy).then(() => {
      alert('Content copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy content:', err);
      alert('Failed to copy content');
    });
  };

  // Render content based on type
  const renderContent = () => {
    if (!artifact || artifact.content === undefined || artifact.content === null) {
      return (
        <Typography variant="body2" color="text.secondary">
          No content available
        </Typography>
      );
    }

    // If showing raw content
    if (showRaw) {
      let rawContent = '';
      
      if (typeof artifact.content === 'string') {
        rawContent = artifact.content;
      } else if (typeof artifact.content === 'object') {
        rawContent = JSON.stringify(artifact.content, null, 2);
      } else {
        rawContent = String(artifact.content);
      }
      
      return (
        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default', overflow: 'auto', maxHeight: '500px' }}>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
            {rawContent}
          </pre>
        </Paper>
      );
    }

    // If content is a string, try to render as markdown
    if (typeof artifact.content === 'string') {
      try {
        // Check if it's JSON
        const parsedContent = JSON.parse(artifact.content);
        return renderObjectContent(parsedContent);
      } catch (e) {
        // Not JSON, render as markdown
        return (
          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default', overflow: 'auto', maxHeight: '500px' }}>
            <ReactMarkdown>
              {artifact.content}
            </ReactMarkdown>
          </Paper>
        );
      }
    }

    // If content is an object
    if (typeof artifact.content === 'object' && artifact.content !== null) {
      return renderObjectContent(artifact.content);
    }

    // Fallback for other types
    return (
      <Typography variant="body2">
        {String(artifact.content)}
      </Typography>
    );
  };

  // Render object content
  const renderObjectContent = (content: any) => {
    // Special handling for common content structures
    if (content.text || content.content || content.body) {
      const textContent = content.text || content.content || content.body;
      return (
        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.default', overflow: 'auto', maxHeight: '500px' }}>
          <ReactMarkdown>
            {textContent}
          </ReactMarkdown>
        </Paper>
      );
    }

    // Render as expandable sections
    return (
      <Box>
        {Object.entries(content).map(([key, value]: [string, any]) => (
          <Accordion key={key} defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2">{key}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {typeof value === 'object' && value !== null ? (
                <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                  {JSON.stringify(value, null, 2)}
                </pre>
              ) : (
                <Typography variant="body2">{String(value)}</Typography>
              )}
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };

  return (
    <Box>
      <Accordion expanded={expanded} onChange={toggleExpanded}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
              {artifact.title || 'Artifact Content'}
            </Typography>
            <Chip
              icon={typeof artifact.content === 'string' ? <TextSnippetIcon /> : <CodeIcon />}
              label={typeof artifact.content === 'string' ? 'Text' : 'Object'}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ mr: 1 }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button
              size="small"
              variant="outlined"
              startIcon={showRaw ? <TextSnippetIcon /> : <CodeIcon />}
              onClick={toggleRawView}
            >
              {showRaw ? 'Formatted View' : 'Raw View'}
            </Button>
            <Button
              size="small"
              variant="outlined"
              startIcon={<ContentCopyIcon />}
              onClick={copyToClipboard}
            >
              Copy
            </Button>
          </Box>
          <Divider sx={{ mb: 2 }} />
          {renderContent()}
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default ArtifactContentDisplay;
