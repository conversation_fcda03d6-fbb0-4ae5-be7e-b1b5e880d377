# New Simplified Content Generation System

## Overview

This is a new, simplified content generation system built according to the critical architecture changes and comprehensive plan. It focuses on **basic working functionality first** before adding complex UI components.

## Architecture

### Core Components

1. **AI Model Manager** (`src/core/ai/`)
   - Multi-provider support (OpenAI, Anthropic)
   - BYOK (Bring Your Own Key) functionality
   - Cost tracking and usage analytics
   - Model selection based on task requirements

2. **Workflow Engine** (`src/core/workflow/`)
   - Simple text-based workflow definitions
   - Step-by-step execution with dependency management
   - Built-in templates for common use cases
   - Human review integration

3. **Simplified Review System** (`src/core/review/`)
   - Basic approve/reject functionality
   - Email notifications (placeholder)
   - Simple web interface for reviews

4. **Flat State Management** (`src/core/state/`)
   - Simplified state structure (no complex nesting)
   - Essential events only (6 types instead of 20+)
   - Memory storage for development, Redis-ready for production

### Essential Templates

1. **SEO Blog Post** - Complete blog post generation with keyword research and human review
2. **Bulk Product Descriptions** - Generate product descriptions from CSV data
3. **Content Refresh** - Analyze and update existing content

## Getting Started

### Prerequisites

1. Node.js 18+ 
2. Yarn package manager
3. OpenAI API key (optional - for testing)

### Installation

Dependencies are already installed. The system uses:
- `@anthropic-ai/sdk` for Claude integration
- `openai` for OpenAI integration
- `uuid` for ID generation

### Running the System

1. Start the development server:
   ```bash
   yarn dev
   ```

2. Open the workflow interface:
   ```
   http://localhost:3000/workflow
   ```

### Testing the System

1. **Basic Workflow Test:**
   - Go to `/workflow`
   - Select "SEO Blog Post" template
   - Fill in the sample inputs
   - Optionally add your OpenAI API key for BYOK
   - Click "Execute Workflow"

2. **Review System Test:**
   - When a workflow reaches a human review step, it will create a review
   - The review URL will be displayed in the execution status
   - Visit the review URL to approve/reject content

## API Endpoints

### Workflow API

- `POST /api/workflow/create` - Create and execute workflow
- `GET /api/workflow/create?executionId=<id>` - Get execution status
- `GET /api/workflow/create` - List available templates

### Review API

- `GET /api/review/[id]` - Get review data
- `POST /api/review/[id]` - Submit review decision

## Key Features Implemented

### ✅ Phase 1 Complete (Basic Working System)

1. **AI Model Manager**
   - Multi-provider support (OpenAI, Anthropic)
   - BYOK functionality
   - Cost estimation and tracking

2. **Simple Workflow Engine**
   - Text-based workflow definitions
   - Step execution with dependencies
   - Template system

3. **Basic Review System**
   - Simple approve/reject interface
   - Web-based review pages

4. **Essential Templates**
   - SEO Blog Post workflow
   - Bulk Product Descriptions workflow
   - Content Refresh workflow

5. **Simplified State Management**
   - Flat state structure
   - Essential events only
   - Memory storage (Redis-ready)

### 🚧 Next Steps (Phase 2)

1. **Enhanced Features**
   - CSV import/export functionality
   - CMS integrations (WordPress, Shopify)
   - Knowledge base system
   - Progress tracking improvements

2. **Visual Interface (Phase 3)**
   - React Flow integration
   - Drag-and-drop workflow builder
   - Advanced templates

## Architecture Decisions

Following the critical architecture changes checklist:

1. **Workflow-First Design** ✅
   - Users see workflows, system uses goals internally
   - Template-driven development

2. **Simplified Human Interaction** ✅
   - Basic approve/reject with edits
   - Removed complex escalation management

3. **AI Model Manager** ✅
   - Transformed from agent system
   - BYOK support
   - Multi-provider integration

4. **Flat State Structure** ✅
   - Simplified from complex nested state
   - Essential events only

5. **Template-Driven Development** ✅
   - 3 essential templates implemented
   - Easy to add more templates

## File Structure

```
src/
├── core/
│   ├── ai/                    # AI Model Manager
│   │   ├── types.ts
│   │   ├── model-manager.ts
│   │   └── providers/
│   ├── workflow/              # Workflow Engine
│   │   ├── types.ts
│   │   ├── engine.ts
│   │   └── templates.ts
│   ├── review/                # Review System
│   │   ├── types.ts
│   │   └── system.ts
│   └── state/                 # State Management
│       ├── types.ts
│       └── store.ts
├── api/
│   ├── workflow/create/route.ts
│   └── review/[id]/route.ts
└── app/
    ├── (payload)/
    │   └── api/
    │       ├── workflow/
    │       │   ├── templates/route.ts
    │       │   ├── create/route.ts
    │       │   ├── execution/[id]/route.ts
    │       │   └── results/[id]/route.ts
    │       ├── review/[id]/route.ts
    │       └── cms/publish/route.ts
    └── workflow/
        ├── layout.tsx
        ├── page.tsx
        ├── unified/page.tsx
        └── results/[id]/page.tsx
```

## Environment Variables

```bash
# Optional - for system default AI provider
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
```

## Testing

The system is designed for immediate testing:

1. **No Complex Setup** - Works with memory storage
2. **BYOK Support** - Users can provide their own API keys
3. **Simple UI** - Basic forms and status displays
4. **Real AI Integration** - Actually calls OpenAI/Anthropic APIs

## Production Readiness

To make this production-ready:

1. **Replace Memory Storage** with Redis/PostgreSQL
2. **Add Authentication** for user management
3. **Implement Email Notifications** for reviews
4. **Add Error Handling** and retry mechanisms
5. **Scale Infrastructure** for concurrent workflows

## Success Metrics

- ✅ Basic workflow executes end-to-end
- ✅ AI integration works with BYOK
- ✅ Human review system functional
- ✅ Templates load and execute
- ✅ Simple UI for testing

This system provides a solid foundation that can be enhanced with the sophisticated features from the comprehensive plan while maintaining the practical, market-ready approach from the critical architecture changes.
